[tool.poetry]
name = "cortaix-factory-mlops-mlopscli"
version = "1.1.1"
description = ""
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [
    { include = "mlopscli" }
]
[tool.poetry.dependencies]
python = "^3.10"
click = {version = "^8.0.1"}
click-config-file = {version = "^0.6.0"}
dependency-injector = {version = "^4.41.0"}
psycopg = "^3.2.4"
minio = "^7.2.11"
kubernetes = "^31.0.0"
psutil = "^6.1.1"
asyncio = "^3.4.3"
playwright = "^1.52.0"
lakefs-client = "^1.44.0"
keyring = "^25.6.0"

[tool.poetry.group.dev.dependencies]
ruff = "^0.8.6"
pre-commit = "^4.0.1"
pytest = "^6.2.1"
pytest-bdd = "^4.0.2"
pytest-cov = "^2.10.1"
pytest-mock = "^3.4.0"
pytest-runner = "^5.2"
pytest-watch = "^4.2.0"
pylint = "^2.17.3"
pylint-exit = "^1.2.0"
sphinx = "6.2.1"
sphinx-click = "^4.4.0"
sphinx-rtd-theme = "^1.2.0"
# Issue with transitive dependencies: client using lib doesn't get dependecies
distlib = "0.3.9" # pre-commit -> virtualenv
filelock = "3.16.1" # pre-commit -> virtualenv
platformdirs = "4.3.6" # pre-commit -> virtualenv

# forced dependencies due to vulnerabilities
urllib3 = ">=2.5.0"
requests = ">=2.32.4"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[[tool.poetry.source]]
name = "dfartifactory"
url = "https://artifactory.thalesdigital.io/artifactory/api/pypi/pypi/simple"

[tool.poetry.scripts]
mlopscli = "mlopscli.cli.cli:start"
