# Modular job definitions for main pipeline
.templates_version: &templates_version 1.1.0
include:
  # Common to all templates
  - project: 'cortaix-factory-mlops/internal/gitlab-ci-templates'
    file: 'jobs/common.yml'
    ref: *templates_version
  # Pre test
  - project: 'cortaix-factory-mlops/internal/gitlab-ci-templates'
    file: 'jobs/pre/poetry-pre-commit.yml'
    ref: *templates_version
  # Test
  - project: 'cortaix-factory-mlops/internal/gitlab-ci-templates'
    file: 'jobs/test/test-python.yml'
    ref: *templates_version
  # Sonar job
  - project: 'cortaix-factory-mlops/internal/gitlab-ci-templates'
    file: 'jobs/quality/sonar.yml'
    ref: *templates_version
  # build job
  - project: 'cortaix-factory-mlops/internal/gitlab-ci-templates'
    file: 'jobs/build/build-python.yml'
    ref: *templates_version
  # SAST jobs
  - project: 'cortaix-factory-mlops/internal/gitlab-ci-templates'
    file: 'jobs/security/sast.yml'
    ref: *templates_version
  # SCA jobs
  - project: 'cortaix-factory-mlops/internal/gitlab-ci-templates'
    file: 'jobs/security/dependency-scanning.yml'
    ref: *templates_version
  # Secret scanning
  - project: 'cortaix-factory-mlops/internal/gitlab-ci-templates'
    file: 'jobs/security/secret-detection.yml'
    ref: *templates_version
  # Rules for Security jobs
  - project: 'cortaix-factory-mlops/internal/gitlab-ci-templates'
    file: 'jobs/security/security-rules.yml'
    ref: *templates_version
  # Pages steps
  - project: 'cortaix-factory-mlops/internal/gitlab-ci-templates'
    file: 'jobs/pages/pages.yml'
    ref: *templates_version

variables:
  # Enable/disable certain steps for speed of development
  EnableSAST: "true"
  EnableSCA: "true"
  EnableSecret: "true"
  EnableSonar: "true"
  # Common security scans variables
  SECURE_LOG_LEVEL: "info"
  # SAST variables
  SAST_EXCLUDED_PATHS: "vendor, third_party, node_modules, test_data, testdata"
  # SCA parameters
  DS_EXCLUDED_PATHS: "vendor, third_party, node_modules, test_data, testdata"
  DS_DEFAULT_ANALYZERS: "gemnasium, retire.js, gemnasium-maven, gemnasium-python"
  DS_MAX_DEPTH: 10
  # Sonar Configuration
  # Note: SONAR_TOKEN should be set as a masked CI/CD variable in GitLab
  SONAR_URL: "https://quality-analysis.thalesdigital.io"
  AST_ENABLE_MR_PIPELINES: "true"

stages:
  - code-quality
  - sonar
  - test
  - build
  - security
  - publish-report
