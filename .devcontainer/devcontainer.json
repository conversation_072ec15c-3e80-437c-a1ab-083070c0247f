{"name": "m<PERSON><PERSON>li", "runArgs": ["--privileged"], "build": {"dockerfile": "Dockerfile", "args": {"VARIANT": "bullseye"}}, "containerEnv": {"NODE_EXTRA_CA_CERTS": "/usr/local/share/ca-certificates/ZscalerRootCertificate-2048-SHA256.crt", "POETRY_VIRTUALENVS_IN_PROJECT": "1"}, "mounts": ["source=${localEnv:HOME}/.ssh,target=/home/<USER>/.ssh,type=bind,consistency=cached", "source=${localEnv:HOME}/.pip,target=/home/<USER>/.pip,type=bind,consistency=cached", "source=${localEnv:HOME}/.netrc,target=/home/<USER>/.netrc,type=bind,consistency=cached", "source=${localEnv:HOME}/.gitconfig,target=/home/<USER>/.gitconfig,type=bind,consistency=cached", "source=${localEnv:HOME}/.config/pypoetry/,target=/home/<USER>/.config/pypoetry/,type=bind,consistency=cached"], "postStartCommand": ".devcontainer/postCreateScript.sh", "remoteUser": "vscode", "customizations": {"vscode": {"extensions": ["aaron-bond.better-comments", "alexkrechik.cucumberautocomplete", "christian-kohler.path-intellisense", "codeavecjonathan.importmagic", "eamodio.gitlens", "esbenp.prettier-vscode", "gruntfuggly.todo-tree", "ms-azuretools.vscode-docker", "ms-python.debugpy", "ms-python.python", "ms-python.vscode-pylance", "njpwerner.autodocstring", "omagerio.tabsort", "redhat.vscode-yaml", "shd101wyy.markdown-preview-enhanced", "timonwong.shellcheck", "tyriar.sort-lines", "vscode-icons-team.vscode-icons", "yzhang.markdown-all-in-one", "charliermarsh.ruff"], "settings": {"terminal.integrated.shell.linux": "/bin/bash", "python.pythonPath": "/usr/local/bin/python", "python.linting.pylintEnabled": true, "python.linting.pylintPath": "/usr/local/bin/pylint", "python.linting.enabled": true, "python.terminal.activateEnvInCurrentTerminal": true, "python.terminal.activateEnvironment": true, "python.defaultInterpreterPath": ".venv/bin/python"}, "features": {"ghcr.io/jsburckhardt/devcontainer-features/ruff:1": {}}}}}