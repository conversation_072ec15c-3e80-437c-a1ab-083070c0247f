import base64
import subprocess
from unittest.mock import MagicMock
from unittest.mock import patch

from kubernetes import client
import pytest
from pytest_bdd import given
from pytest_bdd import parsers
from pytest_bdd import scenarios
from pytest_bdd import then
from pytest_bdd import when

from mlopscli.utils.kubernetes import check_image_pull_secret
from mlopscli.utils.kubernetes import create_docker_registry_secret
from mlopscli.utils.kubernetes import create_pod
from mlopscli.utils.kubernetes import create_secret
from mlopscli.utils.kubernetes import delete_pod
from mlopscli.utils.kubernetes import force_download_image_on_host
from mlopscli.utils.kubernetes import is_helm_chart_installed
from mlopscli.utils.kubernetes import is_helm_repo_installed
from mlopscli.utils.kubernetes import restart_deployment
from mlopscli.utils.kubernetes import uninstall_helm_repo
from mlopscli.utils.kubernetes import wait_for_pod_to_run

scenarios("mlopscli/utils/kubernetes.feature")


PATCH_IS_COMPONENT_INSTALLED = "mlopscli.utils.kubernetes.is_component_installed"
SUBPROCESS_RUN_STR = "subprocess.run"
RESULT_KEY = "result"
REPOS_KEY = "repos"
COMMAND_KEY = "command"


@pytest.fixture
def context():
    return {}


@pytest.fixture
def mock_load_kube_config():
    with patch("kubernetes.config.load_kube_config"):
        yield


@given(parsers.parse('a deployment named "{deployment_name}" in namespace "{namespace}"'), target_fixture="deployment")
def deployment(deployment_name, namespace):
    return deployment_name, namespace


@when("the restart_deployment function is called")
def call_restart_deployment(deployment, mock_load_kube_config, mock_patch_namespaced_deployment):
    deployment_name, namespace = deployment
    restart_deployment(deployment_name, namespace)


@then("the deployment is restarted successfully")
def verify_deployment_restarted_successfully(mock_patch_namespaced_deployment):
    assert mock_patch_namespaced_deployment.called


@when("the restart_deployment function is called with a nonexistent deployment")
def call_restart_with_nonexistent_deployment(deployment, mock_load_kube_config, mock_patch_namespaced_deployment):
    deployment_name, namespace = deployment
    mock_patch_namespaced_deployment.side_effect = client.exceptions.ApiException(status=404)
    restart_deployment(deployment_name, namespace)


@when("the restart_deployment function is called with a exsting deployment but error occured")
def call_restart_with_existing_deployment_but_error_occured(deployment, mock_load_kube_config, mock_patch_namespaced_deployment):
    deployment_name, namespace = deployment
    mock_patch_namespaced_deployment.side_effect = client.exceptions.ApiException(status=403)
    restart_deployment(deployment_name, namespace)


@pytest.fixture
def mock_apps_v1():
    with patch("mlopscli.utils.kubernetes.client.AppsV1Api") as mock_api:
        yield mock_api


@pytest.fixture
def mock_patch_namespaced_deployment(mock_apps_v1):
    mock_patch = MagicMock()
    mock_apps_v1.return_value.patch_namespaced_deployment = mock_patch
    return mock_patch


# Mocking the Kubernetes API
@pytest.fixture
def mock_core_v1_api():
    with patch("mlopscli.utils.kubernetes.client.CoreV1Api") as mock_api:
        yield mock_api


@pytest.fixture
def mock_v1_service_account():
    with patch("mlopscli.utils.kubernetes.V1ServiceAccount") as mock_sa:
        # Make the mock constructor return the input object unchanged
        mock_sa.side_effect = lambda x: x
        yield mock_sa


@pytest.fixture
def mock_v1_pod():
    with patch("mlopscli.utils.kubernetes.V1Pod") as mock_pod:
        # Make the mock constructor return the input object unchanged
        mock_pod.side_effect = lambda x: x
        yield mock_pod


@pytest.fixture
def mock_v1_pod_status():
    with patch("mlopscli.utils.kubernetes.V1PodStatus") as mock_status:
        # Make the mock constructor return the input object unchanged
        mock_status.side_effect = lambda x: x
        yield mock_status


@given(parsers.parse('a service account named "{service_account_name}" in namespace "{namespace}"'), target_fixture="service_account")
def service_account(service_account_name, namespace, mock_core_v1_api, mock_v1_service_account):
    mock_api_instance = mock_core_v1_api.return_value
    # Mock the read_namespaced_service_account to return a stubbed service account
    mock_service_account = MagicMock()
    mock_service_account.image_pull_secrets = []
    mock_service_account.metadata.name = service_account_name
    mock_service_account.metadata.namespace = namespace

    mock_api_instance.read_namespaced_service_account.return_value = mock_service_account
    return mock_service_account


@given(
    parsers.parse('a non existiant service account named "{service_account_name}" in namespace "{namespace}"'),
    target_fixture="non_existent_service_account",
)
def non_existent_service_account(service_account_name, namespace, mock_core_v1_api, mock_v1_service_account):
    mock_api_instance = mock_core_v1_api.return_value
    mock_api_instance.read_namespaced_service_account.side_effect = client.exceptions.ApiException(status=404)
    return None


@given(parsers.parse('it has an image pull secret named "{secret_name}"'), target_fixture="secret_name")
def has_image_pull_secret(service_account, secret_name):
    # Set the service account to have the specified image pull secret
    service_account.image_pull_secrets = [client.V1LocalObjectReference(name=secret_name)]
    return secret_name


@given("it has no image pull secrets", target_fixture="secret_name")
def no_image_pull_secrets(service_account):
    # Simulate the service account having no image pull secrets
    service_account.image_pull_secrets = None
    return None


@when(parsers.parse('I check if the image pull secret "{secret_name}" exists'), target_fixture="check_secret_exists")
def check_secret_exists_specific(service_account, secret_name, mock_load_kube_config, mock_core_v1_api, mock_v1_service_account):
    # The service account fixture already sets up the mocked API response
    return check_image_pull_secret(
        service_account_name=service_account.metadata.name, namespace=service_account.metadata.namespace, secret_name=secret_name
    )


@when("I check if the image pull secret exists", target_fixture="check_secret_exists")
def check_secret_exists(service_account, secret_name, mock_load_kube_config, mock_core_v1_api, mock_v1_service_account):
    # The service account fixture already sets up the mocked API response
    return check_image_pull_secret(
        service_account_name=service_account.metadata.name, namespace=service_account.metadata.namespace, secret_name=secret_name
    )


@when("I check if the image pull secret exists for non-existent service account", target_fixture="check_secret_exists")
def check_secret_exists_non_existent_service_account(non_existent_service_account, mock_load_kube_config, mock_core_v1_api, mock_v1_service_account):
    # Use a known service account name and namespace that does not exist
    return check_image_pull_secret(service_account_name="invalid-service-account", namespace="default", secret_name="my-secret")  # noqa: S106


@then("it should return true")
def check_return_true(check_secret_exists):
    assert check_secret_exists is True


@then("it should return false")
def check_return_false(check_secret_exists):
    assert check_secret_exists is False


@given(parsers.parse('the secret "{secret_name}" does not exist in namespace "{namespace}"'))
def secret_does_not_exist(secret_name, namespace):
    patch(PATCH_IS_COMPONENT_INSTALLED, return_value=False).start()


@given(parsers.parse('the secret "{secret_name}" exists in namespace "{namespace}"'))
def secret_exists(secret_name, namespace):
    patch(PATCH_IS_COMPONENT_INSTALLED, side_effect=[True, False]).start()


@when(parsers.parse('I create the secret "{secret_name}" with data {secret_data} in namespace "{namespace}"'))
def create_secret_action(secret_name, secret_data, namespace, mock_load_kube_config, mock_core_v1_api):
    secret_data_dict = eval(secret_data)  # noqa: S307
    create_secret(secret_name, secret_data_dict, namespace)


@when(parsers.parse('I create the secret "{secret_name}" with data {secret_data} in namespace "{namespace}" with force create'))
def create_secret_with_force(secret_name, secret_data, namespace, mock_load_kube_config, mock_core_v1_api):
    secret_data_dict = eval(secret_data)  # noqa: S307
    create_secret(secret_name, secret_data_dict, namespace, force_create=True)


@when(parsers.parse('an error occurs while creating the secret "{secret_name}" with data {secret_data} in namespace "{namespace}"'))
def create_secret_with_error(secret_name, secret_data, namespace, mock_load_kube_config, mock_core_v1_api):
    secret_data_dict = eval(secret_data)  # noqa: S307
    mock_instance = mock_core_v1_api.return_value
    mock_instance.create_namespaced_secret.side_effect = client.exceptions.ApiException("API Error")
    create_secret(secret_name, secret_data_dict, namespace)


@then(parsers.parse('the secret "{secret_name}" should be created in namespace "{namespace}"'))
def secret_should_be_created(secret_name, namespace, mock_core_v1_api):
    mock_instance = mock_core_v1_api.return_value
    encoded_data = {key: base64.b64encode(value.encode()).decode() for key, value in {"password": "my_password"}.items()}
    expected_secret_body = {
        "api_version": "v1",
        "kind": "Secret",
        "metadata": {"name": secret_name, "namespace": namespace},
        "type": "Opaque",
        "data": encoded_data,
    }
    call_args = mock_instance.create_namespaced_secret.call_args[1]  # call_args gives us a tuple of (args, kwargs)
    actual_body = call_args["body"]  # This will be a V1Secret object

    assert actual_body.api_version == expected_secret_body["api_version"]
    assert actual_body.kind == expected_secret_body["kind"]
    assert actual_body.metadata.name == expected_secret_body["metadata"]["name"]
    assert actual_body.metadata.namespace == expected_secret_body["metadata"]["namespace"]
    assert actual_body.type == expected_secret_body["type"]
    assert actual_body.data == expected_secret_body["data"]


@then(parsers.parse('the secret "{secret_name}" should be updated in namespace "{namespace}"'))
def secret_should_be_updated(secret_name, namespace, mock_core_v1_api):
    mock_instance = mock_core_v1_api.return_value
    mock_instance.delete_namespaced_secret.assert_called_once()
    encoded_data = {key: base64.b64encode(value.encode()).decode() for key, value in {"password": "my_new_password"}.items()}
    expected_secret_body = {
        "api_version": "v1",
        "kind": "Secret",
        "metadata": {"name": secret_name, "namespace": namespace},
        "type": "Opaque",
        "data": encoded_data,
    }
    call_args = mock_instance.create_namespaced_secret.call_args[1]  # call_args gives us a tuple of (args, kwargs)
    actual_body = call_args["body"]  # This will be a V1Secret object

    assert actual_body.api_version == expected_secret_body["api_version"]
    assert actual_body.kind == expected_secret_body["kind"]
    assert actual_body.metadata.name == expected_secret_body["metadata"]["name"]
    assert actual_body.metadata.namespace == expected_secret_body["metadata"]["namespace"]
    assert actual_body.type == expected_secret_body["type"]
    assert actual_body.data == expected_secret_body["data"]


@pytest.fixture
def mock_is_component_installed():
    with patch(PATCH_IS_COMPONENT_INSTALLED) as mock_installed:
        yield mock_installed


@given(parsers.parse('I have a Docker Registry secret named "{name}"'), target_fixture="name")
def have_secret_name(name):
    return name


@given(parsers.parse('the server is "{server}"'), target_fixture="server")
def given_server(server):
    return server


@given(parsers.parse('the username is "{username}"'), target_fixture="username")
def given_username(username):
    return username


@given(parsers.parse('the token is "{token}"'), target_fixture="token")
def given_token(token):
    return token


@given(parsers.parse('the namespace is "{namespace}"'), target_fixture="namespace")
def given_namespace(namespace):
    return namespace


@when("I create the Docker Registry secret", target_fixture="create_docker_registry_secret_result")
def create_docker_secret(context, mock_load_kube_config, mock_core_v1_api, mock_is_component_installed, name, server, username, token, namespace):
    mock_is_component_installed.return_value = False  # Simulate secret not existing
    context["result"] = create_docker_registry_secret(name, server, username, token, namespace, force_create=False)


@when("I create the Docker Registry secret but already exist", target_fixture="create_docker_registry_secret_result")
def create_docker_secret_but_already_exist(
    context, mock_load_kube_config, mock_core_v1_api, mock_is_component_installed, name, server, username, token, namespace
):
    mock_is_component_installed.return_value = True  # Simulate secret exists
    context["result"] = create_docker_registry_secret(name, server, username, token, namespace, force_create=False)


@when("I create the Docker Registry secret with force_create set to true", target_fixture="create_docker_registry_secret_result")
def create_docker_secret_force(
    context, mock_load_kube_config, mock_core_v1_api, mock_is_component_installed, name, server, username, token, namespace
):
    mock_is_component_installed.side_effect = [True, False]
    mock_core_v1_api.return_value.delete_namespaced_secret = MagicMock()
    context["result"] = create_docker_registry_secret(name, server, username, token, namespace, force_create=True)


@when("an error occurs while creating the docker registry secret")
def create_docker_registry_secret_with_error(
    context, mock_load_kube_config, mock_core_v1_api, mock_is_component_installed, name, server, username, token, namespace
):
    mock_is_component_installed.return_value = False
    mock_instance = mock_core_v1_api.return_value
    mock_instance.create_namespaced_secret.side_effect = client.exceptions.ApiException("API Error")
    context["result"] = create_docker_registry_secret(name, server, username, token, namespace)


@then(parsers.parse('the docker registry secret "{secret_name}" should be created in namespace "{namespace}"'))
def docker_registry_secret_should_be_created(mock_core_v1_api, secret_name, namespace):
    mock_instance = mock_core_v1_api.return_value

    # Check for create_namespaced_secret was called
    assert mock_instance.create_namespaced_secret.called
    call_args = mock_instance.create_namespaced_secret.call_args[1]

    actual_body = call_args["body"]

    # You can add assertions for the structure of the secret
    assert actual_body.metadata.name == secret_name
    assert actual_body.metadata.namespace == namespace


@then(parsers.parse('the docker registry secret "{secret_name}" should be updated in namespace "{namespace}"'))
def docker_registry_secret_should_be_updated(mock_core_v1_api, secret_name, namespace):
    mock_instance = mock_core_v1_api.return_value
    mock_instance.delete_namespaced_secret.assert_called_once()

    # Check for create_namespaced_secret was called
    assert mock_instance.create_namespaced_secret.called
    call_args = mock_instance.create_namespaced_secret.call_args[1]

    actual_body = call_args["body"]

    # You can add assertions for the structure of the secret
    assert actual_body.metadata.name == secret_name
    assert actual_body.metadata.namespace == namespace


@given(parsers.parse('the pod "{pod_name}" exists in namespace "{namespace}"'))
def pod_exists(pod_name, namespace, mock_core_v1_api):
    # Simulate that the pod exists by not mocking delete_namespaced_pod
    pass


@given(parsers.parse('the pod "{pod_name}" does not exist in namespace "{namespace}"'))
def pod_not_found(pod_name, namespace, mock_core_v1_api):
    mock_instance = mock_core_v1_api.return_value
    mock_instance.delete_namespaced_pod.side_effect = client.exceptions.ApiException(status=404)


@given(parsers.parse('a bad behaviour happen for the pod "{pod_name}" does not exist in namespace "{namespace}"'))
def pod_generic_exception(pod_name, namespace, mock_core_v1_api):
    mock_instance = mock_core_v1_api.return_value
    mock_instance.delete_namespaced_pod.side_effect = client.exceptions.ApiException(status=401)


@when(parsers.parse('I delete the pod "{pod_name}" from namespace "{namespace}"'))
def delete_the_pod(mock_load_kube_config, mock_core_v1_api, pod_name, namespace):
    delete_pod(pod_name, namespace)


@then("delete_namespaced_pod has been called")
def assert_pod_not_found(mock_core_v1_api, pod_name, namespace):
    mock_core_v1_api.return_value.delete_namespaced_pod.assert_called_once_with(name=pod_name, namespace=namespace)


@given(parsers.parse('I have the registry secret "{secret_name}" in namespace "{namespace}"'))
def secret_exists_list(secret_name, namespace, mock_load_kube_config, mock_core_v1_api, mock_is_component_installed):
    mock_is_component_installed.return_value = True
    node_mock = MagicMock()
    node_mock.metadata.name = "node-1-------------------------------------------------------"
    mock_core_v1_api.return_value.list_node.return_value.items = [node_mock]


@given(parsers.parse('generic exception occured during retrieving nodes with registry secret "{secret_name}" in namespace "{namespace}"'))
def generic_exception_occured(secret_name, namespace, mock_load_kube_config, mock_core_v1_api, mock_is_component_installed):
    mock_is_component_installed.return_value = True
    mock_instance = mock_core_v1_api.return_value
    mock_instance.list_node.side_effect = client.exceptions.ApiException(status=401)


@when(parsers.parse('I force download the images "{images}" from the registry'))
def force_download_images(mock_load_kube_config, mock_core_v1_api, secret_name, namespace, images):
    image_list = images.split(", ")
    artifactory_username = "test_user"
    artifactory_token = "test_token"  # noqa: S105
    server = "test.registry.com"

    force_download_image_on_host(
        images=image_list,
        registry_secret_name=secret_name,
        namespace=namespace,
        artifactory_username=artifactory_username,
        artifactory_token=artifactory_token,
        server=server,
    )


@pytest.fixture(autouse=True)
def mock_internal_force_functions():
    with (
        patch("mlopscli.utils.kubernetes.create_pod") as mock_create_pod,
        patch("mlopscli.utils.kubernetes.wait_for_pod_to_run") as mock_wait_for_pod,
        patch("mlopscli.utils.kubernetes.delete_pod") as mock_delete_pod,
    ):
        yield mock_create_pod, mock_wait_for_pod, mock_delete_pod


@then("all images should be downloaded successfully on the nodes")
def assert_images_downloaded_successfully(mock_core_v1_api, mock_internal_force_functions):
    assert mock_core_v1_api.return_value.list_node.called
    create_pod_mock, wait_for_pod_mock, delete_pod_mock = mock_internal_force_functions
    assert create_pod_mock.called, "create_pod was not called"
    assert wait_for_pod_mock.called, "wait_for_pod_to_run was not called"
    assert delete_pod_mock.called, "delete_pod was not called"


@given(parsers.parse('I have the pod name "{pod_name}"'))
def pod_name(pod_name):
    return pod_name


@given(parsers.parse('I have the image "{image}"'))
def image(image):
    return image


@given(parsers.parse('a registry secret "{registry_secret_name}"'))
def registry_secret_name(registry_secret_name):
    return registry_secret_name


@given(parsers.parse('I have the namespace "{namespace}"'))
def namespace(namespace):
    return namespace


@given(parsers.parse('I want to assign the pod to node "{node_name}"'))
def node_name(node_name):
    return node_name


@when(parsers.parse("I create the pod"))
def create_the_pod(mock_load_kube_config, mock_core_v1_api, pod_name, image, registry_secret_name, namespace, node_name):
    create_pod(pod_name=pod_name, image=image, registry_secret_name=registry_secret_name, namespace=namespace, node_name=node_name)


@then("the pod should be created successfully")
def assert_pod_created_successfully(mock_core_v1_api, pod_name, namespace):
    # Ensure that create_namespaced_pod method was called once with the correct arguments
    assert mock_core_v1_api.return_value.create_namespaced_pod.called

    called_args = mock_core_v1_api.return_value.create_namespaced_pod.call_args
    assert called_args is not None, "create_namespaced_pod was not called"

    # Check if the pod specification is correct
    pod_spec = called_args[1]["body"]
    assert pod_spec.metadata.name == pod_name, f"Expected pod name '{pod_name}', but got '{pod_spec.metadata.name}'"
    assert pod_spec.metadata.namespace == namespace, f"Expected namespace '{namespace}', but got '{pod_spec.metadata.namespace}'"


@when("I create the pod and an error occurs")
def create_the_pod_with_error(mock_load_kube_config, mock_core_v1_api, pod_name, image, registry_secret_name, namespace, node_name):
    # Mock the create_namespaced_pod method to raise an ApiException
    mock_core_v1_api.return_value.create_namespaced_pod.side_effect = client.exceptions.ApiException("Mocked API Exception")

    create_pod(pod_name=pod_name, image=image, registry_secret_name=registry_secret_name, namespace=namespace, node_name=node_name)


@then("the pod creation should fail")
def assert_pod_creation_failed(create_the_pod_with_error):
    assert create_the_pod_with_error is not None, "Expected ApiException was not raised"


@when(parsers.parse("I wait for the pod to run"))
def wait_for_the_pod_to_run(mock_load_kube_config, mock_core_v1_api, mock_v1_pod, mock_v1_pod_status, pod_name, namespace):
    # Configure the mock to simulate the pod reaching Running status immediately
    pod_mock = MagicMock()
    pod_status_mock = MagicMock()
    pod_status_mock.phase = "Running"
    pod_mock.status = pod_status_mock
    mock_core_v1_api.return_value.read_namespaced_pod.return_value = pod_mock

    # Call the function to test - pod is configured as Running so it should return immediately
    wait_for_pod_to_run(namespace, pod_name)
    return mock_core_v1_api, pod_mock


@then("the pod should be running")
def assert_pod_is_running(mock_core_v1_api, pod_name, namespace):
    # The function was already called in the @when step
    # Assert that read_namespaced_pod was called with the correct parameters
    assert mock_core_v1_api.return_value.read_namespaced_pod.called
    called_args = mock_core_v1_api.return_value.read_namespaced_pod.call_args[1]
    assert called_args["name"] == pod_name
    assert called_args["namespace"] == namespace


@when(parsers.parse("I wait for the pod to run timeout"))
def wait_for_the_pod_to_run_timeout(mock_load_kube_config, mock_core_v1_api, pod_name, namespace):
    # Mocking read_namespaced_pod to simulate timeout
    pod_mock = MagicMock()
    pod_mock.status.phase = "Pending"
    mock_core_v1_api.return_value.read_namespaced_pod.return_value = pod_mock

    # Call the function and make it raise TimeoutError after checks
    with pytest.raises(TimeoutError):
        wait_for_pod_to_run(namespace, pod_name, 1)


@then("a TimeoutError should be raised")
def assert_timeout_error():
    # The assertion is handled in the previous step
    pass


@when(parsers.parse("I wait for the pod to run failed"))
def wait_for_the_pod_to_run_failed(mock_load_kube_config, mock_core_v1_api, mock_v1_pod, mock_v1_pod_status, pod_name, namespace):
    # Mocking read_namespaced_pod to return a "Failed" status
    pod_mock = MagicMock()
    pod_status_mock = MagicMock()
    pod_status_mock.phase = "Failed"
    pod_mock.status = pod_status_mock
    mock_core_v1_api.return_value.read_namespaced_pod.return_value = pod_mock

    # Call the function and expect a RuntimeWarning specifically
    with pytest.raises(RuntimeWarning, match=f"Pod '{pod_name}' is in 'Failed' state. It cannot reach Running."):
        wait_for_pod_to_run(namespace, pod_name)


@then("an Exception should be raised indicating the pod is not running")
def assert_non_running_exception():
    # The assertion is handled in the previous step
    pass


@given(parsers.parse('a helm chart with name "{name}" exists'))
def _helm_chart_exists(context, name):
    with patch(SUBPROCESS_RUN_STR) as mock_run:
        mock_run.return_value = MagicMock(stdout=f"{name}\n", stderr="", returncode=0)
        context[RESULT_KEY] = is_helm_chart_installed(name)


@then(parsers.parse('the answer is "{expected_answer}"'))
def _check_answer(context, expected_answer):
    if expected_answer == "True":
        expected_answer = True
    else:
        expected_answer = False

    assert context[RESULT_KEY] == expected_answer


@given(parsers.parse('a helm chart with name "{name}" does not exist'))
def _helm_chart_does_not_exist(context, name):
    with patch(SUBPROCESS_RUN_STR) as mock_run:
        mock_run.side_effect = subprocess.CalledProcessError(returncode=1, cmd="kubectl")
        context[RESULT_KEY] = is_helm_chart_installed(name)


@given(parsers.parse('a helm repo named "{name}" exists'))
def _add_helm_repo_to_list_of_installed_repo(context, name):
    formatted_string = f"{name}\thttp://blabla.chart.stuff\n"
    context.setdefault(REPOS_KEY, "")
    context[REPOS_KEY] = context[REPOS_KEY].join(formatted_string)


@given("no installed helm repo")
def _clear_helm_repo_list(context):
    context[REPOS_KEY] = ""


@when(parsers.parse('I check if the repo "{name}" is installed'))
def _call_is_helm_repo_installed(context, name):
    with patch("subprocess.check_output") as mock_run:
        mock_run.return_value = context[REPOS_KEY].encode("utf-8")
        context[RESULT_KEY] = is_helm_repo_installed(name)


@then(parsers.parse("it's \"{result}\" that it's installed"))
def _check_repo_installed_status(context, result):
    _check_answer(context, result)


@when(parsers.parse('I uninstall the "{name}" repo'))
def execute_uninstall_helm_repo(context, name):
    # Check if the repo should be identified as installed
    _call_is_helm_repo_installed(context, name)
    identified_as_installed = context[RESULT_KEY]

    with (
        patch("mlopscli.utils.kubernetes.is_helm_repo_installed", return_value=identified_as_installed),
        patch("getpass.getpass", return_value="fake-password"),
        patch(SUBPROCESS_RUN_STR) as mock_run_command,
    ):
        uninstall_helm_repo(name)
        context[COMMAND_KEY] = mock_run_command


@then(parsers.parse('it calls the "{name}" command'))
def _command_was_called(context, name):
    # are we checking a specific command or whatever was logged in COMMAND?
    key = COMMAND_KEY
    if name in context:
        key = name

    assert context[key].called


@then(parsers.parse('it doesn\'t call the "{name}" command'))
def _command_was_not_called(context, name):
    # are we checking a specific command or whatever was logged in COMMAND?
    key = COMMAND_KEY
    if name in context:
        key = name

    assert context[key] is not None, "a mock command should have been stored in the context"
    assert not context[key].called
