---
ingress:
  enabled: true
  className: "cilium"  # Can be changed to "nginx", "cilium", or "default"
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: 64m
  hosts:
    - host: lakefs.dpsc
      paths:
        - /

existingSecret: postgres-secret
secretKeys:
  authEncryptSecretKey: auth_encrypt_secret_key
  databaseConnectionString: databaseConnectionString

lakefsConfig: |
  database:
    type: "postgres"
  blockstore:
    type: s3
    s3:
      force_path_style: true
      endpoint: http://s3.object-store:9000
      discover_bucket_region: false
      credentials:
        access_key_id: minio
        secret_access_key: ADummyBase64String
