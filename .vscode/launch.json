{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "00_INSTALL_K3S",
            "type": "debugpy",
            "request": "launch",
            "program": "./bin/mlopscli",
            "console": "integratedTerminal",
            "args": [
                "k3s",
                "install",
                "-au",
                "<username>",
                "-at",
                "<api-key>"
            ],
            "justMyCode": true
        },
        {
            "name": "01_UNINSTALL_K3S",
            "type": "debugpy",
            "request": "launch",
            "program": "./bin/mlopscli",
            "console": "integratedTerminal",
            "args": [
                "k3s",
                "uninstall"
            ],
            "justMyCode": true
        },
        {
            "name": "00_INSTALL_KAST_LOCAL",
            "type": "debugpy",
            "request": "launch",
            "program": "./bin/mlopscli",
            "console": "integratedTerminal",
            "args": [
                "kast",
                "install",
                "-au",
                "<username>",
                "-at",
                "<api-key>",
                "-kc",
                "~/clones/mlops/kast-config",
                "-l",
                "remote"
            ],
            "justMyCode": true
        },
        {
            "name": "01_UNINSTALL_KAST_LOCAL",
            "type": "debugpy",
            "request": "launch",
            "program": "./bin/mlopscli",
            "console": "integratedTerminal",
            "args": [
                "kast",
                "uninstall",
                "-kc",
                "~/clones/mlops/kast-config",
                "-l",
                "local"
            ],
            "justMyCode": true
        },
        {
            "name": "00_INSTALL_KAST_REMOTE",
            "type": "debugpy",
            "request": "launch",
            "program": "./bin/mlopscli",
            "console": "integratedTerminal",
            "args": [
                "kast",
                "install",
                "-l",
                "remote",
                "-au",
                "${ARTICTORY_USERNAME}",
                "-at",
                "${ARTICTORY_TOKEN}",
            ],
            "justMyCode": false,
            "envFile": "${workspaceFolder}/.env",
            "env": {
                "PYTHONUNBUFFERED": "1",
                "MLOPSCLI_DEBUG": "true"
            },
            "stopOnEntry": false,
            "showReturnValue": true,
        },
        {
            "name": "01_UNINSTALL_KAST_REMOTE",
            "type": "debugpy",
            "request": "launch",
            "program": "./bin/mlopscli",
            "console": "integratedTerminal",
            "args": [
                "kast",
                "uninstall"
            ],
            "justMyCode": true
        },
        {
            "name": "Python Debugger: Current File",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal"
        },
    ]
}