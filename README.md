# MlOps Cli

## Running instructions

To get instruction on how to use the client, type the following command in a bash terminal

```bash
./bin/mlopscli --help
```

### Deploy local kubernetes cluster
#### Install k3s
```bash
./bin/mlopscli k3s install
```

#### Uninstall K3S
```bash
./bin/mlopscli k3s uninstall
```

### Install MLOps toolchain
```bash
# deploy on local cluster
./bin/mlopscli kast install -l local -au <artifactory-username> -at <artifactory-token>
# deploy on remote cluster
./bin/mlopscli kast install -l remote -au <artifactory-username> -at <artifactory-token>

# deploy on local cluster with gitlab-runner
./bin/mlopscli kast install -l local -au <artifactory-username> -at <artifactory-token> -grt <gitlab-runner-token>
```

### Uninstall MLOps toolchain
```bash
# uninstall from local cluster
./bin/mlopscli kast uninstall -l local
# uninstall from remote cluster
./bin/mlopscli kast uninstall -l remote
```

### How to package logs for support request
```bash
mlopscli utility package_logs
```

## Development
### Install pre-commit

```bash
pre-commit install
```
