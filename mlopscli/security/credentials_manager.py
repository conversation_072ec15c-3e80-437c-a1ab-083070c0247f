"""
A reusable, cross-platform secret manager for system keyring integration.
Supports Linux and macOS. Used for storing/retrieving/rotating secrets (e.g., passwords)
for any stack component, including sudo password management.
"""

import getpass
import os
import secrets
import string
from typing import Optional

import keyring


class SecretManager:
    """
    Backward compatibility wrapper for the KeyringManager.

    This class maintains compatibility with existing code while
    providing a unified interface through KeyringManager.
    """

    def __init__(self, service: str, user: str = None) -> None:
        self.service = service
        self.user = user or os.getlogin()
        self._keyring = KeyringManager()

    def get_secret(self) -> Optional[str]:
        """Retrieve the secret from the system keyring, or None if not set."""
        return self._keyring.get_secret(self.service, self.user)

    def create_secret(self, length: int = 24, special_chars: bool = True, overwrite: bool = False) -> str:
        """Generate and store a new secret."""
        secret = self._keyring.create_secret(length, special_chars, overwrite)
        self._keyring.store_secret(self.service, self.user, secret)
        return secret

    def print_retrieve_password_command(self) -> str:
        return f"mlopscli secrets show --component {self.service} --user {self.user}"

    def get_or_create_secret(self, length: int = 24, special_chars: bool = True) -> str:
        """Get existing secret or create a new one if it doesn't exist."""
        existing_secret = self.get_secret()
        if existing_secret:
            return existing_secret
        return self.create_secret(length, special_chars, overwrite=True)

    def delete_secret(self) -> bool:
        """Delete secret if exist."""
        existing_secret = self.get_secret()
        if not existing_secret:
            return True
        return self._keyring.delete_secret(service=self.service, username=self.user)


class KeyringManager:
    """
    keyring manager for secure credential storage.

    This class provides an interface for storing and retrieving
    credentials from the system keyring with proper error handling.
    """

    def __init__(self) -> None:
        """Initialize keyring manager."""
        pass

    def get_secret(self, service: str, username: str) -> Optional[str]:
        """Get secret from keyring."""
        try:
            return keyring.get_password(f"mlopscli.{service}", username)
        except Exception:
            return None

    def store_secret(self, service: str, username: str, password: str) -> bool:
        """Store secret in keyring."""
        try:
            keyring.set_password(f"mlopscli.{service}", username, password)
            return True
        except Exception:
            return False

    def delete_secret(self, service: str, username: str) -> bool:
        """Delete secret from keyring."""
        try:
            keyring.delete_password(f"mlopscli.{service}", username)
            return True
        except Exception:
            return False

    def create_secret(self, length: int = 24, special_chars: bool = True, overwrite: bool = False) -> str:
        """Generate and optionally store a new secret."""
        return self._generate_password(length, special_chars)

    @staticmethod
    def _generate_password(length: int = 24, special_chars: bool = True) -> str:
        if length <= 0:
            raise ValueError("Length must be a positive integer")
        if special_chars:
            chars = string.ascii_letters + string.digits + string.punctuation
        else:
            chars = string.ascii_letters + string.digits
        return "".join(secrets.choice(chars) for _ in range(length))

    @classmethod
    def prompt_and_set_secret(cls, service: str, user: str = None) -> None:
        user = user or os.getlogin()
        password = getpass.getpass(f"Enter password for {service} ({user}): ")
        keyring.set_password(f"mlopscli.{service}", user, password)
        print(f"Password for {service} stored securely in keyring.")

    @classmethod
    def remove_secret(cls, service: str, user: str = None) -> None:
        user = user or os.getlogin()
        keyring.delete_password(f"mlopscli.{service}", user)
        print(f"Password for {service} removed from keyring.")
