"""Dependency Injection containers."""

from dependency_injector import containers
from dependency_injector import providers

from mlopscli.k3s.manage_k3s_installation import ManageK3SInstallation
from mlopscli.kast.manage_kast_installation import ManageKastInstallation
from mlopscli.utils.logger import get_logger

logger = get_logger("containers")


class Clients(containers.DeclarativeContainer):
    """Dependency Injection containers."""

    config = providers.Configuration()
    insensitive_config = providers.Configuration()


class Data(containers.DeclarativeContainer):
    """Dependency Injection containers."""

    config = providers.Configuration()
    insensitive_config = providers.Configuration()
    # CodeReview : Why not using providers.Containers(Clients.... ) here. Otherwise what the use of config and insensitive_config ?
    clients = providers.DependenciesContainer()


class Services(containers.DeclarativeContainer):
    """Dependency Injection containers."""

    config = providers.Configuration()
    insensitive_config = providers.Configuration()
    data = providers.DependenciesContainer()
    # CodeReview : Why not using providers.Containers(Clients.... ) here. Otherwise what the use of config and insensitive_config ?
    clients = providers.DependenciesContainer()

    manage_k3s_installation = providers.Factory(ManageK3SInstallation)
    manage_kast_installation = providers.Factory(ManageKastInstallation)


class Application(containers.DeclarativeContainer):
    """Dependency Injection containers."""

    config = providers.Configuration()
    insensitive_config = providers.Configuration()
    clients = providers.Container(Clients, config=config, insensitive_config=insensitive_config)

    data = providers.Container(
        Data,
        config=config,
        insensitive_config=insensitive_config,
        clients=clients,
    )

    services = providers.Container(
        Services,
        config=config,
        insensitive_config=insensitive_config,
        data=data,
        clients=clients,
    )


def configure_application_container(config: providers.Configuration, cli_group: str) -> None:
    """Set container configuration based on current settings."""
    logger.debug("configure_application_container called for cli_group: %s", cli_group)
    logger.debug("Configuration Details:")
    logger.debug("Name: %s", config.get_name())
    logger.debug("Default: %s", config.get_default())
    logger.debug("Strict Mode: %s", config.get_strict())
    logger.debug("INI Files: %s", config.get_ini_files())
    logger.debug("YAML Files: %s", config.get_yaml_files())
    logger.debug("JSON Files: %s", config.get_json_files())
    logger.debug("Pydantic Settings: %s", config.get_pydantic_settings())
