"""MLOps CLI Component Data Model and Agent Assignment System."""

from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, List, Optional, Set, Any
import logging


class ComponentStatus(Enum):
    """Component installation status."""
    NOT_INSTALLED = "not_installed"
    INSTALLING = "installing"
    INSTALLED = "installed"
    FAILED = "failed"
    UPGRADING = "upgrading"
    UNINSTALLING = "uninstalling"


class ComponentType(Enum):
    """Component category for agent assignment."""
    INFRASTRUCTURE = "infrastructure"
    AUTHENTICATION = "authentication"
    STORAGE = "storage"
    CICD = "cicd"
    DATA_ML = "data_ml"
    OBSERVABILITY = "observability"
    REGISTRY = "registry"
    KAST = "kast"


class InstallationLocation(Enum):
    """Installation target location."""
    LOCAL = "local"
    REMOTE = "remote"


@dataclass
class ResourceRequirements:
    """Resource requirements for a component."""
    cpu_request: str = "100m"
    cpu_limit: str = "1000m"
    memory_request: str = "128Mi"
    memory_limit: str = "1Gi"
    storage_request: str = "1Gi"
    ports: List[int] = field(default_factory=list)
    node_selector: Dict[str, str] = field(default_factory=dict)


@dataclass
class HealthCheck:
    """Health check configuration for a component."""
    endpoint: str
    port: int
    path: str = "/health"
    timeout_seconds: int = 30
    initial_delay_seconds: int = 10
    period_seconds: int = 10
    failure_threshold: int = 3


@dataclass
class ComponentDependency:
    """Represents a dependency relationship between components."""
    component_name: str
    dependency_type: str  # "hard", "soft", "conditional"
    condition: Optional[str] = None  # Condition for conditional dependencies


@dataclass
class ComponentConfiguration:
    """Configuration schema for a component."""
    name: str
    version: str
    namespace: str
    helm_chart: Optional[str] = None
    helm_repo: Optional[str] = None
    kpack_file: Optional[str] = None
    kastctl_required: bool = False
    values: Dict[str, Any] = field(default_factory=dict)
    secrets: List[str] = field(default_factory=list)
    config_maps: List[str] = field(default_factory=list)


@dataclass
class MLOpsComponent:
    """Comprehensive component definition for MLOps CLI."""
    
    # Core identification
    name: str
    display_name: str
    description: str
    component_type: ComponentType
    version: str
    
    # Installation configuration
    namespace: str
    installation_class: str
    configuration: ComponentConfiguration
    
    # Dependencies and requirements
    dependencies: List[ComponentDependency] = field(default_factory=list)
    resource_requirements: ResourceRequirements = field(default_factory=ResourceRequirements)
    health_check: Optional[HealthCheck] = None
    
    # Runtime state
    status: ComponentStatus = ComponentStatus.NOT_INSTALLED
    installation_order: int = 0
    supports_locations: Set[InstallationLocation] = field(
        default_factory=lambda: {InstallationLocation.LOCAL, InstallationLocation.REMOTE}
    )
    
    # Validation rules
    validation_rules: List[str] = field(default_factory=list)
    post_install_tasks: List[str] = field(default_factory=list)


class ComponentRegistry:
    """Central registry for all MLOps components."""
    
    def __init__(self):
        self.components: Dict[str, MLOpsComponent] = {}
        self.dependency_graph: Dict[str, List[str]] = {}
        self.installation_order: List[str] = []
        self.agent_assignments: Dict[ComponentType, List[str]] = {}
        self._logger = logging.getLogger(__name__)
    
    def register_component(self, component: MLOpsComponent) -> None:
        """Register a component in the registry."""
        self.components[component.name] = component
        self._build_dependency_graph()
        self._calculate_installation_order()
        self._assign_to_agent(component)
    
    def get_component(self, name: str) -> Optional[MLOpsComponent]:
        """Retrieve a component by name."""
        return self.components.get(name)
    
    def get_components_by_type(self, component_type: ComponentType) -> List[MLOpsComponent]:
        """Get all components of a specific type."""
        return [comp for comp in self.components.values() if comp.component_type == component_type]
    
    def get_dependencies(self, component_name: str) -> List[str]:
        """Get direct dependencies for a component."""
        return self.dependency_graph.get(component_name, [])
    
    def get_installation_order(self) -> List[str]:
        """Get components in installation order (topological sort)."""
        return self.installation_order.copy()
    
    def validate_dependencies(self, component_name: str) -> List[str]:
        """Validate that all dependencies are satisfied."""
        issues = []
        component = self.get_component(component_name)
        if not component:
            return [f"Component {component_name} not found"]
        
        for dep in component.dependencies:
            dep_component = self.get_component(dep.component_name)
            if not dep_component:
                issues.append(f"Dependency {dep.component_name} not found")
            elif dep_component.status != ComponentStatus.INSTALLED and dep.dependency_type == "hard":
                issues.append(f"Hard dependency {dep.component_name} not installed")
        
        return issues
    
    def _build_dependency_graph(self) -> None:
        """Build the dependency graph from registered components."""
        self.dependency_graph.clear()
        for component in self.components.values():
            deps = [dep.component_name for dep in component.dependencies]
            self.dependency_graph[component.name] = deps
    
    def _calculate_installation_order(self) -> None:
        """Calculate installation order using topological sort."""
        # Simplified topological sort implementation
        visited = set()
        temp_visited = set()
        order = []
        
        def visit(node: str) -> None:
            if node in temp_visited:
                raise ValueError(f"Circular dependency detected involving {node}")
            if node in visited:
                return
            
            temp_visited.add(node)
            for dep in self.dependency_graph.get(node, []):
                if dep in self.components:  # Only visit if dependency exists
                    visit(dep)
            temp_visited.remove(node)
            visited.add(node)
            order.append(node)
        
        for component_name in self.components.keys():
            if component_name not in visited:
                visit(component_name)
        
        self.installation_order = order
        
        # Update installation_order field in components
        for i, component_name in enumerate(order):
            self.components[component_name].installation_order = i
    
    def _assign_to_agent(self, component: MLOpsComponent) -> None:
        """Assign component to appropriate agent type."""
        if component.component_type not in self.agent_assignments:
            self.agent_assignments[component.component_type] = []
        
        if component.name not in self.agent_assignments[component.component_type]:
            self.agent_assignments[component.component_type].append(component.name)


# Global component registry instance
component_registry = ComponentRegistry()


def create_standard_components() -> None:
    """Create and register all standard MLOps components."""
    
    # K3S Infrastructure Foundation
    k3s_component = MLOpsComponent(
        name="k3s",
        display_name="K3S Kubernetes",
        description="Lightweight Kubernetes distribution",
        component_type=ComponentType.INFRASTRUCTURE,
        version="v1.31.4+k3s1",
        namespace="kube-system",
        installation_class="mlopscli.k3s.manage_k3s_installation.ManageK3SInstallation",
        configuration=ComponentConfiguration(
            name="k3s",
            version="v1.31.4+k3s1",
            namespace="kube-system",
            values={"cluster_name": "k3s"}
        ),
        resource_requirements=ResourceRequirements(
            cpu_request="500m",
            cpu_limit="2000m",
            memory_request="512Mi",
            memory_limit="2Gi",
            ports=[6443, 10250, 10251, 10252]
        ),
        health_check=HealthCheck(
            endpoint="localhost",
            port=6443,
            path="/healthz",
            timeout_seconds=10
        ),
        installation_order=0
    )
    
    # Certificate Manager
    cert_manager_component = MLOpsComponent(
        name="cert-manager",
        display_name="Certificate Manager",
        description="Certificate management for Kubernetes",
        component_type=ComponentType.INFRASTRUCTURE,
        version="v1.13.1",
        namespace="cert-manager",
        installation_class="mlopscli.kast.manage_kast_installation.ManageKastInstallation",
        configuration=ComponentConfiguration(
            name="cert-manager",
            version="v1.13.1",
            namespace="cert-manager",
            helm_chart="cert-manager",
            helm_repo="https://charts.jetstack.io"
        ),
        dependencies=[ComponentDependency("k3s", "hard")],
        resource_requirements=ResourceRequirements(
            cpu_request="100m",
            memory_request="128Mi",
            ports=[9402, 10250]
        ),
        installation_order=1
    )
    
    # PostgreSQL Database
    postgresql_component = MLOpsComponent(
        name="postgresql",
        display_name="PostgreSQL Database",
        description="Relational database for MLOps components",
        component_type=ComponentType.STORAGE,
        version="14.9.0",
        namespace="sql-store",
        installation_class="mlopscli.kast.manage_kast_installation.ManageKastInstallation",
        configuration=ComponentConfiguration(
            name="postgresql",
            version="14.9.0",
            namespace="sql-store",
            helm_chart="postgresql",
            helm_repo="https://charts.bitnami.com/bitnami",
            secrets=["postgres-secret"]
        ),
        dependencies=[ComponentDependency("k3s", "hard")],
        resource_requirements=ResourceRequirements(
            cpu_request="250m",
            memory_request="256Mi",
            storage_request="8Gi",
            ports=[5432]
        ),
        health_check=HealthCheck(
            endpoint="postgresql-service",
            port=5432,
            path="/",
            timeout_seconds=15
        ),
        installation_order=2
    )
    
    # MinIO Object Storage
    minio_component = MLOpsComponent(
        name="minio",
        display_name="MinIO Object Storage",  
        description="S3-compatible object storage",
        component_type=ComponentType.STORAGE,
        version="RELEASE.2023-09-04T19-57-37Z",
        namespace="object-store",
        installation_class="mlopscli.kast.manage_kast_installation.ManageKastInstallation",
        configuration=ComponentConfiguration(
            name="minio",
            version="RELEASE.2023-09-04T19-57-37Z",
            namespace="object-store",
            helm_chart="minio",
            helm_repo="https://charts.min.io/",
            secrets=["minio-secret"]
        ),
        dependencies=[ComponentDependency("k3s", "hard")],
        resource_requirements=ResourceRequirements(
            cpu_request="250m",
            memory_request="256Mi",
            storage_request="10Gi",
            ports=[9000, 9001]
        ),
        health_check=HealthCheck(
            endpoint="minio-service",
            port=9000,
            path="/minio/health/live"
        ),
        installation_order=3
    )
    
    # Keycloak Authentication
    keycloak_component = MLOpsComponent(
        name="keycloak",
        display_name="Keycloak Identity Provider",
        description="Identity and access management",
        component_type=ComponentType.AUTHENTICATION,
        version="22.0.1",
        namespace="authentication",
        installation_class="mlopscli.kast.manage_kast_installation.ManageKastInstallation",
        configuration=ComponentConfiguration(
            name="keycloak",
            version="22.0.1",
            namespace="authentication",
            helm_chart="keycloak",
            helm_repo="https://charts.bitnami.com/bitnami",
            secrets=["keycloak-secret"]
        ),
        dependencies=[
            ComponentDependency("k3s", "hard"),
            ComponentDependency("postgresql", "hard"),
            ComponentDependency("cert-manager", "hard")
        ],
        resource_requirements=ResourceRequirements(
            cpu_request="500m",
            memory_request="512Mi",
            ports=[8080, 8443]
        ),
        health_check=HealthCheck(
            endpoint="keycloak-service",
            port=8080,
            path="/health/ready"
        ),
        installation_order=4
    )
    
    # ClickHouse OLAP Database
    clickhouse_component = MLOpsComponent(
        name="clickhouse",
        display_name="ClickHouse OLAP Database",
        description="Columnar database for analytics",
        component_type=ComponentType.STORAGE,
        version="********",
        namespace="olap-store",
        installation_class="mlopscli.kast.manage_clickhouse_installation.ClickHouse",
        configuration=ComponentConfiguration(
            name="clickhouse",
            version="********",
            namespace="olap-store",
            helm_chart="clickhouse",
            helm_repo="https://charts.clickhouse.com/"
        ),
        dependencies=[
            ComponentDependency("k3s", "hard"),
            ComponentDependency("minio", "hard")
        ],
        resource_requirements=ResourceRequirements(
            cpu_request="500m",
            memory_request="1Gi",
            storage_request="10Gi",
            ports=[8123, 9000]
        ),
        installation_order=5
    )
    
    # GitLab Runner
    gitlab_runner_component = MLOpsComponent(
        name="gitlab-runner",
        display_name="GitLab Runner",
        description="CI/CD pipeline executor",
        component_type=ComponentType.CICD,
        version="16.4.1",
        namespace="gitlab-runner",
        installation_class="mlopscli.kast.manage_gitlab_runner_installation.GitlabRunner",
        configuration=ComponentConfiguration(
            name="gitlab-runner",
            version="16.4.1",
            namespace="gitlab-runner",
            helm_chart="gitlab-runner",
            helm_repo="https://charts.gitlab.io/",
            secrets=["gitlab-runner-secret"]
        ),
        dependencies=[ComponentDependency("k3s", "hard")],
        resource_requirements=ResourceRequirements(
            cpu_request="100m",
            memory_request="128Mi"
        ),
        installation_order=6
    )
    
    # Argo Workflows
    argo_component = MLOpsComponent(
        name="argo-workflows",
        display_name="Argo Workflows",
        description="Workflow orchestration engine",
        component_type=ComponentType.CICD,
        version="3.4.11",
        namespace="default",
        installation_class="mlopscli.kast.manage_kast_installation.ManageKastInstallation",
        configuration=ComponentConfiguration(
            name="argo-workflows",
            version="3.4.11",
            namespace="default",
            helm_chart="argo-workflows",
            helm_repo="https://argoproj.github.io/argo-helm"
        ),
        dependencies=[
            ComponentDependency("k3s", "hard"),
            ComponentDependency("minio", "hard")
        ],
        resource_requirements=ResourceRequirements(
            cpu_request="100m",
            memory_request="128Mi",
            ports=[2746, 2747]
        ),
        installation_order=7
    )
    
    # LakeFS Data Versioning
    lakefs_component = MLOpsComponent(
        name="lakefs",
        display_name="LakeFS Data Versioning",
        description="Data version control system",
        component_type=ComponentType.DATA_ML,
        version="1.4.3",
        namespace="default",
        installation_class="mlopscli.kast.manage_lakefs_installation.LakeFS",
        configuration=ComponentConfiguration(
            name="lakefs",
            version="1.4.3",
            namespace="default",
            helm_chart="lakefs",
            helm_repo="https://charts.lakefs.io",
            secrets=["lakefs-secret"]
        ),
        dependencies=[
            ComponentDependency("k3s", "hard"),
            ComponentDependency("postgresql", "hard"),
            ComponentDependency("minio", "hard")
        ],
        resource_requirements=ResourceRequirements(
            cpu_request="250m",
            memory_request="256Mi",
            ports=[8000]
        ),
        health_check=HealthCheck(
            endpoint="lakefs-service",
            port=8000,
            path="/api/v1/config"
        ),
        installation_order=8
    )
    
    # OpenTelemetry
    otel_component = MLOpsComponent(
        name="opentelemetry",
        display_name="OpenTelemetry Collector",
        description="Observability data collection",
        component_type=ComponentType.OBSERVABILITY,
        version="0.85.0",
        namespace="olap-store",
        installation_class="mlopscli.kast.manage_otel_installation.OTel",
        configuration=ComponentConfiguration(
            name="opentelemetry",
            version="0.85.0",
            namespace="olap-store",
            helm_chart="opentelemetry-collector",
            helm_repo="https://open-telemetry.github.io/opentelemetry-helm-charts"
        ),
        dependencies=[
            ComponentDependency("k3s", "hard"),
            ComponentDependency("clickhouse", "hard")
        ],
        resource_requirements=ResourceRequirements(
            cpu_request="100m",
            memory_request="128Mi",
            ports=[4317, 4318, 8888]
        ),
        installation_order=9
    )
    
    # Prometheus Monitoring
    prometheus_component = MLOpsComponent(
        name="prometheus",
        display_name="Prometheus Monitoring",
        description="Metrics collection and alerting",
        component_type=ComponentType.OBSERVABILITY,
        version="2.47.0",
        namespace="monitoring",
        installation_class="mlopscli.kast.manage_kast_installation.ManageKastInstallation",
        configuration=ComponentConfiguration(
            name="prometheus",
            version="2.47.0",
            namespace="monitoring",
            helm_chart="kube-prometheus-stack",
            helm_repo="https://prometheus-community.github.io/helm-charts"
        ),
        dependencies=[ComponentDependency("k3s", "hard")],
        resource_requirements=ResourceRequirements(
            cpu_request="200m",
            memory_request="400Mi",
            storage_request="5Gi",
            ports=[9090]
        ),
        installation_order=10
    )
    
    # Grafana Dashboards
    grafana_component = MLOpsComponent(
        name="grafana",
        display_name="Grafana Dashboards",
        description="Metrics visualization and dashboards",
        component_type=ComponentType.OBSERVABILITY,
        version="10.1.0",
        namespace="monitoring",
        installation_class="mlopscli.kast.manage_kast_installation.ManageKastInstallation",
        configuration=ComponentConfiguration(
            name="grafana",
            version="10.1.0",
            namespace="monitoring"
        ),
        dependencies=[
            ComponentDependency("k3s", "hard"),
            ComponentDependency("prometheus", "hard")
        ],
        resource_requirements=ResourceRequirements(
            cpu_request="100m",
            memory_request="128Mi",
            ports=[3000]
        ),
        installation_order=11
    )
    
    # Zot Registry
    zot_component = MLOpsComponent(
        name="zot",
        display_name="Zot Container Registry",
        description="OCI container registry",
        component_type=ComponentType.REGISTRY,
        version="2.0.0",
        namespace="mlops-toolchain",
        installation_class="mlopscli.kast.manage_kast_installation.ManageKastInstallation",
        configuration=ComponentConfiguration(
            name="zot",
            version="2.0.0",
            namespace="mlops-toolchain"
        ),
        dependencies=[ComponentDependency("k3s", "hard")],
        resource_requirements=ResourceRequirements(
            cpu_request="100m",
            memory_request="128Mi",
            storage_request="5Gi",
            ports=[5000]
        ),
        installation_order=12
    )
    
    # KServe Model Serving
    kserve_component = MLOpsComponent(
        name="kserve",
        display_name="KServe Model Serving",
        description="Machine learning model serving platform",
        component_type=ComponentType.DATA_ML,
        version="0.11.0",
        namespace="kserve-system",
        installation_class="mlopscli.kast.manage_kast_installation.ManageKastInstallation",
        configuration=ComponentConfiguration(
            name="kserve",
            version="0.11.0",
            namespace="kserve-system"
        ),
        dependencies=[ComponentDependency("k3s", "hard")],
        resource_requirements=ResourceRequirements(
            cpu_request="100m",
            memory_request="128Mi"
        ),
        installation_order=13
    )
    
    # Dagger Build System
    dagger_component = MLOpsComponent(
        name="dagger",
        display_name="Dagger Build System",
        description="Portable build automation",
        component_type=ComponentType.CICD,
        version="0.8.7",
        namespace="dagger-system",
        installation_class="mlopscli.kast.manage_kast_installation.ManageKastInstallation",
        configuration=ComponentConfiguration(
            name="dagger",
            version="0.8.7",
            namespace="dagger-system"
        ),
        dependencies=[ComponentDependency("k3s", "hard")],
        resource_requirements=ResourceRequirements(
            cpu_request="100m",
            memory_request="128Mi"
        ),
        installation_order=14
    )
    
    # KAST Package Manager (KASTCTL-based components)
    kast_keycloak_component = MLOpsComponent(
        name="kast-keycloak",
        display_name="KAST Keycloak Package",
        description="Keycloak identity provider via KAST KPACK",
        component_type=ComponentType.KAST,
        version="1.7.9+2.3.0",
        namespace="authentication",
        installation_class="mlopscli.agents.kastctl_agent.KastctlAgent",
        configuration=ComponentConfiguration(
            name="kast-keycloak",
            version="1.7.9+2.3.0",
            namespace="authentication",
            kpack_file="yaml/kpack/keycloak/kpack.yaml",
            kastctl_required=True
        ),
        dependencies=[
            ComponentDependency("k3s", "hard"),
            ComponentDependency("postgresql", "hard"),
            ComponentDependency("cert-manager", "hard")
        ],
        resource_requirements=ResourceRequirements(
            cpu_request="200m",
            cpu_limit="600m",
            memory_request="1300Mi",
            memory_limit="1300Mi"
        ),
        installation_order=15
    )
    
    # KAST ClickHouse Package
    kast_clickhouse_component = MLOpsComponent(
        name="kast-clickhouse",
        display_name="KAST ClickHouse Package",
        description="ClickHouse OLAP database via KAST KPACK",
        component_type=ComponentType.KAST,
        version="3.5.0",
        namespace="olap-store",
        installation_class="mlopscli.agents.kastctl_agent.KastctlAgent",
        configuration=ComponentConfiguration(
            name="kast-clickhouse",
            version="3.5.0",
            namespace="olap-store",
            kpack_file="yaml/kpack/clickhouse/kpack.yaml",
            kastctl_required=True
        ),
        dependencies=[
            ComponentDependency("k3s", "hard"),
            ComponentDependency("minio", "hard")
        ],
        resource_requirements=ResourceRequirements(
            cpu_request="100m",
            memory_request="32Mi"
        ),
        installation_order=16
    )
    
    # KAST MinIO Package
    kast_minio_component = MLOpsComponent(
        name="kast-minio",
        display_name="KAST MinIO Package",
        description="MinIO object storage via KAST KPACK",
        component_type=ComponentType.KAST,
        version="RELEASE.2023-09-04T19-57-37Z",
        namespace="object-store",
        installation_class="mlopscli.agents.kastctl_agent.KastctlAgent",
        configuration=ComponentConfiguration(
            name="kast-minio",
            version="RELEASE.2023-09-04T19-57-37Z",
            namespace="object-store",
            kpack_file="yaml/kpack/minio/kpack.yaml",
            kastctl_required=True
        ),
        dependencies=[ComponentDependency("k3s", "hard")],
        resource_requirements=ResourceRequirements(
            cpu_request="250m",
            memory_request="256Mi",
            storage_request="20Gi"
        ),
        installation_order=17
    )
    
    # KAST PostgreSQL Package
    kast_postgresql_component = MLOpsComponent(
        name="kast-postgresql",
        display_name="KAST PostgreSQL Package",
        description="PostgreSQL database via KAST KPACK",
        component_type=ComponentType.KAST,
        version="14.9.0",
        namespace="sql-store",
        installation_class="mlopscli.agents.kastctl_agent.KastctlAgent",
        configuration=ComponentConfiguration(
            name="kast-postgresql",
            version="14.9.0",
            namespace="sql-store",
            kpack_file="yaml/kpack/postgresql/kpack.yaml",
            kastctl_required=True
        ),
        dependencies=[ComponentDependency("k3s", "hard")],
        resource_requirements=ResourceRequirements(
            cpu_request="250m",
            memory_request="256Mi",
            storage_request="10Gi"
        ),
        installation_order=18
    )
    
    # Register all components
    components = [
        k3s_component, cert_manager_component, postgresql_component, minio_component,
        keycloak_component, clickhouse_component, gitlab_runner_component, argo_component,
        lakefs_component, otel_component, prometheus_component, grafana_component,
        zot_component, kserve_component, dagger_component,
        # KAST-based components
        kast_keycloak_component, kast_clickhouse_component, kast_minio_component, kast_postgresql_component
    ]
    
    for component in components:
        component_registry.register_component(component)


# Initialize standard components
create_standard_components()