"""CI/CD & DevOps Agent for GitLab Runner, Argo Workflows, and Dagger."""

import asyncio
import json
import logging
import subprocess
import time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from pathlib import Path

from mlopscli.agents.component_model import (
    ComponentRegistry, MLOpsComponent, ComponentStatus, ComponentType,
    component_registry
)
from mlopscli.kast.manage_gitlab_runner_installation import <PERSON><PERSON><PERSON>b<PERSON><PERSON><PERSON>
from mlopscli.utils.system import run_command, run_command_with_output
from mlopscli.utils.kubernetes import create_secret, is_component_installed
from mlopscli.utils.constants import INSTALLATION_LOCATION_LOCAL_STR, INSTALLATION_LOCATION_REMOTE_STR


@dataclass
class CICDValidationResult:
    """Result of CI/CD component validation."""
    component_name: str
    is_valid: bool
    issues: List[str]
    recommendations: List[str]
    pipeline_assessment: Dict[str, Any]
    integration_status: Dict[str, Any]


@dataclass
class GitLabRunnerConfig:
    """GitLab Runner configuration."""
    gitlab_url: str
    registration_token: str
    executor: str = "kubernetes"
    concurrent: int = 10
    check_interval: int = 3
    tags: List[str] = None


@dataclass
class ArgoWorkflowConfig:
    """Argo Workflows configuration."""
    executor_type: str = "kubernetes"
    artifact_repository: str = "minio"
    workflow_timeout: str = "1h"
    parallelism: int = 10
    archive_ttl: str = "3d"


class CICDDevOpsAgent:
    """
    Specialized agent for managing CI/CD and DevOps components.
    
    Responsibilities:
    - GitLab Runner installation and configuration
    - Argo Workflows setup for pipeline orchestration
    - Dagger build system integration
    - Pipeline execution monitoring
    - Integration with storage and registry components
    - Build cache optimization
    """
    
    def __init__(self, installation_location: str = INSTALLATION_LOCATION_LOCAL_STR):
        self.installation_location = installation_location
        self.logger = logging.getLogger(f"{__name__}.CICDAgent")
        self.gitlab_runner_manager = GitlabRunner("gitlab-runner")
        self.managed_components = self._get_managed_components()
        
    def _get_managed_components(self) -> List[str]:
        """Get list of components managed by this agent."""
        cicd_components = component_registry.get_components_by_type(ComponentType.CICD)
        return [comp.name for comp in cicd_components]
    
    async def validate_prerequisites(self) -> Dict[str, CICDValidationResult]:
        """Validate prerequisites for CI/CD components."""
        results = {}
        
        for component_name in self.managed_components:
            component = component_registry.get_component(component_name)
            if not component:
                continue
                
            result = await self._validate_component_prerequisites(component)
            results[component_name] = result
            
        return results
    
    async def _validate_component_prerequisites(self, component: MLOpsComponent) -> CICDValidationResult:
        """Validate prerequisites for CI/CD component."""
        issues = []
        recommendations = []
        pipeline_assessment = {}
        integration_status = {}
        
        if component.name == "gitlab-runner":
            issues.extend(await self._validate_gitlab_runner_prerequisites())
            pipeline_assessment = await self._assess_gitlab_runner_capabilities()
            integration_status = await self._check_gitlab_integration()
        elif component.name == "argo-workflows":
            issues.extend(await self._validate_argo_prerequisites())
            pipeline_assessment = await self._assess_argo_capabilities()
            integration_status = await self._check_argo_integration()
        elif component.name == "dagger":
            issues.extend(await self._validate_dagger_prerequisites())
            pipeline_assessment = await self._assess_dagger_capabilities()
            integration_status = await self._check_dagger_integration()
        
        # Common CI/CD validations
        issues.extend(self._validate_common_cicd_prerequisites(component))
        
        if issues:
            recommendations = self._generate_cicd_recommendations(component, issues)
        
        return CICDValidationResult(
            component_name=component.name,
            is_valid=len(issues) == 0,
            issues=issues,
            recommendations=recommendations,
            pipeline_assessment=pipeline_assessment,
            integration_status=integration_status
        )
    
    async def _validate_gitlab_runner_prerequisites(self) -> List[str]:
        """Validate GitLab Runner prerequisites."""
        issues = []
        
        # Check Kubernetes cluster
        try:
            result = subprocess.run([
                "kubectl", "cluster-info"
            ], capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                issues.append("Kubernetes cluster is not accessible")
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            issues.append("Cannot verify Kubernetes cluster status")
        
        # Check if namespace exists
        try:
            result = subprocess.run([
                "kubectl", "get", "namespace", "gitlab-runner"
            ], capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                self.logger.info("gitlab-runner namespace will be created")
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            issues.append("Cannot verify namespace status")
        
        # Check for Docker registry access (needed for builds)
        registry_components = component_registry.get_components_by_type(ComponentType.REGISTRY)
        if not any(comp.status == ComponentStatus.INSTALLED for comp in registry_components):
            issues.append("Container registry is recommended for build artifacts")
        
        return issues
    
    async def _validate_argo_prerequisites(self) -> List[str]:
        """Validate Argo Workflows prerequisites."""
        issues = []
        
        # Check MinIO dependency for artifact storage
        minio_component = component_registry.get_component("minio")
        if not minio_component or minio_component.status != ComponentStatus.INSTALLED:
            issues.append("MinIO object storage is required for Argo Workflows artifacts")
        
        # Check Kubernetes cluster
        try:
            result = subprocess.run([
                "kubectl", "cluster-info"
            ], capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                issues.append("Kubernetes cluster is not accessible")
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            issues.append("Cannot verify Kubernetes cluster status")
        
        # Check for sufficient cluster resources
        issues.extend(await self._validate_argo_resources())
        
        return issues
    
    async def _validate_dagger_prerequisites(self) -> List[str]:
        """Validate Dagger prerequisites."""
        issues = []
        
        # Check Kubernetes cluster
        try:
            result = subprocess.run([
                "kubectl", "cluster-info"
            ], capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                issues.append("Kubernetes cluster is not accessible")
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            issues.append("Cannot verify Kubernetes cluster status")
        
        # Check if container runtime is available
        try:
            result = subprocess.run([
                "docker", "version"
            ], capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                issues.append("Docker container runtime is not available")
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            issues.append("Cannot verify Docker installation")
        
        return issues
    
    async def _validate_argo_resources(self) -> List[str]:
        """Validate cluster resources for Argo Workflows."""
        issues = []
        
        try:
            # Check node resources
            result = subprocess.run([
                "kubectl", "top", "nodes", "--no-headers"
            ], capture_output=True, text=True, timeout=15)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if line.strip():
                        parts = line.split()
                        if len(parts) >= 4:
                            cpu_usage = parts[1]
                            memory_usage = parts[3]
                            
                            if cpu_usage.replace('%', '').isdigit():
                                cpu_pct = int(cpu_usage.replace('%', ''))
                                if cpu_pct > 80:
                                    issues.append(f"High CPU usage ({cpu_pct}%) may affect workflow execution")
                            
                            if memory_usage.replace('%', '').isdigit():
                                mem_pct = int(memory_usage.replace('%', ''))
                                if mem_pct > 80:
                                    issues.append(f"High memory usage ({mem_pct}%) may affect workflow execution")
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            self.logger.warning("Could not validate Argo resource requirements")
        
        return issues
    
    def _validate_common_cicd_prerequisites(self, component: MLOpsComponent) -> List[str]:
        """Validate common prerequisites for CI/CD components."""
        issues = []
        
        # Validate dependencies
        dependency_issues = component_registry.validate_dependencies(component.name)
        issues.extend(dependency_issues)
        
        # Check Helm availability
        try:
            result = subprocess.run([
                "helm", "version", "--short"
            ], capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                issues.append("Helm package manager is not available")
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            issues.append("Cannot verify Helm installation")
        
        return issues
    
    async def _assess_gitlab_runner_capabilities(self) -> Dict[str, Any]:
        """Assess GitLab Runner capabilities."""
        assessment = {
            "executor_type": "kubernetes",
            "concurrent_jobs": 10,
            "cache_enabled": False,
            "docker_in_docker": True,
            "resource_limits": "configurable",
            "autoscaling": True,
            "recommendations": []
        }
        
        assessment["recommendations"].extend([
            "Configure shared cache for faster builds",
            "Set up resource limits for build jobs",
            "Enable Docker-in-Docker for container builds",
            "Configure job timeout limits",
            "Set up build artifacts retention policy"
        ])
        
        return assessment
    
    async def _assess_argo_capabilities(self) -> Dict[str, Any]:
        """Assess Argo Workflows capabilities."""
        assessment = {
            "workflow_engine": "kubernetes_native",
            "parallelism": "configurable",
            "artifact_repository": "minio_s3",
            "workflow_templates": True,
            "cron_workflows": True,
            "event_driven": True,
            "recommendations": []
        }
        
        assessment["recommendations"].extend([
            "Configure workflow templates for reusability",
            "Set up artifact repository for data passing",
            "Enable workflow archiving for audit trails",
            "Configure resource quotas for workflows",
            "Set up monitoring and alerting for failed workflows"
        ])
        
        return assessment
    
    async def _assess_dagger_capabilities(self) -> Dict[str, Any]:
        """Assess Dagger capabilities."""
        assessment = {
            "build_system": "containerized",
            "language_support": "multi_language",
            "caching": "layer_based",
            "portability": "high",
            "integration": "ci_cd_agnostic",
            "recommendations": []
        }
        
        assessment["recommendations"].extend([
            "Configure build caching for performance",
            "Set up multi-stage builds for optimization",
            "Use Dagger modules for reusable build logic",
            "Configure secrets management for builds",
            "Set up build result publishing"
        ])
        
        return assessment
    
    async def _check_gitlab_integration(self) -> Dict[str, Any]:
        """Check GitLab integration status."""
        integration = {
            "gitlab_connectivity": False,
            "token_configured": False,
            "runner_registered": False,
            "kubernetes_executor": False,
            "issues": []
        }
        
        # In a real implementation, these would be actual checks
        integration["issues"].append("GitLab URL and token need to be configured")
        
        return integration
    
    async def _check_argo_integration(self) -> Dict[str, Any]:
        """Check Argo Workflows integration status."""
        integration = {
            "minio_connection": False,
            "service_account": False,
            "rbac_configured": False,
            "ui_accessible": False,
            "issues": []
        }
        
        # Check MinIO connection
        minio_component = component_registry.get_component("minio")
        if minio_component and minio_component.status == ComponentStatus.INSTALLED:
            integration["minio_connection"] = True
        else:
            integration["issues"].append("MinIO integration not configured")
        
        return integration
    
    async def _check_dagger_integration(self) -> Dict[str, Any]:
        """Check Dagger integration status."""
        integration = {
            "engine_running": False,
            "docker_socket": False,
            "registry_access": False,
            "build_cache": False,
            "issues": []
        }
        
        # Check if Docker is available
        try:
            result = subprocess.run([
                "docker", "info"
            ], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                integration["docker_socket"] = True
            else:
                integration["issues"].append("Docker socket not accessible")
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            integration["issues"].append("Docker not available for Dagger")
        
        return integration
    
    def _generate_cicd_recommendations(self, component: MLOpsComponent, issues: List[str]) -> List[str]:
        """Generate recommendations for CI/CD issues."""
        recommendations = []
        
        for issue in issues:
            if "minio" in issue.lower():
                recommendations.append("Install MinIO object storage for artifact management")
            elif "kubernetes" in issue.lower():
                recommendations.append("Ensure Kubernetes cluster is healthy and accessible")
            elif "docker" in issue.lower():
                recommendations.append("Install and configure Docker container runtime")
            elif "registry" in issue.lower():
                recommendations.append("Set up container registry for build artifacts")
            elif "resources" in issue.lower():
                recommendations.append("Scale cluster or optimize resource usage")
        
        # Component-specific recommendations
        if component.name == "gitlab-runner":
            recommendations.extend([
                "Configure GitLab URL and registration token",
                "Set up Kubernetes executor for scalability",
                "Configure shared cache for build performance",
                "Set up proper RBAC permissions"
            ])
        elif component.name == "argo-workflows":
            recommendations.extend([
                "Configure MinIO as artifact repository",
                "Set up workflow templates for common patterns",
                "Configure monitoring and alerting",
                "Set up proper service accounts and RBAC"
            ])
        elif component.name == "dagger":
            recommendations.extend([
                "Configure Dagger engine in Kubernetes",
                "Set up build cache for performance",
                "Configure registry integration",
                "Set up secrets management for builds"
            ])
        
        return recommendations
    
    async def install_component(self, component_name: str, config: Optional[Dict[str, Any]] = None) -> Tuple[bool, List[str]]:
        """Install CI/CD component."""
        component = component_registry.get_component(component_name)
        if not component:
            return False, [f"Component {component_name} not found"]
        
        if component.component_type != ComponentType.CICD:
            return False, [f"Component {component_name} is not a CI/CD component"]
        
        # Update component status
        component.status = ComponentStatus.INSTALLING
        
        try:
            if component_name == "gitlab-runner":
                success, errors = await self._install_gitlab_runner(config or {})
            elif component_name == "argo-workflows":
                success, errors = await self._install_argo_workflows(config or {})
            elif component_name == "dagger":
                success, errors = await self._install_dagger(config or {})
            else:
                return False, [f"Installation not implemented for {component_name}"]
            
            if success:
                component.status = ComponentStatus.INSTALLED
                self.logger.info(f"Successfully installed {component_name}")
            else:
                component.status = ComponentStatus.FAILED
                self.logger.error(f"Failed to install {component_name}: {errors}")
            
            return success, errors
            
        except Exception as e:
            component.status = ComponentStatus.FAILED
            error_msg = f"Exception during {component_name} installation: {str(e)}"
            self.logger.error(error_msg)
            return False, [error_msg]
    
    async def _install_gitlab_runner(self, config: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Install GitLab Runner."""
        try:
            self.logger.info("Starting GitLab Runner installation...")
            
            # Use existing GitLab Runner manager
            success = self.gitlab_runner_manager.install()
            
            if success:
                # Wait for GitLab Runner to be ready
                max_wait = 180
                wait_time = 0
                while wait_time < max_wait:
                    if await self._check_gitlab_runner_ready():
                        break
                    await asyncio.sleep(15)
                    wait_time += 15
                else:
                    return False, ["GitLab Runner failed to become ready within timeout period"]
                
                return True, []
            else:
                return False, ["GitLab Runner installation failed"]
            
        except Exception as e:
            return False, [f"GitLab Runner installation failed: {str(e)}"]
    
    async def _install_argo_workflows(self, config: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Install Argo Workflows."""
        try:
            self.logger.info("Starting Argo Workflows installation...")
            
            # Add Argo Helm repository
            result = subprocess.run([
                "helm", "repo", "add", "argo", "https://argoproj.github.io/argo-helm"
            ], capture_output=True, text=True, timeout=30)
            
            result = subprocess.run([
                "helm", "repo", "update"
            ], capture_output=True, text=True, timeout=60)
            
            # Install Argo Workflows
            helm_cmd = [
                "helm", "install", "argo-workflows", "argo/argo-workflows",
                "--namespace", "default",
                "--set", "workflow.serviceAccount.create=true",
                "--set", "workflow.rbac.create=true",
                "--set", "server.ingress.enabled=true",
                "--set", f"server.ingress.hosts[0]=argo.{config.get('domain', 'dpsc')}"
            ]
            
            # Configure MinIO artifact repository if available
            minio_component = component_registry.get_component("minio")
            if minio_component and minio_component.status == ComponentStatus.INSTALLED:
                helm_cmd.extend([
                    "--set", "useDefaultArtifactRepo=true",
                    "--set", "artifactRepository.s3.endpoint=minio.object-store.svc.cluster.local:9000",
                    "--set", "artifactRepository.s3.bucket=argo-workflows",
                    "--set", "artifactRepository.s3.insecure=true"
                ])
            
            result = subprocess.run(helm_cmd, capture_output=True, text=True, timeout=600)
            
            if result.returncode != 0:
                return False, [f"Failed to install Argo Workflows: {result.stderr}"]
            
            # Wait for Argo to be ready
            max_wait = 300
            wait_time = 0
            while wait_time < max_wait:
                if await self._check_argo_ready():
                    break
                await asyncio.sleep(15)
                wait_time += 15
            else:
                return False, ["Argo Workflows failed to become ready within timeout period"]
            
            return True, []
            
        except Exception as e:
            return False, [f"Argo Workflows installation failed: {str(e)}"]
    
    async def _install_dagger(self, config: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Install Dagger build system."""
        try:
            self.logger.info("Starting Dagger installation...")
            
            # Create namespace
            result = subprocess.run([
                "kubectl", "create", "namespace", "dagger-system", "--dry-run=client", "-o", "yaml"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                subprocess.run([
                    "kubectl", "apply", "-f", "-"
                ], input=result.stdout, text=True, timeout=30)
            
            # Install Dagger engine (simplified installation)
            # In a real implementation, this would deploy the Dagger engine to Kubernetes
            
            self.logger.info("Dagger installation completed (engine configuration required)")
            return True, []
            
        except Exception as e:
            return False, [f"Dagger installation failed: {str(e)}"]
    
    async def _check_gitlab_runner_ready(self) -> bool:
        """Check if GitLab Runner is ready."""
        try:
            result = subprocess.run([
                "kubectl", "get", "pods", "-n", "gitlab-runner",
                "-l", "app=gitlab-runner",
                "--no-headers"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode != 0:
                return False
            
            pods = result.stdout.strip().split('\n')
            for pod in pods:
                if pod.strip():
                    if "Running" not in pod or "1/1" not in pod:
                        return False
            
            return len(pods) > 0 and pods[0].strip()
            
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            return False
    
    async def _check_argo_ready(self) -> bool:
        """Check if Argo Workflows is ready."""
        try:
            result = subprocess.run([
                "kubectl", "get", "pods", "-n", "default",
                "-l", "app.kubernetes.io/name=argo-workflows",
                "--no-headers"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode != 0:
                return False
            
            pods = result.stdout.strip().split('\n')
            for pod in pods:
                if pod.strip():
                    if "Running" not in pod or "1/1" not in pod:
                        return False
            
            return len(pods) > 0 and pods[0].strip()
            
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            return False
    
    async def health_check(self, component_name: str) -> Dict[str, Any]:
        """Perform health check on CI/CD component."""
        component = component_registry.get_component(component_name)
        if not component:
            return {"status": "error", "message": f"Component {component_name} not found"}
        
        if component_name == "gitlab-runner":
            return await self._health_check_gitlab_runner()
        elif component_name == "argo-workflows":
            return await self._health_check_argo_workflows()
        elif component_name == "dagger":
            return await self._health_check_dagger()
        else:
            return {"status": "not_implemented", "message": f"Health check not implemented for {component_name}"}
    
    async def _health_check_gitlab_runner(self) -> Dict[str, Any]:
        """Health check for GitLab Runner."""
        try:
            result = subprocess.run([
                "kubectl", "get", "pods", "-n", "gitlab-runner",
                "-l", "app=gitlab-runner",
                "--no-headers"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode != 0:
                return {"status": "unhealthy", "message": "Cannot check GitLab Runner pods"}
            
            pods = result.stdout.strip().split('\n')
            running_pods = sum(1 for pod in pods if pod.strip() and "Running" in pod and "1/1" in pod)
            total_pods = sum(1 for pod in pods if pod.strip())
            
            if running_pods == total_pods and total_pods > 0:
                return {
                    "status": "healthy",
                    "message": f"All {total_pods} GitLab Runner pods are running",
                    "pods": total_pods
                }
            else:
                return {
                    "status": "degraded",
                    "message": f"{running_pods}/{total_pods} GitLab Runner pods are running"
                }
                
        except Exception as e:
            return {"status": "error", "message": f"Health check failed: {str(e)}"}
    
    async def _health_check_argo_workflows(self) -> Dict[str, Any]:
        """Health check for Argo Workflows."""
        try:
            result = subprocess.run([
                "kubectl", "get", "pods", "-n", "default",
                "-l", "app.kubernetes.io/name=argo-workflows",
                "--no-headers"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode != 0:
                return {"status": "unhealthy", "message": "Cannot check Argo Workflows pods"}
            
            pods = result.stdout.strip().split('\n')
            running_pods = sum(1 for pod in pods if pod.strip() and "Running" in pod and "1/1" in pod)
            total_pods = sum(1 for pod in pods if pod.strip())
            
            if running_pods == total_pods and total_pods > 0:
                return {
                    "status": "healthy",
                    "message": f"All {total_pods} Argo Workflows pods are running",
                    "pods": total_pods
                }
            else:
                return {
                    "status": "degraded",
                    "message": f"{running_pods}/{total_pods} Argo Workflows pods are running"
                }
                
        except Exception as e:
            return {"status": "error", "message": f"Health check failed: {str(e)}"}
    
    async def _health_check_dagger(self) -> Dict[str, Any]:
        """Health check for Dagger."""
        try:
            # Check if Dagger engine is accessible
            result = subprocess.run([
                "kubectl", "get", "pods", "-n", "dagger-system",
                "--no-headers"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode != 0:
                return {"status": "unhealthy", "message": "Cannot check Dagger pods"}
            
            pods = result.stdout.strip().split('\n')
            if not pods or not pods[0].strip():
                return {"status": "degraded", "message": "No Dagger pods found"}
            
            return {"status": "healthy", "message": "Dagger namespace configured"}
                
        except Exception as e:
            return {"status": "error", "message": f"Health check failed: {str(e)}"}
    
    def get_managed_components_status(self) -> Dict[str, ComponentStatus]:
        """Get status of all managed CI/CD components."""
        status_map = {}
        for component_name in self.managed_components:
            component = component_registry.get_component(component_name)
            if component:
                status_map[component_name] = component.status
        return status_map