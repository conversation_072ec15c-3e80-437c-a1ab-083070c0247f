# KServe with APISIX Integration - Complete Kubernetes Deployment Manifests
# Production-ready deployment for dynamic model serving

---
# =============================================================================
# KSERVE NAMESPACE AND RBAC
# =============================================================================

apiVersion: v1
kind: Namespace
metadata:
  name: kserve-test
  labels:
    name: kserve-test
    environment: production
    component: model-serving
    istio-injection: enabled

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: kserve-sa
  namespace: kserve-test
  labels:
    app: kserve

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: kserve-model-serving
  labels:
    app: kserve
rules:
- apiGroups: ["serving.kserve.io"]
  resources: ["inferenceservices", "trainedmodels"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["serving.knative.dev"]
  resources: ["services", "configurations", "revisions", "routes"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: [""]
  resources: ["pods", "services", "endpoints", "persistentvolumeclaims", "events", "configmaps", "secrets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["apps"]
  resources: ["deployments", "daemonsets", "replicasets", "statefulsets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: kserve-model-serving
  labels:
    app: kserve
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: kserve-model-serving
subjects:
- kind: ServiceAccount
  name: kserve-sa
  namespace: kserve-test

---
# =============================================================================
# MODEL STORAGE SECRETS
# =============================================================================

apiVersion: v1
kind: Secret
metadata:
  name: kserve-s3-credentials
  namespace: kserve-test
  labels:
    app: kserve
  annotations:
    serving.kserve.io/s3-endpoint: "minio.object-store.svc.cluster.local:9000"
    serving.kserve.io/s3-usehttps: "0"
    serving.kserve.io/s3-region: "us-east-1"
    serving.kserve.io/s3-useanoncredential: "false"
type: Opaque
data:
  # Base64 encoded credentials - replace with actual values
  AWS_ACCESS_KEY_ID: bWxvcHMtbW9kZWxz  # mlops-models
  AWS_SECRET_ACCESS_KEY: bWxvcHMtbW9kZWxzLXNlY3JldC1rZXk=  # mlops-models-secret-key

---
# =============================================================================
# SKLEARN IRIS MODEL INFERENCE SERVICE
# =============================================================================

apiVersion: serving.kserve.io/v1beta1
kind: InferenceService
metadata:
  name: sklearn-iris-v1
  namespace: kserve-test
  labels:
    app: sklearn-iris
    version: v1.0
    model-type: sklearn
  annotations:
    serving.kserve.io/model-version: "v1.0"
    kserve.io/auth-required: "true"
    kserve.io/rate-limit: "100"
    autoscaling.knative.dev/minScale: "1"
    autoscaling.knative.dev/maxScale: "10"
    autoscaling.knative.dev/target: "10"
    autoscaling.knative.dev/targetUtilizationPercentage: "70"
    autoscaling.knative.dev/scaleDownDelay: "10m"
    autoscaling.knative.dev/scaleToZeroGracePeriod: "30s"
spec:
  predictor:
    serviceAccountName: kserve-sa
    sklearn:
      storageUri: "s3://mlops-models/sklearn/iris/v1.0"
      resources:
        requests:
          cpu: "100m"
          memory: "256Mi"
        limits:
          cpu: "1000m"
          memory: "1Gi"
      env:
      - name: STORAGE_URI
        value: "s3://mlops-models/sklearn/iris/v1.0"
      - name: MODEL_NAME
        value: "sklearn-iris"
      - name: MODEL_VERSION
        value: "v1.0"
    tolerations:
    - key: "model-serving"
      operator: "Equal"
      value: "true"
      effect: "NoSchedule"
    nodeSelector:
      node-type: "model-serving"
  transformer:
    serviceAccountName: kserve-sa
    containers:
    - name: transformer
      image: "registry.example.com/kserve/sklearn-transformer:v1.0"
      env:
      - name: MODEL_NAME
        value: "sklearn-iris"
      - name: MODEL_VERSION  
        value: "v1.0"
      - name: PREDICTOR_HOST
        value: "sklearn-iris-v1-predictor-default.kserve-test.svc.cluster.local"
      resources:
        requests:
          cpu: "50m"
          memory: "128Mi"
        limits:
          cpu: "500m"
          memory: "512Mi"

---
apiVersion: serving.kserve.io/v1beta1
kind: InferenceService
metadata:
  name: sklearn-iris-v2
  namespace: kserve-test
  labels:
    app: sklearn-iris
    version: v2.0
    model-type: sklearn
  annotations:
    serving.kserve.io/model-version: "v2.0"
    kserve.io/auth-required: "true"
    kserve.io/rate-limit: "100"
    kserve.io/canary-deployment: "true"
    autoscaling.knative.dev/minScale: "0"
    autoscaling.knative.dev/maxScale: "5"
    autoscaling.knative.dev/target: "5"
spec:
  predictor:
    serviceAccountName: kserve-sa
    sklearn:
      storageUri: "s3://mlops-models/sklearn/iris/v2.0"
      resources:
        requests:
          cpu: "100m"
          memory: "256Mi"
        limits:
          cpu: "1000m"
          memory: "1Gi"
      env:
      - name: MODEL_NAME
        value: "sklearn-iris"
      - name: MODEL_VERSION
        value: "v2.0"
    tolerations:
    - key: "model-serving"
      operator: "Equal"
      value: "true"
      effect: "NoSchedule"
    nodeSelector:
      node-type: "model-serving"

---
# =============================================================================
# PYTORCH RESNET MODEL INFERENCE SERVICE (GPU)
# =============================================================================

apiVersion: serving.kserve.io/v1beta1
kind: InferenceService
metadata:
  name: pytorch-resnet-v1
  namespace: kserve-test
  labels:
    app: pytorch-resnet
    version: v1.5
    model-type: pytorch
  annotations:
    serving.kserve.io/model-version: "v1.5"
    kserve.io/auth-required: "true"
    kserve.io/rate-limit: "50"
    autoscaling.knative.dev/minScale: "0"
    autoscaling.knative.dev/maxScale: "3"
    autoscaling.knative.dev/target: "2"
    autoscaling.knative.dev/scaleToZeroGracePeriod: "60s"
spec:
  predictor:
    serviceAccountName: kserve-sa
    pytorch:
      storageUri: "s3://mlops-models/pytorch/resnet/v1.5"
      resources:
        requests:
          cpu: "500m"
          memory: "2Gi"
          nvidia.com/gpu: "1"
        limits:
          cpu: "2000m"
          memory: "8Gi"
          nvidia.com/gpu: "1"
      env:
      - name: MODEL_NAME
        value: "pytorch-resnet"
      - name: MODEL_VERSION
        value: "v1.5"
      - name: CUDA_VISIBLE_DEVICES
        value: "0"
    tolerations:
    - key: "gpu-model-serving"
      operator: "Equal" 
      value: "true"
      effect: "NoSchedule"
    nodeSelector:
      accelerator: "nvidia-tesla-v100"
      node-type: "gpu-model-serving"

---
# =============================================================================
# TENSORFLOW SENTIMENT MODEL INFERENCE SERVICE
# =============================================================================

apiVersion: serving.kserve.io/v1beta1
kind: InferenceService
metadata:
  name: tensorflow-sentiment-v3
  namespace: kserve-test
  labels:
    app: tensorflow-sentiment
    version: v3.1
    model-type: tensorflow
  annotations:
    serving.kserve.io/model-version: "v3.1"
    kserve.io/auth-required: "true"
    kserve.io/rate-limit: "200"
    autoscaling.knative.dev/minScale: "2"
    autoscaling.knative.dev/maxScale: "20"
    autoscaling.knative.dev/target: "20"
spec:
  predictor:
    serviceAccountName: kserve-sa
    tensorflow:
      storageUri: "s3://mlops-models/tensorflow/sentiment/v3.1"
      resources:
        requests:
          cpu: "200m"
          memory: "512Mi"
        limits:
          cpu: "1000m"
          memory: "2Gi"
      env:
      - name: MODEL_NAME
        value: "tensorflow-sentiment"
      - name: MODEL_VERSION
        value: "v3.1"
    tolerations:
    - key: "model-serving"
      operator: "Equal"
      value: "true"
      effect: "NoSchedule"
    nodeSelector:
      node-type: "model-serving"
  explainer:
    serviceAccountName: kserve-sa
    containers:
    - name: explainer
      image: "registry.example.com/kserve/alibi-explainer:v0.9"
      env:
      - name: MODEL_NAME
        value: "tensorflow-sentiment"
      - name: MODEL_VERSION
        value: "v3.1"
      - name: EXPLAINER_TYPE
        value: "AnchorText"
      - name: PREDICTOR_HOST
        value: "tensorflow-sentiment-v3-predictor-default.kserve-test.svc.cluster.local"
      resources:
        requests:
          cpu: "100m"
          memory: "256Mi"
        limits:
          cpu: "500m"
          memory: "1Gi"

---
# =============================================================================
# CUSTOM RUNTIME FOR HUGGING FACE TRANSFORMERS
# =============================================================================

apiVersion: serving.kserve.io/v1alpha1
kind: ClusterServingRuntime
metadata:
  name: huggingface-runtime
  labels:
    app: kserve
spec:
  supportedModelFormats:
  - name: huggingface
    version: "1"
    autoSelect: true
  containers:
  - name: kserve-container
    image: "registry.example.com/kserve/huggingface-runtime:v0.9"
    env:
    - name: STORAGE_URI
      value: "{{.StorageUri}}"
    - name: MODEL_NAME
      value: "{{.Name}}"
    resources:
      requests:
        cpu: "500m"
        memory: "1Gi"
      limits:
        cpu: "2000m"
        memory: "4Gi"

---
apiVersion: serving.kserve.io/v1beta1
kind: InferenceService
metadata:
  name: huggingface-bert-base
  namespace: kserve-test
  labels:
    app: huggingface-bert
    version: base
    model-type: huggingface
  annotations:
    serving.kserve.io/model-version: "base"
    kserve.io/auth-required: "true"
    kserve.io/rate-limit: "100"
    autoscaling.knative.dev/minScale: "1"
    autoscaling.knative.dev/maxScale: "8"
    autoscaling.knative.dev/target: "10"
spec:
  predictor:
    serviceAccountName: kserve-sa
    model:
      modelFormat:
        name: huggingface
      storageUri: "s3://mlops-models/huggingface/bert-base-uncased"
      resources:
        requests:
          cpu: "500m"
          memory: "1Gi"
        limits:
          cpu: "2000m"
          memory: "4Gi"
      env:
      - name: MODEL_NAME
        value: "bert-base-uncased"
      - name: TASK
        value: "text-classification"
    tolerations:
    - key: "model-serving"
      operator: "Equal"
      value: "true"
      effect: "NoSchedule"
    nodeSelector:
      node-type: "model-serving"

---
# =============================================================================
# KSERVE CONTROLLER CONFIGURATION
# =============================================================================

apiVersion: v1
kind: ConfigMap
metadata:
  name: inferenceservice-config
  namespace: kserve-test
data:
  predictors: |
    {
      "tensorflow": {
        "image": "tensorflow/serving:2.13.0",
        "defaultImageVersion": "2.13.0",
        "supportedFrameworks": [
          "tensorflow"
        ],
        "multiModelServer": false
      },
      "triton": {
        "image": "nvcr.io/nvidia/tritonserver:23.05-py3",
        "defaultImageVersion": "23.05-py3",
        "supportedFrameworks": [
          "tensorrt",
          "tensorflow",
          "onnx",
          "pytorch"
        ],
        "multiModelServer": true
      },
      "sklearn": {
        "image": "registry.example.com/kserve/sklearnserver:v0.11.0",
        "defaultImageVersion": "v0.11.0",
        "supportedFrameworks": [
          "sklearn"
        ],
        "multiModelServer": false
      },
      "xgboost": {
        "image": "registry.example.com/kserve/xgbserver:v0.11.0",
        "defaultImageVersion": "v0.11.0",
        "supportedFrameworks": [
          "xgboost"
        ],
        "multiModelServer": false
      },
      "pytorch": {
        "image": "registry.example.com/kserve/torchserve:0.8.1",
        "defaultImageVersion": "0.8.1-gpu",
        "supportedFrameworks": [
          "pytorch"
        ],
        "multiModelServer": false
      },
      "huggingface": {
        "image": "registry.example.com/kserve/huggingface-runtime:v0.9",
        "defaultImageVersion": "v0.9",
        "supportedFrameworks": [
          "huggingface"
        ],
        "multiModelServer": false
      }
    }
  transformers: |
    {
      "feast": {
        "image": "registry.example.com/kserve/feast-transformer:v0.11.0",
        "defaultImageVersion": "v0.11.0"
      }
    }
  explainers: |
    {
      "alibi": {
        "image": "registry.example.com/kserve/alibi-explainer:v0.11.0",
        "defaultImageVersion": "v0.11.0"
      },
      "aix": {
        "image": "registry.example.com/kserve/aix-explainer:v0.11.0",
        "defaultImageVersion": "v0.11.0"
      },
      "art": {
        "image": "registry.example.com/kserve/art-explainer:v0.11.0",
        "defaultImageVersion": "v0.11.0"
      }
    }
  storageInitializer: |
    {
      "image": "registry.example.com/kserve/storage-initializer:v0.11.0",
      "memoryRequest": "100Mi",
      "memoryLimit": "1Gi",
      "cpuRequest": "100m",
      "cpuLimit": "1"
    }
  credentials: |
    {
      "gcs": {
        "gcsCredentialFileName": "gcloud-application-credentials.json"
      },
      "s3": {
        "s3AccessKeyIDName": "AWS_ACCESS_KEY_ID",
        "s3SecretAccessKeyName": "AWS_SECRET_ACCESS_KEY"
      },
      "azure": {
        "azureClientIdName": "AZURE_CLIENT_ID",
        "azureClientSecretName": "AZURE_CLIENT_SECRET",
        "azureTenantIdName": "AZURE_TENANT_ID",
        "azureSubscriptionIdName": "AZURE_SUBSCRIPTION_ID"
      }
    }
  ingress: |
    {
      "ingressGateway": "knative-serving/knative-ingress-gateway",
      "ingressService": "istio-ingressgateway.istio-system.svc.cluster.local",
      "localGateway": "knative-serving/knative-local-gateway",
      "localGatewayService": "knative-local-gateway.istio-system.svc.cluster.local",
      "ingressDomain": "example.com",
      "ingressClassName": "istio",
      "domainTemplate": "{{.Name}}-{{.Namespace}}.{{.IngressDomain}}",
      "urlScheme": "https"
    }

---
# =============================================================================
# MODEL MONITORING AND ALERTING
# =============================================================================

apiVersion: v1
kind: ServiceMonitor
metadata:
  name: kserve-models-metrics
  namespace: kserve-test
  labels:
    app: kserve
    monitoring: enabled
spec:
  selector:
    matchLabels:
      serving.kserve.io/inferenceservice: "true"
  endpoints:
  - port: http-metrics
    path: /metrics
    interval: 30s
    scrapeTimeout: 10s

---
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: kserve-model-alerts
  namespace: kserve-test
  labels:
    app: kserve
    monitoring: enabled
spec:
  groups:
  - name: kserve.model.performance
    rules:
    - alert: ModelHighLatency
      expr: histogram_quantile(0.95, rate(kserve_model_request_duration_seconds_bucket[5m])) > 1
      for: 2m
      labels:
        severity: warning
        service: kserve
      annotations:
        summary: "KServe model {{ $labels.model_name }} has high latency"
        description: "Model {{ $labels.model_name }} 95th percentile latency is {{ $value }}s"
        
    - alert: ModelHighErrorRate
      expr: rate(kserve_model_request_total{code!~"2.."}[5m]) / rate(kserve_model_request_total[5m]) > 0.1
      for: 1m
      labels:
        severity: critical
        service: kserve
      annotations:
        summary: "KServe model {{ $labels.model_name }} has high error rate"
        description: "Model {{ $labels.model_name }} error rate is {{ $value | humanizePercentage }}"
        
    - alert: ModelPodCrashLooping
      expr: rate(kube_pod_container_status_restarts_total{namespace="kserve-test"}[5m]) > 0
      for: 2m
      labels:
        severity: critical
        service: kserve
      annotations:
        summary: "KServe model pod {{ $labels.pod }} is crash looping"
        description: "Pod {{ $labels.pod }} has restarted {{ $value }} times in the last 5 minutes"
        
    - alert: ModelResourceExhaustion
      expr: |
        (
          kube_pod_container_resource_requests{resource="memory", namespace="kserve-test"} /
          kube_pod_container_resource_limits{resource="memory", namespace="kserve-test"}
        ) > 0.9
      for: 5m
      labels:
        severity: warning
        service: kserve
      annotations:
        summary: "KServe model {{ $labels.pod }} is running out of memory"
        description: "Model pod {{ $labels.pod }} memory usage is {{ $value | humanizePercentage }} of limit"

---
# =============================================================================
# NETWORK POLICIES FOR MODEL SECURITY
# =============================================================================

apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: kserve-model-netpol
  namespace: kserve-test
  labels:
    app: kserve
spec:
  podSelector:
    matchLabels:
      serving.kserve.io/inferenceservice: "true"
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow traffic from API Gateway
  - from:
    - namespaceSelector:
        matchLabels:
          name: api-gateway
    ports:
    - protocol: TCP
      port: 8080
  # Allow traffic from Istio ingress
  - from:
    - namespaceSelector:
        matchLabels:
          name: istio-system
    ports:
    - protocol: TCP
      port: 8080
  # Allow traffic from monitoring
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 8080
    - protocol: TCP
      port: 9090
  egress:
  # Allow access to S3/MinIO for model loading
  - to:
    - namespaceSelector:
        matchLabels:
          name: object-store
    ports:
    - protocol: TCP
      port: 9000
    - protocol: TCP
      port: 443
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  # Allow HTTPS to external services
  - to: []
    ports:
    - protocol: TCP
      port: 443

---
# =============================================================================
# POD DISRUPTION BUDGETS
# =============================================================================

apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: sklearn-iris-pdb
  namespace: kserve-test
  labels:
    app: sklearn-iris
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: sklearn-iris

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: pytorch-resnet-pdb
  namespace: kserve-test
  labels:
    app: pytorch-resnet
spec:
  minAvailable: 0
  selector:
    matchLabels:
      app: pytorch-resnet

---
apiVersion: policy/v1
kind: PodDisruptionBudgets
metadata:
  name: tensorflow-sentiment-pdb
  namespace: kserve-test
  labels:
    app: tensorflow-sentiment
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: tensorflow-sentiment

---
# =============================================================================
# NODE AFFINITY AND TAINTS FOR MODEL SERVING NODES
# =============================================================================

apiVersion: v1
kind: Node
metadata:
  name: model-serving-node-1
  labels:
    node-type: "model-serving"
    instance-type: "c5.2xlarge"
spec:
  taints:
  - key: "model-serving"
    value: "true"
    effect: "NoSchedule"

---
apiVersion: v1
kind: Node
metadata:
  name: gpu-model-serving-node-1
  labels:
    node-type: "gpu-model-serving"
    accelerator: "nvidia-tesla-v100"
    instance-type: "p3.2xlarge"
spec:
  taints:
  - key: "gpu-model-serving"
    value: "true"
    effect: "NoSchedule"
  - key: "nvidia.com/gpu"
    value: "true"
    effect: "NoSchedule"