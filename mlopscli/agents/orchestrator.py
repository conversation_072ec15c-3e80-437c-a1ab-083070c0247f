"""Main MLOps Agent Orchestrator for coordinating all specialized agents."""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Tuple, Any, Set
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path

from mlopscli.agents.component_model import (
    ComponentRegistry, M<PERSON><PERSON><PERSON>Component, ComponentStatus, ComponentType,
    InstallationLocation, component_registry
)
from mlopscli.agents.infrastructure_agent import InfrastructureFoundationAgent
from mlopscli.agents.authentication_agent import AuthenticationSecurityAgent
from mlopscli.agents.storage_agent import StorageDatabaseAgent
from mlopscli.agents.cicd_agent import CICDDevOpsAgent
from mlopscli.agents.service_resolver import ServiceIngressResolver
from mlopscli.agents.kastctl_agent import KastctlAgent
from mlopscli.utils.constants import INSTALLATION_LOCATION_LOCAL_STR, INSTALLATION_LOCATION_REMOTE_STR


class OrchestrationPhase(Enum):
    """Orchestration phases for systematic deployment."""
    PREREQUISITES = "prerequisites"
    INFRASTRUCTURE = "infrastructure"
    STORAGE = "storage"
    AUTHENTICATION = "authentication"
    CICD = "cicd"
    DATA_ML = "data_ml"
    OBSERVABILITY = "observability"
    REGISTRY = "registry"
    KAST = "kast"
    NETWORKING = "networking"
    VALIDATION = "validation"


class AgentStatus(Enum):
    """Status of individual agents."""
    IDLE = "idle"
    WORKING = "working"
    COMPLETED = "completed"
    FAILED = "failed"
    BLOCKED = "blocked"


@dataclass
class OrchestrationResult:
    """Result of orchestration execution."""
    phase: OrchestrationPhase
    success: bool
    components_processed: List[str]
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    duration_seconds: float = 0.0
    agent_results: Dict[str, Any] = field(default_factory=dict)


@dataclass
class AgentCoordinationState:
    """State tracking for agent coordination."""
    agent_name: str
    status: AgentStatus
    current_task: Optional[str] = None
    components_assigned: List[str] = field(default_factory=list)
    last_update: float = field(default_factory=time.time)
    error_count: int = 0
    success_count: int = 0


class MLOpsAgentOrchestrator:
    """
    Main orchestrator for coordinating all specialized MLOps agents.
    
    Responsibilities:
    - Coordinate all specialized agents
    - Manage installation phases and dependencies
    - Handle concurrent agent execution
    - Provide unified monitoring and reporting
    - Manage error recovery and rollback procedures
    - Ensure component requirements validation across agents
    """
    
    def __init__(self, installation_location: str = INSTALLATION_LOCATION_LOCAL_STR):
        self.installation_location = installation_location
        self.logger = logging.getLogger(f"{__name__}.Orchestrator")
        
        # Initialize specialized agents
        self.infrastructure_agent = InfrastructureFoundationAgent(installation_location)
        self.authentication_agent = AuthenticationSecurityAgent(installation_location)
        self.storage_agent = StorageDatabaseAgent(installation_location)
        self.cicd_agent = CICDDevOpsAgent(installation_location)
        self.kastctl_agent = KastctlAgent(installation_location)
        self.service_resolver = ServiceIngressResolver(installation_location)
        
        # Agent coordination state
        self.agent_states: Dict[str, AgentCoordinationState] = {
            "infrastructure": AgentCoordinationState("infrastructure", AgentStatus.IDLE),
            "authentication": AgentCoordinationState("authentication", AgentStatus.IDLE),
            "storage": AgentCoordinationState("storage", AgentStatus.IDLE),
            "cicd": AgentCoordinationState("cicd", AgentStatus.IDLE),
            "kastctl": AgentCoordinationState("kastctl", AgentStatus.IDLE),
            "service_resolver": AgentCoordinationState("service_resolver", AgentStatus.IDLE)
        }
        
        # Orchestration state
        self.current_phase: Optional[OrchestrationPhase] = None
        self.orchestration_results: List[OrchestrationResult] = []
        self.failed_components: Set[str] = set()
        self.completed_components: Set[str] = set()
        
    async def orchestrate_full_installation(self, config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Orchestrate full MLOps platform installation.
        
        Args:
            config: Installation configuration parameters
            
        Returns:
            Complete orchestration results
        """
        config = config or {}
        start_time = time.time()
        
        self.logger.info("Starting MLOps platform orchestration")
        
        try:
            # Phase 1: Prerequisites validation
            prereq_result = await self._execute_prerequisites_phase(config)
            self.orchestration_results.append(prereq_result)
            
            if not prereq_result.success:
                return self._build_final_result(start_time, "Prerequisites validation failed")
            
            # Phase 2: Infrastructure setup
            infra_result = await self._execute_infrastructure_phase(config)
            self.orchestration_results.append(infra_result)
            
            if not infra_result.success:
                return self._build_final_result(start_time, "Infrastructure setup failed")
            
            # Phase 3: Storage and database setup
            storage_result = await self._execute_storage_phase(config)
            self.orchestration_results.append(storage_result)
            
            if not storage_result.success:
                return self._build_final_result(start_time, "Storage setup failed")
            
            # Phase 4: Authentication setup
            auth_result = await self._execute_authentication_phase(config)
            self.orchestration_results.append(auth_result)
            
            if not auth_result.success:
                return self._build_final_result(start_time, "Authentication setup failed")
            
            # Phase 5: CI/CD setup
            cicd_result = await self._execute_cicd_phase(config)
            self.orchestration_results.append(cicd_result)
            
            if not cicd_result.success:
                return self._build_final_result(start_time, "CI/CD setup failed")
            
            # Phase 6: KAST package deployments
            kast_result = await self._execute_kast_phase(config)
            self.orchestration_results.append(kast_result)
            
            if not kast_result.success:
                return self._build_final_result(start_time, "KAST package deployment failed")
            
            # Phase 7: Networking and service resolution
            network_result = await self._execute_networking_phase(config)
            self.orchestration_results.append(network_result)
            
            if not network_result.success:
                return self._build_final_result(start_time, "Networking setup failed")
            
            # Phase 8: Final validation
            validation_result = await self._execute_validation_phase(config)
            self.orchestration_results.append(validation_result)
            
            return self._build_final_result(start_time, "MLOps platform installation completed successfully")
            
        except Exception as e:
            self.logger.error(f"Orchestration failed with exception: {str(e)}")
            return self._build_final_result(start_time, f"Orchestration failed: {str(e)}")
    
    async def _execute_prerequisites_phase(self, config: Dict[str, Any]) -> OrchestrationResult:
        """Execute prerequisites validation phase."""
        self.current_phase = OrchestrationPhase.PREREQUISITES
        phase_start = time.time()
        errors = []
        warnings = []
        agent_results = {}
        
        self.logger.info("Executing prerequisites validation phase")
        
        try:
            # Run all agent prerequisite validations concurrently
            tasks = [
                self._run_agent_prerequisites("infrastructure", self.infrastructure_agent),
                self._run_agent_prerequisites("authentication", self.authentication_agent),
                self._run_agent_prerequisites("storage", self.storage_agent),
                self._run_agent_prerequisites("cicd", self.cicd_agent),
                self._run_agent_prerequisites("kastctl", self.kastctl_agent)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for i, result in enumerate(results):
                agent_name = ["infrastructure", "authentication", "storage", "cicd", "kastctl"][i]
                
                if isinstance(result, Exception):
                    errors.append(f"{agent_name} prerequisites failed: {str(result)}")
                    agent_results[agent_name] = {"error": str(result)}
                else:
                    agent_results[agent_name] = result
                    
                    # Collect issues and warnings
                    for component_name, validation_result in result.items():
                        if not validation_result.is_valid:
                            errors.extend([f"{component_name}: {issue}" for issue in validation_result.issues])
                        if hasattr(validation_result, 'recommendations') and validation_result.recommendations:
                            warnings.extend([f"{component_name}: {rec}" for rec in validation_result.recommendations])
            
            success = len(errors) == 0
            components_processed = list(component_registry.components.keys())
            
            return OrchestrationResult(
                phase=OrchestrationPhase.PREREQUISITES,
                success=success,
                components_processed=components_processed,
                errors=errors,
                warnings=warnings,
                duration_seconds=time.time() - phase_start,
                agent_results=agent_results
            )
            
        except Exception as e:
            return OrchestrationResult(
                phase=OrchestrationPhase.PREREQUISITES,
                success=False,
                components_processed=[],
                errors=[f"Prerequisites phase failed: {str(e)}"],
                duration_seconds=time.time() - phase_start
            )
    
    async def _execute_infrastructure_phase(self, config: Dict[str, Any]) -> OrchestrationResult:
        """Execute infrastructure setup phase."""
        self.current_phase = OrchestrationPhase.INFRASTRUCTURE
        phase_start = time.time()
        errors = []
        warnings = []
        components_processed = []
        
        self.logger.info("Executing infrastructure setup phase")
        
        try:
            # Update agent state
            self.agent_states["infrastructure"].status = AgentStatus.WORKING
            self.agent_states["infrastructure"].current_task = "infrastructure_installation"
            
            # Get infrastructure components in installation order
            infra_components = [comp for comp in component_registry.get_installation_order() 
                             if component_registry.get_component(comp).component_type == ComponentType.INFRASTRUCTURE]
            
            # Install infrastructure components sequentially (they have dependencies)
            for component_name in infra_components:
                try:
                    self.logger.info(f"Installing infrastructure component: {component_name}")
                    success, component_errors = await self.infrastructure_agent.install_component(component_name)
                    
                    if success:
                        self.completed_components.add(component_name)
                        components_processed.append(component_name)
                        self.agent_states["infrastructure"].success_count += 1
                    else:
                        self.failed_components.add(component_name)
                        errors.extend([f"{component_name}: {err}" for err in component_errors])
                        self.agent_states["infrastructure"].error_count += 1
                        
                        # Infrastructure failures are critical - stop here
                        break
                        
                except Exception as e:
                    error_msg = f"Exception installing {component_name}: {str(e)}"
                    errors.append(error_msg)
                    self.failed_components.add(component_name)
                    self.agent_states["infrastructure"].error_count += 1
                    break
            
            self.agent_states["infrastructure"].status = AgentStatus.COMPLETED if not errors else AgentStatus.FAILED
            
            return OrchestrationResult(
                phase=OrchestrationPhase.INFRASTRUCTURE,
                success=len(errors) == 0,
                components_processed=components_processed,
                errors=errors,
                warnings=warnings,
                duration_seconds=time.time() - phase_start,
                agent_results={"infrastructure_agent": {"components": components_processed, "errors": errors}}
            )
            
        except Exception as e:
            self.agent_states["infrastructure"].status = AgentStatus.FAILED
            return OrchestrationResult(
                phase=OrchestrationPhase.INFRASTRUCTURE,
                success=False,
                components_processed=components_processed,
                errors=[f"Infrastructure phase failed: {str(e)}"],
                duration_seconds=time.time() - phase_start
            )
    
    async def _execute_storage_phase(self, config: Dict[str, Any]) -> OrchestrationResult:
        """Execute storage and database setup phase."""
        self.current_phase = OrchestrationPhase.STORAGE
        phase_start = time.time()
        errors = []
        warnings = []
        components_processed = []
        
        self.logger.info("Executing storage setup phase")
        
        try:
            # Update agent state
            self.agent_states["storage"].status = AgentStatus.WORKING
            self.agent_states["storage"].current_task = "storage_installation"
            
            # Get storage components in installation order
            storage_components = [comp for comp in component_registry.get_installation_order() 
                                if component_registry.get_component(comp).component_type == ComponentType.STORAGE]
            
            # Install storage components (PostgreSQL first, then others can be parallel)
            for component_name in storage_components:
                try:
                    self.logger.info(f"Installing storage component: {component_name}")
                    success, component_errors = await self.storage_agent.install_component(component_name, config)
                    
                    if success:
                        self.completed_components.add(component_name)
                        components_processed.append(component_name)
                        self.agent_states["storage"].success_count += 1
                    else:
                        self.failed_components.add(component_name)
                        errors.extend([f"{component_name}: {err}" for err in component_errors])
                        self.agent_states["storage"].error_count += 1
                        
                except Exception as e:
                    error_msg = f"Exception installing {component_name}: {str(e)}"
                    errors.append(error_msg)
                    self.failed_components.add(component_name)
                    self.agent_states["storage"].error_count += 1
            
            self.agent_states["storage"].status = AgentStatus.COMPLETED if not errors else AgentStatus.FAILED
            
            return OrchestrationResult(
                phase=OrchestrationPhase.STORAGE,
                success=len(errors) == 0,
                components_processed=components_processed,
                errors=errors,
                warnings=warnings,
                duration_seconds=time.time() - phase_start,
                agent_results={"storage_agent": {"components": components_processed, "errors": errors}}
            )
            
        except Exception as e:
            self.agent_states["storage"].status = AgentStatus.FAILED
            return OrchestrationResult(
                phase=OrchestrationPhase.STORAGE,
                success=False,
                components_processed=components_processed,
                errors=[f"Storage phase failed: {str(e)}"],
                duration_seconds=time.time() - phase_start
            )
    
    async def _execute_authentication_phase(self, config: Dict[str, Any]) -> OrchestrationResult:
        """Execute authentication setup phase."""
        self.current_phase = OrchestrationPhase.AUTHENTICATION
        phase_start = time.time()
        errors = []
        warnings = []
        components_processed = []
        
        self.logger.info("Executing authentication setup phase")
        
        try:
            # Update agent state
            self.agent_states["authentication"].status = AgentStatus.WORKING
            self.agent_states["authentication"].current_task = "authentication_installation"
            
            # Get authentication components
            auth_components = [comp for comp in component_registry.get_installation_order() 
                             if component_registry.get_component(comp).component_type == ComponentType.AUTHENTICATION]
            
            # Install authentication components
            for component_name in auth_components:
                try:
                    self.logger.info(f"Installing authentication component: {component_name}")
                    success, component_errors = await self.authentication_agent.install_component(component_name, config)
                    
                    if success:
                        self.completed_components.add(component_name)
                        components_processed.append(component_name)
                        self.agent_states["authentication"].success_count += 1
                    else:
                        self.failed_components.add(component_name)
                        errors.extend([f"{component_name}: {err}" for err in component_errors])
                        self.agent_states["authentication"].error_count += 1
                        
                except Exception as e:
                    error_msg = f"Exception installing {component_name}: {str(e)}"
                    errors.append(error_msg)
                    self.failed_components.add(component_name)
                    self.agent_states["authentication"].error_count += 1
            
            self.agent_states["authentication"].status = AgentStatus.COMPLETED if not errors else AgentStatus.FAILED
            
            return OrchestrationResult(
                phase=OrchestrationPhase.AUTHENTICATION,
                success=len(errors) == 0,
                components_processed=components_processed,
                errors=errors,
                warnings=warnings,
                duration_seconds=time.time() - phase_start,
                agent_results={"authentication_agent": {"components": components_processed, "errors": errors}}
            )
            
        except Exception as e:
            self.agent_states["authentication"].status = AgentStatus.FAILED
            return OrchestrationResult(
                phase=OrchestrationPhase.AUTHENTICATION,
                success=False,
                components_processed=components_processed,
                errors=[f"Authentication phase failed: {str(e)}"],
                duration_seconds=time.time() - phase_start
            )
    
    async def _execute_cicd_phase(self, config: Dict[str, Any]) -> OrchestrationResult:
        """Execute CI/CD setup phase."""
        self.current_phase = OrchestrationPhase.CICD
        phase_start = time.time()
        errors = []
        warnings = []
        components_processed = []
        
        self.logger.info("Executing CI/CD setup phase")
        
        try:
            # Update agent state
            self.agent_states["cicd"].status = AgentStatus.WORKING
            self.agent_states["cicd"].current_task = "cicd_installation"
            
            # Get CI/CD components
            cicd_components = [comp for comp in component_registry.get_installation_order() 
                             if component_registry.get_component(comp).component_type == ComponentType.CICD]
            
            # Install CI/CD components
            for component_name in cicd_components:
                try:
                    self.logger.info(f"Installing CI/CD component: {component_name}")
                    success, component_errors = await self.cicd_agent.install_component(component_name, config)
                    
                    if success:
                        self.completed_components.add(component_name)
                        components_processed.append(component_name)
                        self.agent_states["cicd"].success_count += 1
                    else:
                        self.failed_components.add(component_name)
                        errors.extend([f"{component_name}: {err}" for err in component_errors])
                        self.agent_states["cicd"].error_count += 1
                        
                except Exception as e:
                    error_msg = f"Exception installing {component_name}: {str(e)}"
                    errors.append(error_msg)
                    self.failed_components.add(component_name)
                    self.agent_states["cicd"].error_count += 1
            
            self.agent_states["cicd"].status = AgentStatus.COMPLETED if not errors else AgentStatus.FAILED
            
            return OrchestrationResult(
                phase=OrchestrationPhase.CICD,
                success=len(errors) == 0,
                components_processed=components_processed,
                errors=errors,
                warnings=warnings,
                duration_seconds=time.time() - phase_start,
                agent_results={"cicd_agent": {"components": components_processed, "errors": errors}}
            )
            
        except Exception as e:
            self.agent_states["cicd"].status = AgentStatus.FAILED
            return OrchestrationResult(
                phase=OrchestrationPhase.CICD,
                success=False,
                components_processed=components_processed,
                errors=[f"CI/CD phase failed: {str(e)}"],
                duration_seconds=time.time() - phase_start
            )
    
    async def _execute_kast_phase(self, config: Dict[str, Any]) -> OrchestrationResult:
        """Execute KAST package deployment phase."""
        self.current_phase = OrchestrationPhase.KAST
        phase_start = time.time()
        errors = []
        warnings = []
        components_processed = []
        
        self.logger.info("Executing KAST package deployment phase")
        
        try:
            # Update agent state
            self.agent_states["kastctl"].status = AgentStatus.WORKING
            self.agent_states["kastctl"].current_task = "kast_package_deployment"
            
            # Validate KASTCTL prerequisites first
            kastctl_validation = await self.kastctl_agent.validate_kastctl_prerequisites()
            
            if not kastctl_validation.is_valid:
                errors.extend([f"KASTCTL validation: {issue}" for issue in kastctl_validation.issues])
                self.agent_states["kastctl"].status = AgentStatus.FAILED
                return OrchestrationResult(
                    phase=OrchestrationPhase.KAST,
                    success=False,
                    components_processed=components_processed,
                    errors=errors,
                    warnings=warnings,
                    duration_seconds=time.time() - phase_start,
                    agent_results={"kastctl_validation": kastctl_validation}
                )
            
            # Get KAST components in installation order
            kast_components = [comp for comp in component_registry.get_installation_order() 
                             if component_registry.get_component(comp).component_type == ComponentType.KAST]
            
            # Deploy KAST packages
            for component_name in kast_components:
                try:
                    component = component_registry.get_component(component_name)
                    if not component or not component.configuration.kpack_file:
                        self.logger.warning(f"Skipping {component_name}: no KPACK file configured")
                        continue
                    
                    self.logger.info(f"Deploying KAST package: {component_name}")
                    
                    # Extract KPACK name (remove 'kast-' prefix)
                    kpack_name = component_name.replace("kast-", "")
                    
                    # Deploy KPACK
                    deployment_result = await self.kastctl_agent.deploy_kpack(kpack_name, {
                        "domain": config.get("domain", "dpsc"),
                        "force": config.get("force_kpack_install", True),
                        "namespace": component.namespace
                    })
                    
                    if deployment_result.success:
                        self.completed_components.add(component_name)
                        components_processed.append(component_name)
                        component.status = ComponentStatus.INSTALLED
                        self.agent_states["kastctl"].success_count += 1
                        
                        # Add deployed sub-components
                        components_processed.extend(deployment_result.deployed_components)
                        
                    else:
                        self.failed_components.add(component_name)
                        component.status = ComponentStatus.FAILED
                        errors.extend([f"{component_name}: {err}" for err in deployment_result.errors])
                        self.agent_states["kastctl"].error_count += 1
                        
                        # KAST failures might be acceptable for some components
                        warnings.append(f"KAST package {component_name} failed but continuing")
                        
                except Exception as e:
                    error_msg = f"Exception deploying KAST package {component_name}: {str(e)}"
                    errors.append(error_msg)
                    self.failed_components.add(component_name)
                    self.agent_states["kastctl"].error_count += 1
            
            # Get KPACK deployment status summary
            kpack_status_summary = self.kastctl_agent.get_deployment_status_summary()
            
            self.agent_states["kastctl"].status = AgentStatus.COMPLETED if not errors else AgentStatus.COMPLETED
            
            return OrchestrationResult(
                phase=OrchestrationPhase.KAST,
                success=len([e for e in errors if "failed but continuing" not in e]) == 0,
                components_processed=components_processed,
                errors=errors,
                warnings=warnings,
                duration_seconds=time.time() - phase_start,
                agent_results={
                    "kastctl_agent": {
                        "components": components_processed, 
                        "errors": errors,
                        "kpack_status": kpack_status_summary,
                        "kastctl_version": self.kastctl_agent.get_kastctl_version()
                    }
                }
            )
            
        except Exception as e:
            self.agent_states["kastctl"].status = AgentStatus.FAILED
            return OrchestrationResult(
                phase=OrchestrationPhase.KAST,
                success=False,
                components_processed=components_processed,
                errors=[f"KAST phase failed: {str(e)}"],
                duration_seconds=time.time() - phase_start
            )
    
    async def _execute_networking_phase(self, config: Dict[str, Any]) -> OrchestrationResult:
        """Execute networking and service resolution phase."""
        self.current_phase = OrchestrationPhase.NETWORKING
        phase_start = time.time()
        errors = []
        warnings = []
        components_processed = []
        
        self.logger.info("Executing networking setup phase")
        
        try:
            # Update agent state
            self.agent_states["service_resolver"].status = AgentStatus.WORKING
            self.agent_states["service_resolver"].current_task = "networking_setup"
            
            # Setup networking for all services
            network_results = await self.service_resolver.setup_networking()
            
            components_processed = (
                network_results.get("ingress_created", []) +
                network_results.get("storage_classes_created", []) +
                [sr["service"] for sr in network_results.get("services_resolved", [])]
            )
            
            errors.extend(network_results.get("errors", []))
            
            if network_results.get("services_resolved"):
                for service_result in network_results["services_resolved"]:
                    self.logger.info(f"Resolved service {service_result['service']}: {service_result['endpoint']}")
            
            self.agent_states["service_resolver"].status = AgentStatus.COMPLETED if not errors else AgentStatus.FAILED
            if not errors:
                self.agent_states["service_resolver"].success_count += len(components_processed)
            else:
                self.agent_states["service_resolver"].error_count += len(errors)
            
            return OrchestrationResult(
                phase=OrchestrationPhase.NETWORKING,
                success=len(errors) == 0,
                components_processed=components_processed,
                errors=errors,
                warnings=warnings,
                duration_seconds=time.time() - phase_start,
                agent_results={"service_resolver": network_results}
            )
            
        except Exception as e:
            self.agent_states["service_resolver"].status = AgentStatus.FAILED
            return OrchestrationResult(
                phase=OrchestrationPhase.NETWORKING,
                success=False,
                components_processed=components_processed,
                errors=[f"Networking phase failed: {str(e)}"],
                duration_seconds=time.time() - phase_start
            )
    
    async def _execute_validation_phase(self, config: Dict[str, Any]) -> OrchestrationResult:
        """Execute final validation phase."""
        self.current_phase = OrchestrationPhase.VALIDATION
        phase_start = time.time()
        errors = []
        warnings = []
        components_processed = []
        
        self.logger.info("Executing final validation phase")
        
        try:
            # Run health checks on all installed components
            health_check_tasks = []
            
            for component_name in self.completed_components:
                component = component_registry.get_component(component_name)
                if not component:
                    continue
                
                if component.component_type == ComponentType.INFRASTRUCTURE:
                    health_check_tasks.append(self._run_health_check("infrastructure", component_name, self.infrastructure_agent))
                elif component.component_type == ComponentType.AUTHENTICATION:
                    health_check_tasks.append(self._run_health_check("authentication", component_name, self.authentication_agent))
                elif component.component_type == ComponentType.STORAGE:
                    health_check_tasks.append(self._run_health_check("storage", component_name, self.storage_agent))
                elif component.component_type == ComponentType.CICD:
                    health_check_tasks.append(self._run_health_check("cicd", component_name, self.cicd_agent))
            
            # Run all health checks concurrently
            health_results = await asyncio.gather(*health_check_tasks, return_exceptions=True)
            
            # Process health check results
            for i, result in enumerate(health_results):
                if isinstance(result, Exception):
                    errors.append(f"Health check failed: {str(result)}")
                else:
                    component_name, health_result = result
                    components_processed.append(component_name)
                    
                    if health_result.get("status") != "healthy":
                        warnings.append(f"{component_name}: {health_result.get('message', 'Unknown health issue')}")
            
            # Validate service connectivity
            connectivity_tasks = []
            for service_name in self.service_resolver.get_all_service_endpoints().keys():
                if service_name in [comp.name for comp in component_registry.components.values() if comp.status == ComponentStatus.INSTALLED]:
                    connectivity_tasks.append(self.service_resolver.validate_ingress_connectivity(service_name))
            
            connectivity_results = await asyncio.gather(*connectivity_tasks, return_exceptions=True)
            
            for result in connectivity_results:
                if isinstance(result, Exception):
                    errors.append(f"Connectivity check failed: {str(result)}")
                elif result.get("status") not in ["accessible", "unreachable"]:  # unreachable might be expected for some services
                    errors.append(f"Service connectivity issue: {result}")
            
            return OrchestrationResult(
                phase=OrchestrationPhase.VALIDATION,
                success=len(errors) == 0,
                components_processed=components_processed,
                errors=errors,
                warnings=warnings,
                duration_seconds=time.time() - phase_start,
                agent_results={"health_checks": health_results, "connectivity_checks": connectivity_results}
            )
            
        except Exception as e:
            return OrchestrationResult(
                phase=OrchestrationPhase.VALIDATION,
                success=False,
                components_processed=components_processed,
                errors=[f"Validation phase failed: {str(e)}"],
                duration_seconds=time.time() - phase_start
            )
    
    async def _run_agent_prerequisites(self, agent_name: str, agent) -> Dict[str, Any]:
        """Run prerequisites validation for an agent."""
        self.agent_states[agent_name].status = AgentStatus.WORKING
        self.agent_states[agent_name].current_task = "prerequisites_validation"
        
        try:
            result = await agent.validate_prerequisites()
            self.agent_states[agent_name].status = AgentStatus.COMPLETED
            return result
        except Exception as e:
            self.agent_states[agent_name].status = AgentStatus.FAILED
            self.agent_states[agent_name].error_count += 1
            raise e
    
    async def _run_health_check(self, agent_name: str, component_name: str, agent) -> Tuple[str, Dict[str, Any]]:
        """Run health check for a component via its agent."""
        try:
            health_result = await agent.health_check(component_name)
            return component_name, health_result
        except Exception as e:
            return component_name, {"status": "error", "message": str(e)}
    
    def _build_final_result(self, start_time: float, message: str) -> Dict[str, Any]:
        """Build final orchestration result."""
        total_duration = time.time() - start_time
        
        # Calculate statistics
        total_components = len(component_registry.components)
        completed_count = len(self.completed_components)
        failed_count = len(self.failed_components)
        success_rate = (completed_count / total_components * 100) if total_components > 0 else 0
        
        # Collect all errors and warnings
        all_errors = []
        all_warnings = []
        for result in self.orchestration_results:
            all_errors.extend(result.errors)
            all_warnings.extend(result.warnings)
        
        return {
            "success": failed_count == 0 and completed_count > 0,
            "message": message,
            "duration_seconds": total_duration,
            "statistics": {
                "total_components": total_components,
                "completed_components": completed_count,
                "failed_components": failed_count,
                "success_rate_percent": success_rate
            },
            "components": {
                "completed": list(self.completed_components),
                "failed": list(self.failed_components),
                "all_components": list(component_registry.components.keys())
            },
            "phases": [
                {
                    "phase": result.phase.value,
                    "success": result.success,
                    "duration": result.duration_seconds,
                    "components": result.components_processed,
                    "errors": result.errors,
                    "warnings": result.warnings
                }
                for result in self.orchestration_results
            ],
            "agents": {
                name: {
                    "status": state.status.value,
                    "current_task": state.current_task,
                    "success_count": state.success_count,
                    "error_count": state.error_count,
                    "components_assigned": state.components_assigned
                }
                for name, state in self.agent_states.items()
            },
            "errors": all_errors,
            "warnings": all_warnings,
            "installation_location": self.installation_location
        }
    
    def get_orchestration_status(self) -> Dict[str, Any]:
        """Get current orchestration status."""
        return {
            "current_phase": self.current_phase.value if self.current_phase else None,
            "completed_components": list(self.completed_components),
            "failed_components": list(self.failed_components),
            "agent_states": {
                name: {
                    "status": state.status.value,
                    "current_task": state.current_task,
                    "success_count": state.success_count,
                    "error_count": state.error_count
                }
                for name, state in self.agent_states.items()
            },
            "total_phases": len(self.orchestration_results),
            "installation_location": self.installation_location
        }
    
    async def validate_component_requirements(self, component_name: str) -> Dict[str, Any]:
        """Validate requirements for a specific component across all relevant agents."""
        component = component_registry.get_component(component_name)
        if not component:
            return {"error": f"Component {component_name} not found"}
        
        validation_results = {}
        
        # Route to appropriate agent based on component type
        if component.component_type == ComponentType.INFRASTRUCTURE:
            result = await self.infrastructure_agent.validate_prerequisites()
            validation_results["infrastructure"] = result.get(component_name, {})
        elif component.component_type == ComponentType.AUTHENTICATION:
            result = await self.authentication_agent.validate_prerequisites()
            validation_results["authentication"] = result.get(component_name, {})
        elif component.component_type == ComponentType.STORAGE:
            result = await self.storage_agent.validate_prerequisites()
            validation_results["storage"] = result.get(component_name, {})
        elif component.component_type == ComponentType.CICD:
            result = await self.cicd_agent.validate_prerequisites()
            validation_results["cicd"] = result.get(component_name, {})
        
        # Always check service resolution
        if component_name in self.service_resolver.get_all_service_endpoints():
            connectivity_result = await self.service_resolver.validate_ingress_connectivity(component_name)
            validation_results["connectivity"] = connectivity_result
        
        return {
            "component": component_name,
            "component_type": component.component_type.value,
            "validation_results": validation_results,
            "overall_valid": all(
                result.get("is_valid", True) if hasattr(result, 'get') else True
                for result in validation_results.values()
            )
        }