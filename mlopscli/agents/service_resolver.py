"""Advanced Service Resolution and Ingress Management Agent."""

import asyncio
import json
import logging
import subprocess
import time
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, field
from pathlib import Path
from enum import Enum

from mlopscli.agents.component_model import (
    ComponentRegistry, ML<PERSON>psComponent, ComponentStatus, ComponentType,
    InstallationLocation, component_registry
)
from mlopscli.utils.installation_resolver import InstallationResolver, ServiceConfig
from mlopscli.utils.installation_ingress_resolver import (
    create_service_ingress_config, get_argo_ingress_config, get_grafana_ingress_config,
    get_minio_dual_ingress_config, get_polycore_ingress_config, get_zot_ingress_config,
    get_lakefs_ingress_config, to_yaml
)
from mlopscli.utils.constants import (
    INSTALLATION_LOCATION_LOCAL_STR, INSTALLATION_LOCATION_REMOTE_STR,
    INGRESS_CLASS_NGINX, INGRESS_CLASS_CILIUM, TDS_PROJECT_DOMAIN_NAME
)


class ServiceType(Enum):
    """Service type for resolution strategy."""
    HTTP = "http"
    HTTPS = "https"
    TCP = "tcp"
    GRPC = "grpc"
    DATABASE = "database"
    OBJECT_STORAGE = "object_storage"
    MESSAGE_QUEUE = "message_queue"


class ResolutionStrategy(Enum):
    """Service resolution strategy."""
    CLUSTER_IP = "cluster_ip"
    NODE_PORT = "node_port"
    LOAD_BALANCER = "load_balancer"
    INGRESS = "ingress"
    PORT_FORWARD = "port_forward"


@dataclass
class ServiceEndpoint:
    """Service endpoint configuration."""
    name: str
    service_type: ServiceType
    namespace: str
    port: int
    target_port: Optional[int] = None
    protocol: str = "TCP"
    selector: Dict[str, str] = field(default_factory=dict)
    annotations: Dict[str, str] = field(default_factory=dict)
    labels: Dict[str, str] = field(default_factory=dict)


@dataclass
class IngressConfiguration:
    """Ingress configuration for service exposure."""
    name: str
    namespace: str
    hostname: str
    service_name: str
    service_port: int
    path: str = "/"
    path_type: str = "Prefix"
    tls_enabled: bool = True
    tls_secret_name: Optional[str] = None
    ingress_class: str = INGRESS_CLASS_NGINX
    annotations: Dict[str, str] = field(default_factory=dict)
    additional_paths: List[Dict[str, Any]] = field(default_factory=list)


@dataclass
class StorageConfiguration:
    """Storage configuration for components."""
    name: str
    storage_class: str
    access_modes: List[str]
    size: str
    volume_mode: str = "Filesystem"
    reclaim_policy: str = "Retain"
    mount_path: str = "/data"
    sub_path: Optional[str] = None
    read_only: bool = False


@dataclass
class ServiceResolutionResult:
    """Result of service resolution."""
    service_name: str
    resolution_strategy: ResolutionStrategy
    endpoint_url: str
    internal_endpoint: str
    port_mappings: Dict[str, int]
    health_check_url: Optional[str] = None
    authentication_required: bool = False
    connection_config: Dict[str, Any] = field(default_factory=dict)


class ServiceIngressResolver:
    """
    Advanced service resolution and ingress management agent.
    
    Responsibilities:
    - Service endpoint resolution based on installation location
    - Ingress configuration and management
    - Storage class configuration and management
    - Network policy configuration
    - Service mesh integration
    - Load balancer configuration
    - Certificate management for services
    """
    
    def __init__(self, installation_location: str = INSTALLATION_LOCATION_LOCAL_STR):
        self.installation_location = installation_location
        self.logger = logging.getLogger(f"{__name__}.ServiceResolver")
        self.installation_resolver = InstallationResolver(self.logger)
        self.service_registry: Dict[str, ServiceEndpoint] = {}
        self.ingress_registry: Dict[str, IngressConfiguration] = {}
        self.storage_registry: Dict[str, StorageConfiguration] = {}
        
        # Initialize standard service configurations
        self._initialize_standard_services()
        self._initialize_standard_ingress()
        self._initialize_standard_storage()
    
    def _initialize_standard_services(self) -> None:
        """Initialize standard service endpoint configurations."""
        
        # PostgreSQL service
        postgres_service = ServiceEndpoint(
            name="postgresql",
            service_type=ServiceType.DATABASE,
            namespace="sql-store",
            port=5432,
            selector={"app.kubernetes.io/name": "postgresql"}
        )
        self.register_service_endpoint(postgres_service)
        
        # MinIO service
        minio_service = ServiceEndpoint(
            name="minio",
            service_type=ServiceType.OBJECT_STORAGE,
            namespace="object-store",
            port=9000,
            selector={"app": "minio"}
        )
        self.register_service_endpoint(minio_service)
        
        # MinIO Console service
        minio_console_service = ServiceEndpoint(
            name="minio-console",
            service_type=ServiceType.HTTP,
            namespace="object-store",
            port=9001,
            selector={"app": "minio"}
        )
        self.register_service_endpoint(minio_console_service)
        
        # ClickHouse service
        clickhouse_service = ServiceEndpoint(
            name="clickhouse",
            service_type=ServiceType.DATABASE,
            namespace="olap-store",
            port=8123,
            selector={"app": "clickhouse"}
        )
        self.register_service_endpoint(clickhouse_service)
        
        # Keycloak service
        keycloak_service = ServiceEndpoint(
            name="keycloak",
            service_type=ServiceType.HTTPS,
            namespace="authentication",
            port=8080,
            selector={"app.kubernetes.io/name": "keycloak"}
        )
        self.register_service_endpoint(keycloak_service)
        
        # LakeFS service
        lakefs_service = ServiceEndpoint(
            name="lakefs",
            service_type=ServiceType.HTTPS,
            namespace="default",
            port=8000,
            selector={"app.kubernetes.io/name": "lakefs"}
        )
        self.register_service_endpoint(lakefs_service)
        
        # Argo Workflows service
        argo_service = ServiceEndpoint(
            name="argo-workflows",
            service_type=ServiceType.HTTPS,
            namespace="default",
            port=2746,
            selector={"app.kubernetes.io/name": "argo-workflows-server"}
        )
        self.register_service_endpoint(argo_service)
        
        # Grafana service
        grafana_service = ServiceEndpoint(
            name="grafana",
            service_type=ServiceType.HTTPS,
            namespace="monitoring",
            port=3000,
            selector={"app.kubernetes.io/name": "grafana"}
        )
        self.register_service_endpoint(grafana_service)
        
        # Prometheus service
        prometheus_service = ServiceEndpoint(
            name="prometheus",
            service_type=ServiceType.HTTP,
            namespace="monitoring",
            port=9090,
            selector={"app.kubernetes.io/name": "prometheus"}
        )
        self.register_service_endpoint(prometheus_service)
        
        # Zot Registry service
        zot_service = ServiceEndpoint(
            name="zot",
            service_type=ServiceType.HTTPS,
            namespace="mlops-toolchain",
            port=5000,
            selector={"app": "zot"}
        )
        self.register_service_endpoint(zot_service)
    
    def _initialize_standard_ingress(self) -> None:
        """Initialize standard ingress configurations."""
        
        # Keycloak ingress
        keycloak_ingress = IngressConfiguration(
            name="keycloak-ingress",
            namespace="authentication",
            hostname=f"keycloak.{TDS_PROJECT_DOMAIN_NAME}",
            service_name="keycloak",
            service_port=8080,
            annotations={
                "cert-manager.io/cluster-issuer": "external-ca-issuer",
                "nginx.ingress.kubernetes.io/proxy-buffer-size": "16k",
                "nginx.ingress.kubernetes.io/proxy-body-size": "64m"
            }
        )
        self.register_ingress_configuration(keycloak_ingress)
        
        # LakeFS ingress
        lakefs_ingress = IngressConfiguration(
            name="lakefs-ingress",
            namespace="default",
            hostname=f"lakefs.{TDS_PROJECT_DOMAIN_NAME}",
            service_name="lakefs",
            service_port=8000,
            annotations={
                "cert-manager.io/cluster-issuer": "external-ca-issuer",
                "nginx.ingress.kubernetes.io/proxy-body-size": "64m"
            }
        )
        self.register_ingress_configuration(lakefs_ingress)
        
        # Argo Workflows ingress
        argo_ingress = IngressConfiguration(
            name="argo-workflows-ingress",
            namespace="default",
            hostname=f"argo.{TDS_PROJECT_DOMAIN_NAME}",
            service_name="argo-workflows-server",
            service_port=2746,
            annotations={
                "cert-manager.io/cluster-issuer": "external-ca-issuer",
                "nginx.ingress.kubernetes.io/backend-protocol": "HTTP"
            }
        )
        self.register_ingress_configuration(argo_ingress)
        
        # Grafana ingress
        grafana_ingress = IngressConfiguration(
            name="grafana-ingress",
            namespace="monitoring",
            hostname=f"grafana.{TDS_PROJECT_DOMAIN_NAME}",
            service_name="grafana",
            service_port=3000,
            annotations={
                "cert-manager.io/cluster-issuer": "external-ca-issuer"
            }
        )
        self.register_ingress_configuration(grafana_ingress)
        
        # MinIO dual ingress (API and Console)
        minio_api_ingress = IngressConfiguration(
            name="minio-api-ingress",
            namespace="object-store",
            hostname=f"minio-api.{TDS_PROJECT_DOMAIN_NAME}",
            service_name="minio",
            service_port=9000,
            annotations={
                "cert-manager.io/cluster-issuer": "external-ca-issuer",
                "nginx.ingress.kubernetes.io/proxy-body-size": "1024m"
            }
        )
        self.register_ingress_configuration(minio_api_ingress)
        
        minio_console_ingress = IngressConfiguration(
            name="minio-console-ingress",
            namespace="object-store",
            hostname=f"minio.{TDS_PROJECT_DOMAIN_NAME}",
            service_name="minio-console",
            service_port=9001,
            annotations={
                "cert-manager.io/cluster-issuer": "external-ca-issuer"
            }
        )
        self.register_ingress_configuration(minio_console_ingress)
        
        # Zot Registry ingress
        zot_ingress = IngressConfiguration(
            name="zot-ingress",
            namespace="mlops-toolchain",
            hostname=f"zot.{TDS_PROJECT_DOMAIN_NAME}",
            service_name="zot",
            service_port=5000,
            annotations={
                "cert-manager.io/cluster-issuer": "external-ca-issuer",
                "nginx.ingress.kubernetes.io/proxy-body-size": "1024m"
            }
        )
        self.register_ingress_configuration(zot_ingress)
    
    def _initialize_standard_storage(self) -> None:
        """Initialize standard storage configurations."""
        
        # PostgreSQL storage
        postgres_storage = StorageConfiguration(
            name="postgresql-data",
            storage_class="fast-ssd",
            access_modes=["ReadWriteOnce"],
            size="10Gi",
            mount_path="/var/lib/postgresql/data"
        )
        self.register_storage_configuration(postgres_storage)
        
        # MinIO storage
        minio_storage = StorageConfiguration(
            name="minio-data",
            storage_class="fast-ssd",
            access_modes=["ReadWriteOnce"],
            size="50Gi",
            mount_path="/data"
        )
        self.register_storage_configuration(minio_storage)
        
        # ClickHouse storage
        clickhouse_storage = StorageConfiguration(
            name="clickhouse-data",
            storage_class="fast-ssd",
            access_modes=["ReadWriteOnce"],
            size="20Gi",
            mount_path="/var/lib/clickhouse"
        )
        self.register_storage_configuration(clickhouse_storage)
        
        # Prometheus storage
        prometheus_storage = StorageConfiguration(
            name="prometheus-data",
            storage_class="standard",
            access_modes=["ReadWriteOnce"],
            size="10Gi",
            mount_path="/prometheus"
        )
        self.register_storage_configuration(prometheus_storage)
        
        # Grafana storage
        grafana_storage = StorageConfiguration(
            name="grafana-data",
            storage_class="standard",
            access_modes=["ReadWriteOnce"],
            size="5Gi",
            mount_path="/var/lib/grafana"
        )
        self.register_storage_configuration(grafana_storage)
    
    def register_service_endpoint(self, service: ServiceEndpoint) -> None:
        """Register a service endpoint configuration."""
        self.service_registry[service.name] = service
        self.logger.info(f"Registered service endpoint: {service.name}")
    
    def register_ingress_configuration(self, ingress: IngressConfiguration) -> None:
        """Register an ingress configuration."""
        self.ingress_registry[ingress.name] = ingress
        self.logger.info(f"Registered ingress configuration: {ingress.name}")
    
    def register_storage_configuration(self, storage: StorageConfiguration) -> None:
        """Register a storage configuration."""
        self.storage_registry[storage.name] = storage
        self.logger.info(f"Registered storage configuration: {storage.name}")
    
    async def resolve_service(self, service_name: str, force_strategy: Optional[ResolutionStrategy] = None) -> ServiceResolutionResult:
        """
        Resolve service endpoint based on installation location and strategy.
        
        Args:
            service_name: Name of the service to resolve
            force_strategy: Force specific resolution strategy
            
        Returns:
            ServiceResolutionResult with endpoint information
        """
        service = self.service_registry.get(service_name)
        if not service:
            raise ValueError(f"Service {service_name} not registered")
        
        # Determine resolution strategy
        strategy = force_strategy or self._determine_resolution_strategy(service)
        
        if strategy == ResolutionStrategy.INGRESS:
            return await self._resolve_via_ingress(service)
        elif strategy == ResolutionStrategy.PORT_FORWARD:
            return await self._resolve_via_port_forward(service)
        elif strategy == ResolutionStrategy.CLUSTER_IP:
            return await self._resolve_via_cluster_ip(service)
        elif strategy == ResolutionStrategy.NODE_PORT:
            return await self._resolve_via_node_port(service)
        elif strategy == ResolutionStrategy.LOAD_BALANCER:
            return await self._resolve_via_load_balancer(service)
        else:
            raise ValueError(f"Unsupported resolution strategy: {strategy}")
    
    def _determine_resolution_strategy(self, service: ServiceEndpoint) -> ResolutionStrategy:
        """Determine the best resolution strategy for a service."""
        
        # For remote installations, prefer ingress for HTTP/HTTPS services
        if self.installation_location == INSTALLATION_LOCATION_REMOTE_STR:
            if service.service_type in [ServiceType.HTTP, ServiceType.HTTPS]:
                return ResolutionStrategy.INGRESS
            else:
                return ResolutionStrategy.PORT_FORWARD
        
        # For local installations, prefer cluster IP or port forwarding
        else:
            if service.service_type in [ServiceType.DATABASE, ServiceType.OBJECT_STORAGE]:
                return ResolutionStrategy.PORT_FORWARD
            else:
                return ResolutionStrategy.CLUSTER_IP
    
    async def _resolve_via_ingress(self, service: ServiceEndpoint) -> ServiceResolutionResult:
        """Resolve service via ingress."""
        ingress_name = f"{service.name}-ingress"
        ingress = self.ingress_registry.get(ingress_name)
        
        if not ingress:
            # Create default ingress configuration
            ingress = IngressConfiguration(
                name=ingress_name,
                namespace=service.namespace,
                hostname=f"{service.name}.{TDS_PROJECT_DOMAIN_NAME}",
                service_name=service.name,
                service_port=service.port
            )
            await self._create_ingress(ingress)
        
        protocol = "https" if ingress.tls_enabled else "http"
        endpoint_url = f"{protocol}://{ingress.hostname}"
        internal_endpoint = f"{service.name}.{service.namespace}.svc.cluster.local:{service.port}"
        
        return ServiceResolutionResult(
            service_name=service.name,
            resolution_strategy=ResolutionStrategy.INGRESS,
            endpoint_url=endpoint_url,
            internal_endpoint=internal_endpoint,
            port_mappings={"external": 443 if ingress.tls_enabled else 80, "internal": service.port},
            health_check_url=f"{endpoint_url}/health" if service.service_type == ServiceType.HTTP else None,
            authentication_required=service.service_type == ServiceType.HTTPS
        )
    
    async def _resolve_via_port_forward(self, service: ServiceEndpoint) -> ServiceResolutionResult:
        """Resolve service via port forwarding."""
        local_port = await self._find_available_port(service.port)
        
        # Register with installation resolver for port forwarding
        service_config = ServiceConfig(
            service_name=service.name,
            local_port=local_port,
            remote_port=service.port,
            namespace=service.namespace,
            client_cls=object  # Placeholder
        )
        
        self.installation_resolver.register_service(service_config)
        
        endpoint_url = f"http://localhost:{local_port}"
        internal_endpoint = f"{service.name}.{service.namespace}.svc.cluster.local:{service.port}"
        
        return ServiceResolutionResult(
            service_name=service.name,
            resolution_strategy=ResolutionStrategy.PORT_FORWARD,
            endpoint_url=endpoint_url,
            internal_endpoint=internal_endpoint,
            port_mappings={"local": local_port, "remote": service.port},
            health_check_url=f"{endpoint_url}/health" if service.service_type == ServiceType.HTTP else None
        )
    
    async def _resolve_via_cluster_ip(self, service: ServiceEndpoint) -> ServiceResolutionResult:
        """Resolve service via cluster IP."""
        internal_endpoint = f"{service.name}.{service.namespace}.svc.cluster.local:{service.port}"
        
        return ServiceResolutionResult(
            service_name=service.name,
            resolution_strategy=ResolutionStrategy.CLUSTER_IP,
            endpoint_url=internal_endpoint,
            internal_endpoint=internal_endpoint,
            port_mappings={"cluster": service.port}
        )
    
    async def _resolve_via_node_port(self, service: ServiceEndpoint) -> ServiceResolutionResult:
        """Resolve service via NodePort."""
        # Get node IP and NodePort
        node_port = await self._get_node_port(service)
        node_ip = await self._get_node_ip()
        
        endpoint_url = f"http://{node_ip}:{node_port}"
        internal_endpoint = f"{service.name}.{service.namespace}.svc.cluster.local:{service.port}"
        
        return ServiceResolutionResult(
            service_name=service.name,
            resolution_strategy=ResolutionStrategy.NODE_PORT,
            endpoint_url=endpoint_url,
            internal_endpoint=internal_endpoint,
            port_mappings={"node_port": node_port, "internal": service.port}
        )
    
    async def _resolve_via_load_balancer(self, service: ServiceEndpoint) -> ServiceResolutionResult:
        """Resolve service via LoadBalancer."""
        # Get LoadBalancer external IP
        external_ip = await self._get_load_balancer_ip(service)
        
        endpoint_url = f"http://{external_ip}:{service.port}"
        internal_endpoint = f"{service.name}.{service.namespace}.svc.cluster.local:{service.port}"
        
        return ServiceResolutionResult(
            service_name=service.name,
            resolution_strategy=ResolutionStrategy.LOAD_BALANCER,
            endpoint_url=endpoint_url,
            internal_endpoint=internal_endpoint,
            port_mappings={"external": service.port, "internal": service.port}
        )
    
    async def _create_ingress(self, ingress: IngressConfiguration) -> bool:
        """Create ingress resource in Kubernetes."""
        try:
            # Use existing ingress resolver functions
            if ingress.service_name == "argo-workflows":
                ingress_config = get_argo_ingress_config(
                    ingress.hostname,
                    ingress.service_name,
                    ingress.service_port,
                    ingress.annotations
                )
            elif ingress.service_name == "grafana":
                ingress_config = get_grafana_ingress_config(
                    ingress.hostname,
                    ingress.service_name,
                    ingress.service_port,
                    ingress.annotations
                )
            elif ingress.service_name in ["minio", "minio-console"]:
                ingress_config = get_minio_dual_ingress_config(
                    ingress.hostname,
                    ingress.service_name,
                    ingress.service_port,
                    ingress.annotations
                )
            elif ingress.service_name == "lakefs":
                ingress_config = get_lakefs_ingress_config(
                    ingress.hostname,
                    ingress.service_name,
                    ingress.service_port,
                    ingress.annotations
                )
            elif ingress.service_name == "zot":
                ingress_config = get_zot_ingress_config(
                    ingress.hostname,
                    ingress.service_name,
                    ingress.service_port,
                    ingress.annotations
                )
            else:
                # Generic ingress configuration
                ingress_config = create_service_ingress_config(
                    ingress.name,
                    ingress.namespace,
                    ingress.hostname,
                    ingress.service_name,
                    ingress.service_port,
                    ingress.path,
                    ingress.annotations
                )
            
            # Apply ingress configuration
            ingress_yaml = to_yaml(ingress_config)
            
            result = subprocess.run([
                "kubectl", "apply", "-f", "-"
            ], input=ingress_yaml, text=True, timeout=30)
            
            if result.returncode == 0:
                self.logger.info(f"Created ingress: {ingress.name}")
                self.register_ingress_configuration(ingress)
                return True
            else:
                self.logger.error(f"Failed to create ingress: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"Exception creating ingress {ingress.name}: {str(e)}")
            return False
    
    async def _find_available_port(self, preferred_port: int) -> int:
        """Find an available local port for port forwarding."""
        import socket
        
        # Try preferred port first
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            try:
                s.bind(('localhost', preferred_port))
                return preferred_port
            except OSError:
                pass
        
        # Find any available port
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('localhost', 0))
            return s.getsockname()[1]
    
    async def _get_node_port(self, service: ServiceEndpoint) -> int:
        """Get NodePort for a service."""
        try:
            result = subprocess.run([
                "kubectl", "get", "service", service.name,
                "-n", service.namespace,
                "-o", "jsonpath='{.spec.ports[0].nodePort}'"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                return int(result.stdout.strip().strip("'"))
            else:
                return 30000 + service.port  # Default NodePort range
                
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError, ValueError):
            return 30000 + service.port
    
    async def _get_node_ip(self) -> str:
        """Get node IP address."""
        try:
            result = subprocess.run([
                "kubectl", "get", "nodes",
                "-o", "jsonpath='{.items[0].status.addresses[0].address}'"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                return result.stdout.strip().strip("'")
            else:
                return "localhost"
                
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            return "localhost"
    
    async def _get_load_balancer_ip(self, service: ServiceEndpoint) -> str:
        """Get LoadBalancer external IP."""
        try:
            result = subprocess.run([
                "kubectl", "get", "service", service.name,
                "-n", service.namespace,
                "-o", "jsonpath='{.status.loadBalancer.ingress[0].ip}'"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and result.stdout.strip():
                return result.stdout.strip().strip("'")
            else:
                return await self._get_node_ip()  # Fallback to node IP
                
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            return await self._get_node_ip()
    
    async def create_storage_class(self, storage_config: StorageConfiguration) -> bool:
        """Create storage class if it doesn't exist."""
        try:
            # Check if storage class exists
            result = subprocess.run([
                "kubectl", "get", "storageclass", storage_config.storage_class
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                self.logger.info(f"Storage class {storage_config.storage_class} already exists")
                return True
            
            # Create storage class
            storage_class_yaml = f"""
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: {storage_config.storage_class}
provisioner: rancher.io/local-path
reclaimPolicy: {storage_config.reclaim_policy}
volumeBindingMode: WaitForFirstConsumer
allowVolumeExpansion: true
"""
            
            result = subprocess.run([
                "kubectl", "apply", "-f", "-"
            ], input=storage_class_yaml, text=True, timeout=30)
            
            if result.returncode == 0:
                self.logger.info(f"Created storage class: {storage_config.storage_class}")
                return True
            else:
                self.logger.error(f"Failed to create storage class: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"Exception creating storage class: {str(e)}")
            return False
    
    async def validate_ingress_connectivity(self, service_name: str) -> Dict[str, Any]:
        """Validate ingress connectivity for a service."""
        try:
            resolution = await self.resolve_service(service_name, ResolutionStrategy.INGRESS)
            
            # Test connectivity
            import urllib.request
            try:
                response = urllib.request.urlopen(resolution.endpoint_url, timeout=10)
                status_code = response.getcode()
                
                return {
                    "service": service_name,
                    "ingress_url": resolution.endpoint_url,
                    "status": "accessible" if status_code < 400 else "error",
                    "status_code": status_code,
                    "resolution_strategy": resolution.resolution_strategy.value
                }
            except Exception as e:
                return {
                    "service": service_name,
                    "ingress_url": resolution.endpoint_url,
                    "status": "unreachable",
                    "error": str(e),
                    "resolution_strategy": resolution.resolution_strategy.value
                }
                
        except Exception as e:
            return {
                "service": service_name,
                "status": "resolution_failed",
                "error": str(e)
            }
    
    def get_all_service_endpoints(self) -> Dict[str, ServiceEndpoint]:
        """Get all registered service endpoints."""
        return self.service_registry.copy()
    
    def get_all_ingress_configurations(self) -> Dict[str, IngressConfiguration]:
        """Get all registered ingress configurations."""
        return self.ingress_registry.copy()
    
    def get_all_storage_configurations(self) -> Dict[str, StorageConfiguration]:
        """Get all registered storage configurations."""
        return self.storage_registry.copy()
    
    async def setup_networking(self) -> Dict[str, Any]:
        """Setup networking components for all services."""
        results = {
            "ingress_created": [],
            "storage_classes_created": [],
            "services_resolved": [],
            "errors": []
        }
        
        try:
            # Create storage classes
            for storage_name, storage_config in self.storage_registry.items():
                success = await self.create_storage_class(storage_config)
                if success:
                    results["storage_classes_created"].append(storage_name)
                else:
                    results["errors"].append(f"Failed to create storage class: {storage_name}")
            
            # Create ingress configurations
            for ingress_name, ingress_config in self.ingress_registry.items():
                success = await self._create_ingress(ingress_config)
                if success:
                    results["ingress_created"].append(ingress_name)
                else:
                    results["errors"].append(f"Failed to create ingress: {ingress_name}")
            
            # Resolve all services
            for service_name in self.service_registry.keys():
                try:
                    resolution = await self.resolve_service(service_name)
                    results["services_resolved"].append({
                        "service": service_name,
                        "strategy": resolution.resolution_strategy.value,
                        "endpoint": resolution.endpoint_url
                    })
                except Exception as e:
                    results["errors"].append(f"Failed to resolve service {service_name}: {str(e)}")
            
            return results
            
        except Exception as e:
            results["errors"].append(f"Networking setup failed: {str(e)}")
            return results