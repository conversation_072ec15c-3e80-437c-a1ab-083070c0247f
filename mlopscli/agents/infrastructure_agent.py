"""Infrastructure Foundation Agent for K3S and Core Services."""

import asyncio
import logging
import subprocess
import time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from pathlib import Path

from mlopscli.agents.component_model import (
    ComponentRegistry, M<PERSON><PERSON><PERSON>Component, ComponentStatus, ComponentType,
    InstallationLocation, component_registry
)
from mlopscli.k3s.manage_k3s_installation import ManageK3SInstallation
from mlopscli.utils.system import run_command, is_command_available
from mlopscli.utils.kubernetes import is_component_installed, check_image_pull_secret
from mlopscli.utils.constants import INSTALLATION_LOCATION_LOCAL_STR, INSTALLATION_LOCATION_REMOTE_STR


@dataclass
class InfrastructureValidationResult:
    """Result of infrastructure validation checks."""
    component_name: str
    is_valid: bool
    issues: List[str]
    recommendations: List[str]
    resource_usage: Dict[str, Any]


class InfrastructureFoundationAgent:
    """
    Specialized agent for managing infrastructure foundation components.
    
    Responsibilities:
    - K3S Kubernetes cluster installation and management
    - Certificate management setup (cert-manager)
    - Ingress controller configuration
    - Docker registry configuration
    - System prerequisites validation
    - Infrastructure health monitoring
    """
    
    def __init__(self, installation_location: str = INSTALLATION_LOCATION_LOCAL_STR):
        self.installation_location = installation_location
        self.logger = logging.getLogger(f"{__name__}.InfrastructureAgent")
        self.k3s_manager = ManageK3SInstallation()
        self.managed_components = self._get_managed_components()
        
    def _get_managed_components(self) -> List[str]:
        """Get list of components managed by this agent."""
        infrastructure_components = component_registry.get_components_by_type(ComponentType.INFRASTRUCTURE)
        return [comp.name for comp in infrastructure_components]
    
    async def validate_prerequisites(self) -> Dict[str, InfrastructureValidationResult]:
        """
        Validate system prerequisites for infrastructure components.
        
        Returns:
            Dictionary mapping component names to validation results
        """
        results = {}
        
        for component_name in self.managed_components:
            component = component_registry.get_component(component_name)
            if not component:
                continue
                
            result = await self._validate_component_prerequisites(component)
            results[component_name] = result
            
        return results
    
    async def _validate_component_prerequisites(self, component: MLOpsComponent) -> InfrastructureValidationResult:
        """Validate prerequisites for a specific component."""
        issues = []
        recommendations = []
        resource_usage = {}
        
        # System-level validations
        if component.name == "k3s":
            issues.extend(self._validate_k3s_prerequisites())
            resource_usage = await self._get_k3s_resource_usage()
        elif component.name == "cert-manager":
            issues.extend(self._validate_cert_manager_prerequisites())
        
        # Common validations for all infrastructure components
        issues.extend(self._validate_common_prerequisites(component))
        
        # Generate recommendations based on issues
        if issues:
            recommendations = self._generate_recommendations(component, issues)
        
        return InfrastructureValidationResult(
            component_name=component.name,
            is_valid=len(issues) == 0,
            issues=issues,
            recommendations=recommendations,
            resource_usage=resource_usage
        )
    
    def _validate_k3s_prerequisites(self) -> List[str]:
        """Validate K3S-specific prerequisites."""
        issues = []
        
        # Check required commands
        required_commands = ["curl", "systemctl", "docker"]
        for cmd in required_commands:
            if not is_command_available(cmd):
                issues.append(f"Required command '{cmd}' not found")
        
        # Check system resources
        try:
            # Check available memory (minimum 2GB recommended)
            result = subprocess.run(
                ["free", "-m"], capture_output=True, text=True, timeout=10
            )
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if line.startswith('Mem:'):
                        available_mem = int(line.split()[1])
                        if available_mem < 2048:
                            issues.append(f"Insufficient memory: {available_mem}MB available, 2048MB recommended")
                        break
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError, ValueError):
            issues.append("Could not check system memory")
        
        # Check disk space (minimum 10GB)
        try:
            result = subprocess.run(
                ["df", "-h", "/"], capture_output=True, text=True, timeout=10
            )
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # Skip header
                if lines:
                    parts = lines[0].split()
                    available = parts[3]  # Available space
                    if 'G' in available:
                        available_gb = float(available.replace('G', ''))
                        if available_gb < 10:
                            issues.append(f"Insufficient disk space: {available} available, 10GB recommended")
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError, ValueError):
            issues.append("Could not check disk space")
        
        # Check if ports are available
        required_ports = [6443, 10250, 10251, 10252, 2379, 2380]
        busy_ports = self._check_port_availability(required_ports)
        if busy_ports:
            issues.append(f"Required ports are busy: {busy_ports}")
        
        return issues
    
    def _validate_cert_manager_prerequisites(self) -> List[str]:
        """Validate cert-manager prerequisites."""
        issues = []
        
        # Ensure K3S is running
        if not self._is_k3s_running():
            issues.append("K3S cluster must be running before installing cert-manager")
        
        # Check if kubectl is available and configured
        if not is_command_available("kubectl"):
            issues.append("kubectl command not available")
        else:
            try:
                result = subprocess.run(
                    ["kubectl", "cluster-info"], capture_output=True, text=True, timeout=10
                )
                if result.returncode != 0:
                    issues.append("kubectl not configured or cluster not accessible")
            except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
                issues.append("Could not verify kubectl configuration")
        
        # Check if helm is available
        if not is_command_available("helm"):
            issues.append("Helm package manager not available")
        
        return issues
    
    def _validate_common_prerequisites(self, component: MLOpsComponent) -> List[str]:
        """Validate common prerequisites for infrastructure components."""
        issues = []
        
        # Validate namespace requirements
        if component.namespace and component.namespace != "default":
            if not self._namespace_exists(component.namespace):
                issues.append(f"Namespace '{component.namespace}' does not exist and will be created")
        
        # Validate resource requirements
        if component.resource_requirements:
            resource_issues = self._validate_resource_requirements(component.resource_requirements)
            issues.extend(resource_issues)
        
        # Validate dependencies
        dependency_issues = component_registry.validate_dependencies(component.name)
        issues.extend(dependency_issues)
        
        return issues
    
    def _check_port_availability(self, ports: List[int]) -> List[int]:
        """Check which ports are already in use."""
        busy_ports = []
        
        for port in ports:
            try:
                result = subprocess.run(
                    ["netstat", "-ln", f"--tcp", f"--udp"],
                    capture_output=True, text=True, timeout=5
                )
                if result.returncode == 0 and f":{port}" in result.stdout:
                    busy_ports.append(port)
            except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
                # If netstat fails, assume port might be busy (conservative approach)
                self.logger.warning(f"Could not check port {port} availability")
        
        return busy_ports
    
    def _is_k3s_running(self) -> bool:
        """Check if K3S cluster is running."""
        try:
            result = subprocess.run(
                ["systemctl", "is-active", "k3s"], 
                capture_output=True, text=True, timeout=5
            )
            return result.returncode == 0 and "active" in result.stdout
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            return False
    
    def _namespace_exists(self, namespace: str) -> bool:
        """Check if a Kubernetes namespace exists."""
        try:
            result = subprocess.run(
                ["kubectl", "get", "namespace", namespace], 
                capture_output=True, text=True, timeout=10
            )
            return result.returncode == 0
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            return False
    
    def _validate_resource_requirements(self, requirements) -> List[str]:
        """Validate system can meet resource requirements."""
        issues = []
        
        # This is a simplified validation - in production you'd want more sophisticated checks
        try:
            # Check CPU cores
            result = subprocess.run(
                ["nproc"], capture_output=True, text=True, timeout=5
            )
            if result.returncode == 0:
                cpu_cores = int(result.stdout.strip())
                # Parse CPU requirement (e.g., "1000m" = 1 core)
                cpu_limit = requirements.cpu_limit
                if cpu_limit.endswith('m'):
                    required_millicores = int(cpu_limit[:-1])
                    required_cores = required_millicores / 1000
                    if cpu_cores < required_cores:
                        issues.append(f"Insufficient CPU: {cpu_cores} cores available, {required_cores} required")
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError, ValueError):
            self.logger.warning("Could not validate CPU requirements")
        
        return issues
    
    async def _get_k3s_resource_usage(self) -> Dict[str, Any]:
        """Get current K3S resource usage."""
        resource_usage = {}
        
        try:
            # Get node resource usage
            result = subprocess.run(
                ["kubectl", "top", "nodes", "--no-headers"], 
                capture_output=True, text=True, timeout=15
            )
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if line.strip():
                        parts = line.split()
                        if len(parts) >= 5:
                            node_name = parts[0]
                            cpu_usage = parts[1]
                            memory_usage = parts[3]
                            resource_usage[node_name] = {
                                "cpu_usage": cpu_usage,
                                "memory_usage": memory_usage
                            }
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            self.logger.warning("Could not get K3S resource usage")
        
        return resource_usage
    
    def _generate_recommendations(self, component: MLOpsComponent, issues: List[str]) -> List[str]:
        """Generate recommendations based on validation issues."""
        recommendations = []
        
        for issue in issues:
            if "memory" in issue.lower():
                recommendations.append("Consider adding more RAM or reducing other running processes")
            elif "disk space" in issue.lower():
                recommendations.append("Free up disk space or consider mounting additional storage")
            elif "port" in issue.lower():
                recommendations.append("Stop services using required ports or configure alternative ports")
            elif "kubectl" in issue.lower():
                recommendations.append("Install and configure kubectl for cluster access")
            elif "helm" in issue.lower():
                recommendations.append("Install Helm package manager")
            elif "namespace" in issue.lower():
                recommendations.append("Namespace will be created automatically during installation")
        
        # Component-specific recommendations
        if component.name == "k3s":
            recommendations.extend([
                "Ensure Docker is installed and running",
                "Run with sudo privileges for system-level operations",
                "Consider configuring firewall rules for K3S ports"
            ])
        elif component.name == "cert-manager":
            recommendations.extend([
                "Ensure K3S cluster is healthy before installing cert-manager",
                "Configure custom resource definitions (CRDs) during installation"
            ])
        
        return recommendations
    
    async def install_component(self, component_name: str) -> Tuple[bool, List[str]]:
        """
        Install a specific infrastructure component.
        
        Args:
            component_name: Name of the component to install
            
        Returns:
            Tuple of (success, error_messages)
        """
        component = component_registry.get_component(component_name)
        if not component:
            return False, [f"Component {component_name} not found"]
        
        if component.component_type != ComponentType.INFRASTRUCTURE:
            return False, [f"Component {component_name} is not an infrastructure component"]
        
        # Update component status
        component.status = ComponentStatus.INSTALLING
        
        try:
            if component_name == "k3s":
                success, errors = await self._install_k3s()
            elif component_name == "cert-manager":
                success, errors = await self._install_cert_manager()
            else:
                return False, [f"Installation not implemented for {component_name}"]
            
            if success:
                component.status = ComponentStatus.INSTALLED
                self.logger.info(f"Successfully installed {component_name}")
            else:
                component.status = ComponentStatus.FAILED
                self.logger.error(f"Failed to install {component_name}: {errors}")
            
            return success, errors
            
        except Exception as e:
            component.status = ComponentStatus.FAILED
            error_msg = f"Exception during {component_name} installation: {str(e)}"
            self.logger.error(error_msg)
            return False, [error_msg]
    
    async def _install_k3s(self) -> Tuple[bool, List[str]]:
        """Install K3S cluster."""
        try:
            self.logger.info("Starting K3S installation...")
            self.k3s_manager.install_k3s()
            
            # Wait for K3S to be ready
            max_wait = 300  # 5 minutes
            wait_time = 0
            while wait_time < max_wait:
                if self._is_k3s_running():
                    break
                await asyncio.sleep(10)
                wait_time += 10
            else:
                return False, ["K3S failed to start within timeout period"]
            
            # Verify installation
            try:
                result = subprocess.run(
                    ["kubectl", "get", "nodes"], 
                    capture_output=True, text=True, timeout=10
                )
                if result.returncode != 0:
                    return False, ["K3S installed but kubectl verification failed"]
            except (subprocess.TimeoutExpired, subprocess.CalledProcessError) as e:
                return False, [f"K3S verification failed: {str(e)}"]
            
            return True, []
            
        except Exception as e:
            return False, [f"K3S installation failed: {str(e)}"]
    
    async def _install_cert_manager(self) -> Tuple[bool, List[str]]:
        """Install cert-manager."""
        try:
            self.logger.info("Starting cert-manager installation...")
            
            # Add Helm repository
            result = subprocess.run([
                "helm", "repo", "add", "jetstack", "https://charts.jetstack.io"
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode != 0:
                return False, [f"Failed to add jetstack helm repo: {result.stderr}"]
            
            # Update Helm repositories
            result = subprocess.run([
                "helm", "repo", "update"
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode != 0:
                return False, [f"Failed to update helm repos: {result.stderr}"]
            
            # Install cert-manager
            result = subprocess.run([
                "helm", "install", "cert-manager", "jetstack/cert-manager",
                "--namespace", "cert-manager",
                "--create-namespace",
                "--set", "installCRDs=true"
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode != 0:
                return False, [f"Failed to install cert-manager: {result.stderr}"]
            
            # Wait for cert-manager to be ready
            max_wait = 180  # 3 minutes
            wait_time = 0
            while wait_time < max_wait:
                result = subprocess.run([
                    "kubectl", "get", "pods", "-n", "cert-manager", "--no-headers"
                ], capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    # Check if all pods are running
                    lines = result.stdout.strip().split('\n')
                    all_running = True
                    for line in lines:
                        if line.strip() and "Running" not in line:
                            all_running = False
                            break
                    
                    if all_running and lines and lines[0].strip():
                        break
                
                await asyncio.sleep(10)
                wait_time += 10
            else:
                return False, ["cert-manager pods failed to start within timeout period"]
            
            return True, []
            
        except Exception as e:
            return False, [f"cert-manager installation failed: {str(e)}"]
    
    async def health_check(self, component_name: str) -> Dict[str, Any]:
        """
        Perform health check on infrastructure component.
        
        Args:
            component_name: Name of component to check
            
        Returns:
            Health check results
        """
        component = component_registry.get_component(component_name)
        if not component:
            return {"status": "error", "message": f"Component {component_name} not found"}
        
        if component_name == "k3s":
            return await self._health_check_k3s()
        elif component_name == "cert-manager":
            return await self._health_check_cert_manager()
        else:
            return {"status": "not_implemented", "message": f"Health check not implemented for {component_name}"}
    
    async def _health_check_k3s(self) -> Dict[str, Any]:
        """Health check for K3S cluster."""
        try:
            # Check if K3S service is active
            if not self._is_k3s_running():
                return {"status": "unhealthy", "message": "K3S service is not active"}
            
            # Check kubectl access
            result = subprocess.run([
                "kubectl", "get", "nodes", "--no-headers"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode != 0:
                return {"status": "unhealthy", "message": "Cannot access K3S cluster via kubectl"}
            
            # Check node status
            nodes = result.stdout.strip().split('\n')
            healthy_nodes = 0
            total_nodes = 0
            
            for node in nodes:
                if node.strip():
                    total_nodes += 1
                    if "Ready" in node:
                        healthy_nodes += 1
            
            if healthy_nodes == total_nodes and total_nodes > 0:
                return {
                    "status": "healthy",
                    "message": f"All {total_nodes} nodes are ready",
                    "nodes": total_nodes
                }
            else:
                return {
                    "status": "degraded",
                    "message": f"{healthy_nodes}/{total_nodes} nodes are ready"
                }
                
        except Exception as e:
            return {"status": "error", "message": f"Health check failed: {str(e)}"}
    
    async def _health_check_cert_manager(self) -> Dict[str, Any]:
        """Health check for cert-manager."""
        try:
            # Check if cert-manager pods are running
            result = subprocess.run([
                "kubectl", "get", "pods", "-n", "cert-manager", "--no-headers"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode != 0:
                return {"status": "unhealthy", "message": "Cannot check cert-manager pods"}
            
            pods = result.stdout.strip().split('\n')
            running_pods = 0
            total_pods = 0
            
            for pod in pods:
                if pod.strip():
                    total_pods += 1
                    if "Running" in pod and "1/1" in pod:
                        running_pods += 1
            
            if running_pods == total_pods and total_pods > 0:
                return {
                    "status": "healthy",
                    "message": f"All {total_pods} cert-manager pods are running",
                    "pods": total_pods
                }
            else:
                return {
                    "status": "degraded",
                    "message": f"{running_pods}/{total_pods} cert-manager pods are running"
                }
                
        except Exception as e:
            return {"status": "error", "message": f"Health check failed: {str(e)}"}
    
    def get_managed_components_status(self) -> Dict[str, ComponentStatus]:
        """Get status of all managed components."""
        status_map = {}
        for component_name in self.managed_components:
            component = component_registry.get_component(component_name)
            if component:
                status_map[component_name] = component.status
        return status_map