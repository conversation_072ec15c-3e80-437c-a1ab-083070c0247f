"""Authentication & Security Agent for Keycloak Identity Management."""

import asyncio
import json
import logging
import subprocess
import time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from pathlib import Path

from mlopscli.agents.component_model import (
    ComponentRegistry, M<PERSON><PERSON><PERSON><PERSON>omponent, ComponentStatus, ComponentType,
    component_registry
)
from mlopscli.kast.keycloak import Keycloak
from mlopscli.utils.system import run_command, run_command_with_output
from mlopscli.utils.kubernetes import create_secret, is_component_installed
from mlopscli.utils.constants import INSTALLATION_LOCATION_LOCAL_STR, INSTALLATION_LOCATION_REMOTE_STR


@dataclass
class AuthenticationValidationResult:
    """Result of authentication component validation."""
    component_name: str
    is_valid: bool
    issues: List[str]
    recommendations: List[str]
    security_assessment: Dict[str, Any]


@dataclass
class KeycloakRealm:
    """Keycloak realm configuration."""
    name: str
    enabled: bool = True
    display_name: Optional[str] = None
    clients: List[Dict[str, Any]] = None
    users: List[Dict[str, Any]] = None
    roles: List[Dict[str, Any]] = None


@dataclass
class KeycloakClient:
    """Keycloak client configuration."""
    client_id: str
    name: str
    protocol: str = "openid-connect"
    public_client: bool = False
    redirect_uris: List[str] = None
    web_origins: List[str] = None
    service_accounts_enabled: bool = False


class AuthenticationSecurityAgent:
    """
    Specialized agent for managing authentication and security components.
    
    Responsibilities:
    - Keycloak identity provider installation and configuration
    - OIDC/SAML integration setup
    - User management and role-based access control
    - Security policy enforcement
    - SSL/TLS certificate management for auth services
    - Integration with other MLOps components for authentication
    """
    
    def __init__(self, installation_location: str = INSTALLATION_LOCATION_LOCAL_STR):
        self.installation_location = installation_location
        self.logger = logging.getLogger(f"{__name__}.AuthenticationAgent")
        self.keycloak_manager = Keycloak()
        self.managed_components = self._get_managed_components()
        
    def _get_managed_components(self) -> List[str]:
        """Get list of components managed by this agent."""
        auth_components = component_registry.get_components_by_type(ComponentType.AUTHENTICATION)
        return [comp.name for comp in auth_components]
    
    async def validate_prerequisites(self) -> Dict[str, AuthenticationValidationResult]:
        """
        Validate prerequisites for authentication components.
        
        Returns:
            Dictionary mapping component names to validation results
        """
        results = {}
        
        for component_name in self.managed_components:
            component = component_registry.get_component(component_name)
            if not component:
                continue
                
            result = await self._validate_component_prerequisites(component)
            results[component_name] = result
            
        return results
    
    async def _validate_component_prerequisites(self, component: MLOpsComponent) -> AuthenticationValidationResult:
        """Validate prerequisites for authentication component."""
        issues = []
        recommendations = []
        security_assessment = {}
        
        if component.name == "keycloak":
            issues.extend(await self._validate_keycloak_prerequisites())
            security_assessment = await self._assess_keycloak_security()
        
        # Common authentication validations
        issues.extend(self._validate_common_auth_prerequisites(component))
        
        # Generate recommendations
        if issues:
            recommendations = self._generate_auth_recommendations(component, issues)
        
        return AuthenticationValidationResult(
            component_name=component.name,
            is_valid=len(issues) == 0,
            issues=issues,
            recommendations=recommendations,
            security_assessment=security_assessment
        )
    
    async def _validate_keycloak_prerequisites(self) -> List[str]:
        """Validate Keycloak-specific prerequisites."""
        issues = []
        
        # Check if K3S is running
        try:
            result = subprocess.run([
                "kubectl", "cluster-info"
            ], capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                issues.append("Kubernetes cluster is not accessible")
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            issues.append("Cannot verify Kubernetes cluster status")
        
        # Check PostgreSQL dependency
        postgres_component = component_registry.get_component("postgresql")
        if not postgres_component or postgres_component.status != ComponentStatus.INSTALLED:
            issues.append("PostgreSQL database is required but not installed")
        
        # Check if authentication namespace exists or can be created
        try:
            result = subprocess.run([
                "kubectl", "get", "namespace", "authentication"
            ], capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                # Namespace doesn't exist, will be created during installation
                self.logger.info("Authentication namespace will be created")
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            issues.append("Cannot verify namespace status")
        
        # Check if cert-manager is available for TLS
        cert_manager_component = component_registry.get_component("cert-manager")
        if not cert_manager_component or cert_manager_component.status != ComponentStatus.INSTALLED:
            issues.append("cert-manager is required for SSL/TLS certificates")
        
        # Validate resource requirements
        issues.extend(await self._validate_keycloak_resources())
        
        return issues
    
    async def _validate_keycloak_resources(self) -> List[str]:
        """Validate system resources for Keycloak."""
        issues = []
        
        try:
            # Check available memory for Keycloak (minimum 512MB recommended)
            result = subprocess.run([
                "kubectl", "top", "nodes", "--no-headers"
            ], capture_output=True, text=True, timeout=15)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if line.strip():
                        parts = line.split()
                        if len(parts) >= 4:
                            memory_usage = parts[3]  # Memory usage percentage
                            if memory_usage.replace('%', '').isdigit():
                                usage_pct = int(memory_usage.replace('%', ''))
                                if usage_pct > 80:
                                    issues.append(f"High memory usage ({usage_pct}%) may affect Keycloak performance")
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            self.logger.warning("Could not check cluster resource usage")
        
        return issues
    
    def _validate_common_auth_prerequisites(self, component: MLOpsComponent) -> List[str]:
        """Validate common prerequisites for authentication components."""
        issues = []
        
        # Validate dependencies
        dependency_issues = component_registry.validate_dependencies(component.name)
        issues.extend(dependency_issues)
        
        # Check Helm availability
        try:
            result = subprocess.run([
                "helm", "version", "--short"
            ], capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                issues.append("Helm package manager is not available")
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            issues.append("Cannot verify Helm installation")
        
        return issues
    
    async def _assess_keycloak_security(self) -> Dict[str, Any]:
        """Assess security configuration for Keycloak."""
        assessment = {
            "ssl_enabled": False,
            "database_encryption": False,
            "admin_console_secured": False,
            "password_policy_strength": "unknown",
            "session_security": "unknown",
            "recommendations": []
        }
        
        # Check if TLS is configured
        cert_manager_component = component_registry.get_component("cert-manager")
        if cert_manager_component and cert_manager_component.status == ComponentStatus.INSTALLED:
            assessment["ssl_enabled"] = True
        else:
            assessment["recommendations"].append("Enable SSL/TLS with cert-manager")
        
        # Check database encryption
        postgres_component = component_registry.get_component("postgresql")
        if postgres_component and postgres_component.status == ComponentStatus.INSTALLED:
            assessment["database_encryption"] = True  # Assume encrypted connection
        else:
            assessment["recommendations"].append("Ensure database connections are encrypted")
        
        # Security recommendations
        assessment["recommendations"].extend([
            "Configure strong password policies",
            "Enable two-factor authentication",
            "Set up session timeout policies",
            "Configure rate limiting for login attempts",
            "Enable audit logging for security events"
        ])
        
        return assessment
    
    def _generate_auth_recommendations(self, component: MLOpsComponent, issues: List[str]) -> List[str]:
        """Generate recommendations for authentication issues."""
        recommendations = []
        
        for issue in issues:
            if "postgresql" in issue.lower():
                recommendations.append("Install PostgreSQL database before configuring Keycloak")
            elif "cert-manager" in issue.lower():
                recommendations.append("Install cert-manager for SSL certificate management")
            elif "kubernetes" in issue.lower():
                recommendations.append("Ensure Kubernetes cluster is running and accessible")
            elif "memory" in issue.lower():
                recommendations.append("Consider scaling cluster nodes or reducing other workloads")
            elif "helm" in issue.lower():
                recommendations.append("Install Helm package manager for Keycloak deployment")
        
        # Keycloak-specific recommendations
        if component.name == "keycloak":
            recommendations.extend([
                "Configure persistent storage for Keycloak data",
                "Set up backup strategy for authentication data",
                "Configure monitoring and alerting for authentication services",
                "Plan for high availability if required",
                "Document authentication integration procedures"
            ])
        
        return recommendations
    
    async def install_component(self, component_name: str, config: Optional[Dict[str, Any]] = None) -> Tuple[bool, List[str]]:
        """
        Install authentication component.
        
        Args:
            component_name: Name of component to install
            config: Additional configuration parameters
            
        Returns:
            Tuple of (success, error_messages)
        """
        component = component_registry.get_component(component_name)
        if not component:
            return False, [f"Component {component_name} not found"]
        
        if component.component_type != ComponentType.AUTHENTICATION:
            return False, [f"Component {component_name} is not an authentication component"]
        
        # Update component status
        component.status = ComponentStatus.INSTALLING
        
        try:
            if component_name == "keycloak":
                success, errors = await self._install_keycloak(config or {})
            else:
                return False, [f"Installation not implemented for {component_name}"]
            
            if success:
                component.status = ComponentStatus.INSTALLED
                self.logger.info(f"Successfully installed {component_name}")
            else:
                component.status = ComponentStatus.FAILED
                self.logger.error(f"Failed to install {component_name}: {errors}")
            
            return success, errors
            
        except Exception as e:
            component.status = ComponentStatus.FAILED
            error_msg = f"Exception during {component_name} installation: {str(e)}"
            self.logger.error(error_msg)
            return False, [error_msg]
    
    async def _install_keycloak(self, config: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Install Keycloak identity provider."""
        try:
            self.logger.info("Starting Keycloak installation...")
            
            # Create namespace if it doesn't exist
            result = subprocess.run([
                "kubectl", "create", "namespace", "authentication", "--dry-run=client", "-o", "yaml"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                subprocess.run([
                    "kubectl", "apply", "-f", "-"
                ], input=result.stdout, text=True, timeout=30)
            
            # Create PostgreSQL connection secret
            postgres_password = config.get("postgres_password", "defaultpassword")
            secret_success = create_secret(
                "keycloak-db-secret",
                "authentication",
                {
                    "username": "keycloak",
                    "password": postgres_password,
                    "database": "keycloak"
                }
            )
            
            if not secret_success:
                return False, ["Failed to create database secret for Keycloak"]
            
            # Add Bitnami Helm repository
            result = subprocess.run([
                "helm", "repo", "add", "bitnami", "https://charts.bitnami.com/bitnami"
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode != 0:
                self.logger.warning(f"Helm repo add warning: {result.stderr}")
            
            # Update Helm repositories
            result = subprocess.run([
                "helm", "repo", "update"
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode != 0:
                return False, [f"Failed to update helm repos: {result.stderr}"]
            
            # Prepare Keycloak values
            keycloak_values = self._prepare_keycloak_values(config)
            
            # Install Keycloak
            helm_cmd = [
                "helm", "install", "keycloak", "bitnami/keycloak",
                "--namespace", "authentication",
                "--set-string", "auth.adminUser=admin",
                "--set-string", f"auth.adminPassword={config.get('admin_password', 'admin123')}",
                "--set", "postgresql.enabled=false",
                "--set-string", "externalDatabase.host=postgresql.sql-store.svc.cluster.local",
                "--set-string", "externalDatabase.user=keycloak",
                "--set-string", "externalDatabase.database=keycloak",
                "--set-string", "externalDatabase.existingSecret=keycloak-db-secret",
                "--set-string", "externalDatabase.existingSecretPasswordKey=password"
            ]
            
            # Add TLS configuration if cert-manager is available
            cert_manager_component = component_registry.get_component("cert-manager")
            if cert_manager_component and cert_manager_component.status == ComponentStatus.INSTALLED:
                helm_cmd.extend([
                    "--set", "ingress.enabled=true",
                    "--set", f"ingress.hostname=keycloak.{config.get('domain', 'dpsc')}",
                    "--set", "ingress.tls=true",
                    "--set", "ingress.annotations.cert-manager\\.io/cluster-issuer=external-ca-issuer"
                ])
            
            result = subprocess.run(helm_cmd, capture_output=True, text=True, timeout=600)
            
            if result.returncode != 0:
                return False, [f"Failed to install Keycloak: {result.stderr}"]
            
            # Wait for Keycloak to be ready
            max_wait = 300  # 5 minutes
            wait_time = 0
            while wait_time < max_wait:
                ready = await self._check_keycloak_ready()
                if ready:
                    break
                await asyncio.sleep(15)
                wait_time += 15
            else:
                return False, ["Keycloak failed to become ready within timeout period"]
            
            # Configure default realm and clients
            if config.get("configure_defaults", True):
                await self._configure_keycloak_defaults(config)
            
            self.logger.info("Keycloak installation completed successfully")
            return True, []
            
        except Exception as e:
            return False, [f"Keycloak installation failed: {str(e)}"]
    
    def _prepare_keycloak_values(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare Helm values for Keycloak installation."""
        values = {
            "replicaCount": 1,
            "auth": {
                "adminUser": "admin",
                "adminPassword": config.get("admin_password", "admin123")
            },
            "postgresql": {
                "enabled": False
            },
            "externalDatabase": {
                "host": "postgresql.sql-store.svc.cluster.local",
                "port": 5432,
                "user": "keycloak",
                "database": "keycloak",
                "existingSecret": "keycloak-db-secret",
                "existingSecretPasswordKey": "password"
            },
            "resources": {
                "limits": {
                    "cpu": "1000m",
                    "memory": "1Gi"
                },
                "requests": {
                    "cpu": "500m",
                    "memory": "512Mi"
                }
            }
        }
        
        return values
    
    async def _check_keycloak_ready(self) -> bool:
        """Check if Keycloak pods are ready."""
        try:
            result = subprocess.run([
                "kubectl", "get", "pods", "-n", "authentication", 
                "-l", "app.kubernetes.io/name=keycloak",
                "--no-headers"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode != 0:
                return False
            
            pods = result.stdout.strip().split('\n')
            for pod in pods:
                if pod.strip():
                    if "Running" not in pod or "1/1" not in pod:
                        return False
            
            return len(pods) > 0 and pods[0].strip()
            
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            return False
    
    async def _configure_keycloak_defaults(self, config: Dict[str, Any]) -> None:
        """Configure default Keycloak realms and clients."""
        try:
            # This would typically involve using the Keycloak admin API
            # For now, we'll log the configuration that should be applied
            default_realm = KeycloakRealm(
                name="mlops",
                display_name="MLOps Platform",
                enabled=True
            )
            
            default_clients = [
                KeycloakClient(
                    client_id="argo-workflows",
                    name="Argo Workflows",
                    redirect_uris=["https://argo.dpsc/*"],
                    web_origins=["https://argo.dpsc"]
                ),
                KeycloakClient(
                    client_id="grafana",
                    name="Grafana",
                    redirect_uris=["https://grafana.dpsc/*"],
                    web_origins=["https://grafana.dpsc"]
                ),
                KeycloakClient(
                    client_id="lakefs",
                    name="LakeFS",
                    redirect_uris=["https://lakefs.dpsc/*"],
                    web_origins=["https://lakefs.dpsc"]
                )
            ]
            
            self.logger.info(f"Default realm configuration prepared: {default_realm.name}")
            self.logger.info(f"Default clients configured: {len(default_clients)}")
            
            # In a full implementation, these would be configured via Keycloak API
            
        except Exception as e:
            self.logger.error(f"Failed to configure Keycloak defaults: {str(e)}")
    
    async def health_check(self, component_name: str) -> Dict[str, Any]:
        """
        Perform health check on authentication component.
        
        Args:
            component_name: Name of component to check
            
        Returns:
            Health check results
        """
        component = component_registry.get_component(component_name)
        if not component:
            return {"status": "error", "message": f"Component {component_name} not found"}
        
        if component_name == "keycloak":
            return await self._health_check_keycloak()
        else:
            return {"status": "not_implemented", "message": f"Health check not implemented for {component_name}"}
    
    async def _health_check_keycloak(self) -> Dict[str, Any]:
        """Health check for Keycloak."""
        try:
            # Check pod status
            result = subprocess.run([
                "kubectl", "get", "pods", "-n", "authentication",
                "-l", "app.kubernetes.io/name=keycloak",
                "--no-headers"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode != 0:
                return {"status": "unhealthy", "message": "Cannot check Keycloak pods"}
            
            pods = result.stdout.strip().split('\n')
            running_pods = 0
            total_pods = 0
            
            for pod in pods:
                if pod.strip():
                    total_pods += 1
                    if "Running" in pod and "1/1" in pod:
                        running_pods += 1
            
            if running_pods == total_pods and total_pods > 0:
                # Check service accessibility
                service_check = await self._check_keycloak_service()
                if service_check:
                    return {
                        "status": "healthy",
                        "message": f"All {total_pods} Keycloak pods are running and service is accessible",
                        "pods": total_pods,
                        "service_accessible": True
                    }
                else:
                    return {
                        "status": "degraded",
                        "message": f"Keycloak pods are running but service is not accessible",
                        "pods": total_pods,
                        "service_accessible": False
                    }
            else:
                return {
                    "status": "degraded",
                    "message": f"{running_pods}/{total_pods} Keycloak pods are running"
                }
                
        except Exception as e:
            return {"status": "error", "message": f"Health check failed: {str(e)}"}
    
    async def _check_keycloak_service(self) -> bool:
        """Check if Keycloak service is accessible."""
        try:
            result = subprocess.run([
                "kubectl", "get", "service", "-n", "authentication",
                "-l", "app.kubernetes.io/name=keycloak",
                "--no-headers"
            ], capture_output=True, text=True, timeout=10)
            
            return result.returncode == 0 and result.stdout.strip()
            
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            return False
    
    def get_managed_components_status(self) -> Dict[str, ComponentStatus]:
        """Get status of all managed authentication components."""
        status_map = {}
        for component_name in self.managed_components:
            component = component_registry.get_component(component_name)
            if component:
                status_map[component_name] = component.status
        return status_map
    
    async def configure_oidc_client(self, client_config: KeycloakClient) -> Tuple[bool, str]:
        """
        Configure OIDC client in Keycloak.
        
        Args:
            client_config: Client configuration
            
        Returns:
            Tuple of (success, client_secret_or_error)
        """
        try:
            # In a full implementation, this would use the Keycloak Admin API
            # to create and configure OIDC clients
            self.logger.info(f"Configuring OIDC client: {client_config.client_id}")
            
            # Mock implementation - in reality would make API calls to Keycloak
            client_secret = f"secret-{client_config.client_id}-{int(time.time())}"
            
            return True, client_secret
            
        except Exception as e:
            return False, f"Failed to configure OIDC client: {str(e)}"
    
    async def create_user(self, username: str, email: str, password: str, roles: List[str] = None) -> Tuple[bool, str]:
        """
        Create user in Keycloak.
        
        Args:
            username: Username
            email: User email
            password: User password
            roles: List of roles to assign
            
        Returns:
            Tuple of (success, user_id_or_error)
        """
        try:
            # In a full implementation, this would use the Keycloak Admin API
            self.logger.info(f"Creating user: {username}")
            
            # Mock implementation
            user_id = f"user-{username}-{int(time.time())}"
            
            return True, user_id
            
        except Exception as e:
            return False, f"Failed to create user: {str(e)}"