"""MLOps CLI Specialized Agent System.

This package provides a comprehensive agent-based architecture for managing MLOps platform
components with specialized sub-agents responsible for different aspects of the installation
and management process.

Agent Architecture:
- InfrastructureFoundationAgent: K3S, cert-manager, core infrastructure
- AuthenticationSecurityAgent: Keycloak, identity management, security
- StorageDatabaseAgent: PostgreSQL, MinIO, ClickHouse data layer
- CICDDevOpsAgent: GitLab Runner, Argo Workflows, Dagger automation
- ServiceIngressResolver: Service resolution, ingress, networking
- MLOpsAgentOrchestrator: Main coordinator for all agents

Each agent ensures requirements validation for its assigned components and provides
specialized expertise for installation, configuration, monitoring, and troubleshooting.
"""

from .component_model import (
    ComponentRegistry,
    MLOpsComponent,
    ComponentStatus,
    ComponentType,
    InstallationLocation,
    ResourceRequirements,
    HealthCheck,
    ComponentDependency,
    ComponentConfiguration,
    component_registry,
    create_standard_components
)

from .infrastructure_agent import (
    InfrastructureFoundationAgent,
    InfrastructureValidationResult
)

from .authentication_agent import (
    AuthenticationSecurityAgent,
    AuthenticationValidationResult,
    KeycloakRealm,
    KeycloakClient
)

from .storage_agent import (
    StorageDatabaseAgent,
    StorageValidationResult,
    DatabaseConfiguration,
    S3BucketConfiguration,
    ClickHouseConfiguration
)

from .cicd_agent import (
    CICDDevOpsAgent,
    CICDValidationResult,
    GitLabRunnerConfig,
    ArgoWorkflowConfig
)

from .service_resolver import (
    ServiceIngressResolver,
    ServiceType,
    ResolutionStrategy,
    ServiceEndpoint,
    IngressConfiguration,
    StorageConfiguration,
    ServiceResolutionResult
)

from .kastctl_agent import (
    KastctlAgent,
    KPackType,
    KPackStatus,
    KPackConfiguration,
    KastctlValidationResult,
    KPackDeploymentResult
)

from .orchestrator import (
    MLOpsAgentOrchestrator,
    OrchestrationPhase,
    AgentStatus,
    OrchestrationResult,
    AgentCoordinationState
)

from .service_registry_agent import (
    ServiceRegistryAgent,
    ServiceDefinition
)

__all__ = [
    # Component Model
    "ComponentRegistry",
    "MLOpsComponent", 
    "ComponentStatus",
    "ComponentType",
    "InstallationLocation",
    "ResourceRequirements",
    "HealthCheck",
    "ComponentDependency",
    "ComponentConfiguration",
    "component_registry",
    "create_standard_components",
    
    # Infrastructure Agent
    "InfrastructureFoundationAgent",
    "InfrastructureValidationResult",
    
    # Authentication Agent
    "AuthenticationSecurityAgent",
    "AuthenticationValidationResult",
    "KeycloakRealm",
    "KeycloakClient",
    
    # Storage Agent
    "StorageDatabaseAgent",
    "StorageValidationResult", 
    "DatabaseConfiguration",
    "S3BucketConfiguration",
    "ClickHouseConfiguration",
    
    # CI/CD Agent
    "CICDDevOpsAgent",
    "CICDValidationResult",
    "GitLabRunnerConfig",
    "ArgoWorkflowConfig",
    
    # Service Resolver
    "ServiceIngressResolver",
    "ServiceType",
    "ResolutionStrategy",
    "ServiceEndpoint",
    "IngressConfiguration",
    "StorageConfiguration", 
    "ServiceResolutionResult",
    
    # KASTCTL Agent
    "KastctlAgent",
    "KPackType",
    "KPackStatus", 
    "KPackConfiguration",
    "KastctlValidationResult",
    "KPackDeploymentResult",
    
    # Orchestrator
    "MLOpsAgentOrchestrator",
    "OrchestrationPhase",
    "AgentStatus",
    "OrchestrationResult",
    "AgentCoordinationState",
    
    # Service Registry Agent
    "ServiceRegistryAgent",
    "ServiceDefinition"
]

# Version information
__version__ = "1.0.0"
__author__ = "MLOps CLI Team"
__description__ = "Specialized agent system for MLOps platform management"