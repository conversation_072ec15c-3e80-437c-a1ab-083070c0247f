# mlopscli/kast/manage_kserve_installation.py

import asyncio
import os
import tempfile
import yaml
from pathlib import Path
from typing import Dict, List, Optional

from mlopscli.kast.manage_installation_base import ManageInstallationBase
from mlopscli.utils.constants import (
    INSTALLATION_LOCATION_LOCAL_STR,
    INSTALLATION_LOCATION_REMOTE_STR,
    TDS_PROJECT_DOMAIN_NAME,
    DPSC_DOMAIN_NAME
)
from mlopscli.utils.installation_ingress_resolver import (
    configure_ingress_class,
    get_kserve_ingress_config
)
from mlopscli.utils.kubernetes import (
    is_helm_chart_installed,
    run_command,
    is_component_installed
)
from mlopscli.utils.logger import get_logger

logger = get_logger(__name__)

# KServe Configuration Constants
KSERVE_NAMESPACE = "kserve-test"
KNATIVE_SERVING_NAMESPACE = "knative-serving"
KNATIVE_EVENTING_NAMESPACE = "knative-eventing"
CERT_MANAGER_NAMESPACE = "cert-manager"

KSERVE_VERSION = "v0.11.2"
KNATIVE_SERVING_VERSION = "knative-v1.11.2"
KNATIVE_EVENTING_VERSION = "knative-v1.11.0"
CERT_MANAGER_VERSION = "v1.13.2"

# KServe Configuration with OTEL and ClickHouse observability
KSERVE_CONFIG = {
    "predictors": {
        "tensorflow": {
            "image": "tensorflow/serving:2.13.0",
            "defaultImageVersion": "2.13.0",
            "supportedFrameworks": ["tensorflow"],
            "multiModelServer": False
        },
        "sklearn": {
            "image": "kserve/sklearnserver:v0.11.2",
            "defaultImageVersion": "v0.11.2", 
            "supportedFrameworks": ["sklearn"],
            "multiModelServer": False
        },
        "pytorch": {
            "image": "kserve/torchserve:0.8.1",
            "defaultImageVersion": "0.8.1-gpu",
            "supportedFrameworks": ["pytorch"],
            "multiModelServer": False
        },
        "xgboost": {
            "image": "kserve/xgbserver:v0.11.2",
            "defaultImageVersion": "v0.11.2",
            "supportedFrameworks": ["xgboost"],
            "multiModelServer": False
        },
        "triton": {
            "image": "nvcr.io/nvidia/tritonserver:23.05-py3",
            "defaultImageVersion": "23.05-py3",
            "supportedFrameworks": ["tensorrt", "tensorflow", "onnx", "pytorch"],
            "multiModelServer": True
        },
        "huggingface": {
            "image": "kserve/huggingfaceserver:v0.11.2",
            "defaultImageVersion": "v0.11.2",
            "supportedFrameworks": ["huggingface"],
            "multiModelServer": False
        }
    },
    "transformers": {
        "feast": {
            "image": "kserve/feast-transformer:v0.11.2",
            "defaultImageVersion": "v0.11.2"
        }
    },
    "explainers": {
        "alibi": {
            "image": "kserve/alibi-explainer:v0.11.2",
            "defaultImageVersion": "v0.11.2"
        },
        "aix": {
            "image": "kserve/aix-explainer:v0.11.2",
            "defaultImageVersion": "v0.11.2"
        },
        "art": {
            "image": "kserve/art-explainer:v0.11.2",
            "defaultImageVersion": "v0.11.2"
        }
    },
    "storageInitializer": {
        "image": "kserve/storage-initializer:v0.11.2",
        "memoryRequest": "100Mi",
        "memoryLimit": "1Gi",
        "cpuRequest": "100m", 
        "cpuLimit": "1"
    },
    "credentials": {
        "gcs": {
            "gcsCredentialFileName": "gcloud-application-credentials.json"
        },
        "s3": {
            "s3AccessKeyIDName": "AWS_ACCESS_KEY_ID",
            "s3SecretAccessKeyName": "AWS_SECRET_ACCESS_KEY"
        },
        "azure": {
            "azureClientIdName": "AZURE_CLIENT_ID",
            "azureClientSecretName": "AZURE_CLIENT_SECRET",
            "azureTenantIdName": "AZURE_TENANT_ID",
            "azureSubscriptionIdName": "AZURE_SUBSCRIPTION_ID"
        }
    },
    "ingress": {
        "ingressGateway": "knative-serving/knative-ingress-gateway",
        "ingressService": "istio-ingressgateway.istio-system.svc.cluster.local",
        "localGateway": "knative-serving/knative-local-gateway", 
        "localGatewayService": "knative-local-gateway.istio-system.svc.cluster.local",
        "ingressDomain": "example.com",
        "ingressClassName": "istio",
        "domainTemplate": "{{.Name}}-{{.Namespace}}.{{.IngressDomain}}",
        "urlScheme": "https"
    }
}

# OpenTelemetry configuration for KServe
OTEL_CONFIG = {
    "endpoint": "jaeger-collector.monitoring.svc.cluster.local:14268",
    "service_name": "kserve-models",
    "service_version": "v0.11.2",
    "resource_attributes": {
        "service.namespace": "kserve-test",
        "deployment.environment": "production"
    },
    "sampling_rate": 0.1,
    "batch_processor": {
        "timeout": "5s",
        "batch_size": 256,
        "max_queue_size": 2048
    }
}

# ClickHouse configuration for KServe logging
CLICKHOUSE_CONFIG = {
    "host": "clickhouse.monitoring.svc.cluster.local",
    "port": 8123,
    "database": "kserve_logs",
    "tables": {
        "inference_logs": "inference_requests",
        "model_metrics": "model_performance", 
        "error_logs": "model_errors"
    },
    "batch_size": 1000,
    "flush_interval": "30s"
}


class ManageKServeInstallation(ManageInstallationBase):
    """Manages KServe installation and configuration with OTEL and ClickHouse observability."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._logger = logger

    def install_kserve(
        self,
        installation_location: str = INSTALLATION_LOCATION_LOCAL_STR,
        ingress_class_name: str = "",
        domain_name: str = "",
        clickhouse_host: str = "clickhouse.monitoring.svc.cluster.local",
        clickhouse_port: int = 8123,
        clickhouse_database: str = "kserve_logs",
        otel_endpoint: str = "jaeger-collector.monitoring.svc.cluster.local:14268",
        enable_istio: bool = True
    ) -> None:
        """
        Install KServe with Knative Serving and configure OTEL + ClickHouse observability.
        
        Args:
            installation_location: Installation location (local/remote)
            ingress_class_name: Ingress class to use
            domain_name: Domain name for the installation
            clickhouse_host: ClickHouse host for logging
            clickhouse_port: ClickHouse port
            clickhouse_database: ClickHouse database name
            otel_endpoint: OpenTelemetry collector endpoint  
            enable_istio: Whether to enable Istio service mesh
        """
        self._logger.info("Starting KServe installation with OTEL and ClickHouse observability...")
        
        # Auto-detect ingress class if not provided
        if not ingress_class_name:
            try:
                ingress_class_name = configure_ingress_class()
                self._logger.info(f"Auto-detected ingress class: {ingress_class_name}")
            except Exception as e:
                self._logger.error(f"Failed to auto-detect ingress class: {e}")
                ingress_class_name = "nginx"
        
        # Install prerequisites
        self._install_cert_manager()
        self._install_knative_serving(enable_istio=enable_istio)
        
        # Create KServe namespace and RBAC
        self._create_kserve_namespace()
        
        # Install KServe
        self._install_kserve_components(
            installation_location=installation_location,
            ingress_class_name=ingress_class_name,
            domain_name=domain_name
        )
        
        # Configure observability
        self._configure_otel_observability(otel_endpoint)
        self._configure_clickhouse_logging(
            clickhouse_host=clickhouse_host,
            clickhouse_port=clickhouse_port,
            clickhouse_database=clickhouse_database
        )
        
        # Create storage secrets for model loading
        self._create_storage_secrets()
        
        # Deploy sample inference services
        self._deploy_sample_models()
        
        self._logger.info("KServe installation completed successfully!")

    def _install_cert_manager(self) -> None:
        """Install cert-manager for TLS certificate management."""
        self._logger.info("Installing cert-manager...")
        
        if is_component_installed(type="namespace", name=CERT_MANAGER_NAMESPACE):
            self._logger.info("cert-manager is already installed")
            return
        
        # Install cert-manager
        command = (
            f"kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/"
            f"{CERT_MANAGER_VERSION}/cert-manager.yaml"
        )
        run_command(command=command, check=True)
        
        # Wait for cert-manager to be ready
        self._wait_for_deployment("cert-manager", CERT_MANAGER_NAMESPACE)
        self._logger.info("cert-manager installation completed")

    def _install_knative_serving(self, enable_istio: bool = True) -> None:
        """Install Knative Serving."""
        self._logger.info("Installing Knative Serving...")
        
        if is_component_installed(type="namespace", name=KNATIVE_SERVING_NAMESPACE):
            self._logger.info("Knative Serving is already installed")
            return
        
        # Install Knative Serving CRDs
        command = (
            f"kubectl apply -f https://github.com/knative/serving/releases/download/"
            f"{KNATIVE_SERVING_VERSION}/serving-crds.yaml"
        )
        run_command(command=command, check=True)
        
        # Install Knative Serving core
        command = (
            f"kubectl apply -f https://github.com/knative/serving/releases/download/"
            f"{KNATIVE_SERVING_VERSION}/serving-core.yaml"
        )
        run_command(command=command, check=True)
        
        if enable_istio:
            # Install Knative Istio controller
            command = (
                f"kubectl apply -f https://github.com/knative/net-istio/releases/download/"
                f"{KNATIVE_SERVING_VERSION}/net-istio.yaml"
            )
            run_command(command=command, check=True)
        else:
            # Install Kourier networking layer
            command = (
                f"kubectl apply -f https://github.com/knative/net-kourier/releases/download/"
                f"{KNATIVE_SERVING_VERSION}/kourier.yaml"
            )
            run_command(command=command, check=True)
            
            # Configure Kourier as default networking layer
            command = (
                "kubectl patch configmap/config-network "
                "--namespace knative-serving "
                "--type merge --patch "
                "'{\"data\":{\"ingress-class\":\"kourier.ingress.networking.knative.dev\"}}'"
            )
            run_command(command=command, check=True)
        
        # Wait for Knative Serving to be ready
        self._wait_for_deployment("controller", KNATIVE_SERVING_NAMESPACE)
        self._logger.info("Knative Serving installation completed")

    def _create_kserve_namespace(self) -> None:
        """Create KServe namespace and RBAC."""
        self._logger.info(f"Creating KServe namespace: {KSERVE_NAMESPACE}")
        
        if not is_component_installed(type="namespace", name=KSERVE_NAMESPACE):
            command = f"kubectl create namespace {KSERVE_NAMESPACE}"
            run_command(command=command, check=True)
            
            # Label namespace for Istio injection
            label_command = (
                f"kubectl label namespace {KSERVE_NAMESPACE} "
                f"istio-injection=enabled --overwrite"
            )
            run_command(command=label_command, check=True)
        else:
            self._logger.info(f"Namespace {KSERVE_NAMESPACE} already exists")
        
        # Create service account and RBAC
        self._create_kserve_rbac()

    def _create_kserve_rbac(self) -> None:
        """Create KServe RBAC configuration."""
        rbac_config = f"""
apiVersion: v1
kind: ServiceAccount
metadata:
  name: kserve-sa
  namespace: {KSERVE_NAMESPACE}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: kserve-model-serving
rules:
- apiGroups: ["serving.kserve.io"]
  resources: ["inferenceservices", "trainedmodels"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["serving.knative.dev"]
  resources: ["services", "configurations", "revisions", "routes"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: [""]
  resources: ["pods", "services", "endpoints", "persistentvolumeclaims", "events", "configmaps", "secrets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: kserve-model-serving
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: kserve-model-serving
subjects:
- kind: ServiceAccount
  name: kserve-sa
  namespace: {KSERVE_NAMESPACE}
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write(rbac_config)
            rbac_file = f.name
        
        try:
            command = f"kubectl apply -f {rbac_file}"
            run_command(command=command, check=True)
            self._logger.info("KServe RBAC configuration applied")
        finally:
            os.unlink(rbac_file)

    def _install_kserve_components(
        self,
        installation_location: str,
        ingress_class_name: str,
        domain_name: str
    ) -> None:
        """Install KServe components."""
        self._logger.info("Installing KServe components...")
        
        # Install KServe CRDs
        command = (
            f"kubectl apply -f https://github.com/kserve/kserve/releases/download/"
            f"{KSERVE_VERSION}/kserve.yaml"
        )
        run_command(command=command, check=True)
        
        # Wait for KServe controller to be ready
        self._wait_for_deployment("kserve-controller-manager", "kserve-system")
        
        # Configure KServe with OTEL and ClickHouse
        self._configure_kserve_config(
            installation_location=installation_location,
            ingress_class_name=ingress_class_name,
            domain_name=domain_name
        )
        
        self._logger.info("KServe components installation completed")

    def _configure_kserve_config(
        self,
        installation_location: str,
        ingress_class_name: str,
        domain_name: str
    ) -> None:
        """Configure KServe with OTEL and ClickHouse integration."""
        effective_domain = domain_name or (
            TDS_PROJECT_DOMAIN_NAME if installation_location == INSTALLATION_LOCATION_REMOTE_STR 
            else DPSC_DOMAIN_NAME
        )
        
        # Update KServe configuration
        config = KSERVE_CONFIG.copy()
        config["ingress"]["ingressDomain"] = effective_domain
        config["ingress"]["ingressClassName"] = ingress_class_name
        
        # Add OTEL configuration
        config["observability"] = {
            "opentelemetry": OTEL_CONFIG,
            "clickhouse": CLICKHOUSE_CONFIG
        }
        
        config_yaml = yaml.dump(config, default_flow_style=False)
        
        # Apply configuration
        configmap_config = f"""
apiVersion: v1
kind: ConfigMap
metadata:
  name: inferenceservice-config
  namespace: kserve-system
data:
  predictors: |
{yaml.dump(config["predictors"], default_flow_style=False, indent=4)}
  transformers: |
{yaml.dump(config["transformers"], default_flow_style=False, indent=4)}
  explainers: |
{yaml.dump(config["explainers"], default_flow_style=False, indent=4)}
  storageInitializer: |
{yaml.dump(config["storageInitializer"], default_flow_style=False, indent=4)}
  credentials: |
{yaml.dump(config["credentials"], default_flow_style=False, indent=4)}
  ingress: |
{yaml.dump(config["ingress"], default_flow_style=False, indent=4)}
  observability: |
{yaml.dump(config["observability"], default_flow_style=False, indent=4)}
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write(configmap_config)
            config_file = f.name
        
        try:
            command = f"kubectl apply -f {config_file}"
            run_command(command=command, check=True)
            self._logger.info("KServe configuration applied")
        finally:
            os.unlink(config_file)

    def _configure_otel_observability(self, otel_endpoint: str) -> None:
        """Configure OpenTelemetry observability for KServe."""
        self._logger.info("Configuring OpenTelemetry observability...")
        
        otel_config = f"""
apiVersion: v1
kind: ConfigMap
metadata:
  name: kserve-otel-config
  namespace: {KSERVE_NAMESPACE}
data:
  config.yaml: |
    receivers:
      otlp:
        protocols:
          grpc:
            endpoint: 0.0.0.0:4317
          http:
            endpoint: 0.0.0.0:4318
    
    processors:
      batch:
        timeout: {OTEL_CONFIG["batch_processor"]["timeout"]}
        send_batch_size: {OTEL_CONFIG["batch_processor"]["batch_size"]}
        send_batch_max_size: {OTEL_CONFIG["batch_processor"]["max_queue_size"]}
      
      resource:
        attributes:
          - key: service.name
            value: {OTEL_CONFIG["service_name"]}
          - key: service.version
            value: {OTEL_CONFIG["service_version"]}
          - key: service.namespace
            value: {OTEL_CONFIG["resource_attributes"]["service.namespace"]}
          - key: deployment.environment
            value: {OTEL_CONFIG["resource_attributes"]["deployment.environment"]}
    
    exporters:
      jaeger:
        endpoint: {otel_endpoint}
        tls:
          insecure: true
    
    service:
      pipelines:
        traces:
          receivers: [otlp]
          processors: [batch, resource]
          exporters: [jaeger]
---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: kserve-otel-collector
  namespace: {KSERVE_NAMESPACE}
spec:
  selector:
    matchLabels:
      app: kserve-otel-collector
  template:
    metadata:
      labels:
        app: kserve-otel-collector
    spec:
      serviceAccountName: kserve-sa
      containers:
      - name: otel-collector
        image: otel/opentelemetry-collector-contrib:0.88.0
        args:
          - --config=/conf/config.yaml
        volumeMounts:
        - name: config
          mountPath: /conf
        ports:
        - containerPort: 4317
          name: otlp-grpc
        - containerPort: 4318
          name: otlp-http
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 500m
            memory: 512Mi
      volumes:
      - name: config
        configMap:
          name: kserve-otel-config
---
apiVersion: v1
kind: Service
metadata:
  name: kserve-otel-collector
  namespace: {KSERVE_NAMESPACE}
spec:
  selector:
    app: kserve-otel-collector
  ports:
  - name: otlp-grpc
    port: 4317
    targetPort: 4317
  - name: otlp-http
    port: 4318
    targetPort: 4318
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write(otel_config)
            otel_file = f.name
        
        try:
            command = f"kubectl apply -f {otel_file}"
            run_command(command=command, check=True)
            self._logger.info("OpenTelemetry configuration applied")
        finally:
            os.unlink(otel_file)

    def _configure_clickhouse_logging(
        self,
        clickhouse_host: str,
        clickhouse_port: int,
        clickhouse_database: str
    ) -> None:
        """Configure ClickHouse logging for KServe."""
        self._logger.info("Configuring ClickHouse logging...")
        
        clickhouse_config = f"""
apiVersion: v1
kind: ConfigMap
metadata:
  name: kserve-clickhouse-config
  namespace: {KSERVE_NAMESPACE}
data:
  config.yaml: |
    clickhouse:
      host: {clickhouse_host}
      port: {clickhouse_port}
      database: {clickhouse_database}
      tables:
        inference_logs: {CLICKHOUSE_CONFIG["tables"]["inference_logs"]}
        model_metrics: {CLICKHOUSE_CONFIG["tables"]["model_metrics"]}
        error_logs: {CLICKHOUSE_CONFIG["tables"]["error_logs"]}
      batch_size: {CLICKHOUSE_CONFIG["batch_size"]}
      flush_interval: {CLICKHOUSE_CONFIG["flush_interval"]}
    
    logging:
      level: info
      format: json
      output: stdout
---
apiVersion: v1
kind: Secret
metadata:
  name: kserve-clickhouse-credentials
  namespace: {KSERVE_NAMESPACE}
type: Opaque
data:
  username: a3NlcnZl  # kserve
  password: a3NlcnZlLXBhc3N3b3Jk  # kserve-password
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write(clickhouse_config)
            clickhouse_file = f.name
        
        try:
            command = f"kubectl apply -f {clickhouse_file}"
            run_command(command=command, check=True)
            self._logger.info("ClickHouse logging configuration applied")
        finally:
            os.unlink(clickhouse_file)

    def _create_storage_secrets(self) -> None:
        """Create storage secrets for model loading."""
        self._logger.info("Creating storage secrets...")
        
        storage_secret = f"""
apiVersion: v1
kind: Secret
metadata:
  name: kserve-s3-credentials
  namespace: {KSERVE_NAMESPACE}
  annotations:
    serving.kserve.io/s3-endpoint: "minio.object-store.svc.cluster.local:9000"
    serving.kserve.io/s3-usehttps: "0"
    serving.kserve.io/s3-region: "us-east-1"
    serving.kserve.io/s3-useanoncredential: "false"
type: Opaque
data:
  AWS_ACCESS_KEY_ID: bWxvcHMtbW9kZWxz  # mlops-models
  AWS_SECRET_ACCESS_KEY: bWxvcHMtbW9kZWxzLXNlY3JldC1rZXk=  # mlops-models-secret-key
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write(storage_secret)
            secret_file = f.name
        
        try:
            command = f"kubectl apply -f {secret_file}"
            run_command(command=command, check=True)
            self._logger.info("Storage secrets created")
        finally:
            os.unlink(secret_file)

    def _deploy_sample_models(self) -> None:
        """Deploy sample inference services."""
        self._logger.info("Deploying sample inference services...")
        
        sample_models = f"""
apiVersion: serving.kserve.io/v1beta1
kind: InferenceService
metadata:
  name: sklearn-iris
  namespace: {KSERVE_NAMESPACE}
  annotations:
    serving.kserve.io/model-version: "v1.0"
    autoscaling.knative.dev/minScale: "1"
    autoscaling.knative.dev/maxScale: "10"
spec:
  predictor:
    serviceAccountName: kserve-sa
    sklearn:
      storageUri: "s3://mlops-models/sklearn/iris/v1.0"
      resources:
        requests:
          cpu: "100m"
          memory: "256Mi"
        limits:
          cpu: "1000m"
          memory: "1Gi"
      env:
      - name: OTEL_EXPORTER_JAEGER_ENDPOINT
        value: "http://kserve-otel-collector:4318"
      - name: OTEL_SERVICE_NAME
        value: "sklearn-iris"
      - name: CLICKHOUSE_HOST
        value: "{CLICKHOUSE_CONFIG['host']}"
      - name: CLICKHOUSE_DATABASE
        value: "{CLICKHOUSE_CONFIG['database']}"
---
apiVersion: serving.kserve.io/v1beta1
kind: InferenceService
metadata:
  name: tensorflow-mnist
  namespace: {KSERVE_NAMESPACE}
  annotations:
    serving.kserve.io/model-version: "v1.0"
    autoscaling.knative.dev/minScale: "0"
    autoscaling.knative.dev/maxScale: "5"
spec:
  predictor:
    serviceAccountName: kserve-sa
    tensorflow:
      storageUri: "s3://mlops-models/tensorflow/mnist/v1.0"
      resources:
        requests:
          cpu: "200m"
          memory: "512Mi"
        limits:
          cpu: "1000m"
          memory: "2Gi"
      env:
      - name: OTEL_EXPORTER_JAEGER_ENDPOINT
        value: "http://kserve-otel-collector:4318"
      - name: OTEL_SERVICE_NAME
        value: "tensorflow-mnist"
      - name: CLICKHOUSE_HOST
        value: "{CLICKHOUSE_CONFIG['host']}"
      - name: CLICKHOUSE_DATABASE
        value: "{CLICKHOUSE_CONFIG['database']}"
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write(sample_models)
            models_file = f.name
        
        try:
            command = f"kubectl apply -f {models_file}"
            run_command(command=command, check=True)
            self._logger.info("Sample inference services deployed")
        finally:
            os.unlink(models_file)

    def _wait_for_deployment(self, deployment_name: str, namespace: str, timeout: int = 300) -> None:
        """Wait for deployment to be ready."""
        self._logger.info(f"Waiting for deployment {deployment_name} in namespace {namespace}...")
        
        command = (
            f"kubectl wait --namespace={namespace} "
            f"--for=condition=available deployment/{deployment_name} "
            f"--timeout={timeout}s"
        )
        run_command(command=command, check=True)

    def uninstall_kserve(self) -> None:
        """Uninstall KServe and related components."""
        self._logger.info("Starting KServe uninstallation...")
        
        # Delete sample models
        try:
            command = f"kubectl delete inferenceservices --all -n {KSERVE_NAMESPACE}"
            run_command(command=command, check=False)
        except Exception:
            pass
        
        # Delete KServe
        command = (
            f"kubectl delete -f https://github.com/kserve/kserve/releases/download/"
            f"{KSERVE_VERSION}/kserve.yaml"
        )
        run_command(command=command, check=False)
        
        # Delete KServe namespace
        if is_component_installed(type="namespace", name=KSERVE_NAMESPACE):
            command = f"kubectl delete namespace {KSERVE_NAMESPACE}"
            run_command(command=command, check=True)
        
        self._logger.info("KServe uninstallation completed")

    def get_kserve_status(self) -> Dict:
        """Get KServe installation status."""
        status = {
            "kserve_namespace_exists": is_component_installed(type="namespace", name=KSERVE_NAMESPACE),
            "knative_serving_exists": is_component_installed(type="namespace", name=KNATIVE_SERVING_NAMESPACE),
            "cert_manager_exists": is_component_installed(type="namespace", name=CERT_MANAGER_NAMESPACE),
            "kserve_system_exists": is_component_installed(type="namespace", name="kserve-system")
        }
        
        return status