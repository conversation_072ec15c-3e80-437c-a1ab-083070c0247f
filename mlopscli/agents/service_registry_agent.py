"""Service Registry Agent for bulk service registration and management."""

import logging
from typing import Dict, List, Optional, Any, Type
from dataclasses import dataclass

from mlopscli.utils.installation_resolver import InstallationResolver, ServiceConfig


@dataclass
class ServiceDefinition:
    """Simple service definition for fluent interface."""
    service_name: str
    local_port: int
    remote_port: int
    namespace: str
    client_cls: Optional[Type[Any]] = None
    client_kwargs: Optional[Dict[str, Any]] = None


class ServiceRegistryAgent:
    """
    Agent for simplified bulk service registration and management.
    
    Provides multiple ways to register services:
    - Bulk registration from list of ServiceConfig objects
    - Configuration-based registration from dictionaries
    - Fluent interface for readable service definitions
    """

    def __init__(self, installation_resolver: InstallationResolver, logger: Optional[logging.Logger] = None):
        """
        Initialize the ServiceRegistryAgent.
        
        Args:
            installation_resolver: The InstallationResolver instance to register services with
            logger: Optional logger instance
        """
        self._installation_resolver = installation_resolver
        self._logger = logger or logging.getLogger(__name__)
        self._service_definitions: List[ServiceDefinition] = []

    def register_services(self, configs: List[ServiceConfig]) -> None:
        """
        Register multiple services at once from a list of ServiceConfig objects.
        
        Args:
            configs: List of ServiceConfig objects to register
        """
        self._logger.info(f"Registering {len(configs)} services...")
        for config in configs:
            self._installation_resolver.register_service(config)
            self._logger.debug(f"Registered service: {config.service_name}")
        self._logger.info("All services registered successfully")

    def register_from_config(self, service_configs: Dict[str, Dict[str, Any]]) -> None:
        """
        Register services from a configuration dictionary.
        
        Args:
            service_configs: Dictionary mapping service names to their configuration
                Example:
                {
                    'postgresql': {
                        'local_port': 5432,
                        'remote_port': 5432,
                        'namespace': 'default',
                        'client_cls': PostgreSQLClient,
                        'client_kwargs': {'database': 'mydb'}
                    }
                }
        """
        self._logger.info(f"Registering {len(service_configs)} services from configuration...")
        
        for service_name, config in service_configs.items():
            service_config = ServiceConfig(
                service_name=service_name,
                local_port=config['local_port'],
                remote_port=config['remote_port'],
                namespace=config['namespace'],
                client_cls=config.get('client_cls', object),
                client_kwargs=config.get('client_kwargs', {})
            )
            self._installation_resolver.register_service(service_config)
            self._logger.debug(f"Registered service from config: {service_name}")
        
        self._logger.info("All services registered from configuration successfully")

    def add_service(self, service_name: str, local_port: int, remote_port: int, 
                   namespace: str, client_cls: Optional[Type[Any]] = None, 
                   client_kwargs: Optional[Dict[str, Any]] = None) -> 'ServiceRegistryAgent':
        """
        Add a service definition using fluent interface.
        
        Args:
            service_name: Name of the service
            local_port: Local port for port forwarding
            remote_port: Remote port on the service
            namespace: Kubernetes namespace
            client_cls: Optional client class
            client_kwargs: Optional client configuration
            
        Returns:
            Self for method chaining
        """
        service_def = ServiceDefinition(
            service_name=service_name,
            local_port=local_port,
            remote_port=remote_port,
            namespace=namespace,
            client_cls=client_cls,
            client_kwargs=client_kwargs or {}
        )
        self._service_definitions.append(service_def)
        self._logger.debug(f"Added service definition: {service_name}")
        return self

    def register_all(self) -> None:
        """Register all services that have been added via the fluent interface."""
        if not self._service_definitions:
            self._logger.warning("No service definitions to register")
            return

        self._logger.info(f"Registering {len(self._service_definitions)} services from definitions...")
        
        for service_def in self._service_definitions:
            service_config = ServiceConfig(
                service_name=service_def.service_name,
                local_port=service_def.local_port,
                remote_port=service_def.remote_port,
                namespace=service_def.namespace,
                client_cls=service_def.client_cls or object,
                client_kwargs=service_def.client_kwargs or {}
            )
            self._installation_resolver.register_service(service_config)
            self._logger.debug(f"Registered service from definition: {service_def.service_name}")
        
        # Clear definitions after registration
        self._service_definitions.clear()
        self._logger.info("All services registered from definitions successfully")

    def clear_definitions(self) -> None:
        """Clear all pending service definitions without registering them."""
        count = len(self._service_definitions)
        self._service_definitions.clear()
        self._logger.info(f"Cleared {count} pending service definitions")

    def get_pending_definitions(self) -> List[ServiceDefinition]:
        """Get a copy of all pending service definitions."""
        return self._service_definitions.copy()

    def has_pending_definitions(self) -> bool:
        """Check if there are any pending service definitions."""
        return len(self._service_definitions) > 0