"""Storage & Database Agent for PostgreSQL, MinIO, and ClickHouse."""

import asyncio
import json
import logging
import subprocess
import time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from pathlib import Path

from mlopscli.agents.component_model import (
    ComponentRegistry, ML<PERSON>psComponent, ComponentStatus, ComponentType,
    component_registry
)
from mlopscli.kast.manage_clickhouse_installation import <PERSON><PERSON><PERSON>ouse
from mlopscli.utils.system import run_command, run_command_with_output
from mlopscli.utils.kubernetes import create_secret, is_component_installed
from mlopscli.utils.minio import create_s3_bucket
from mlopscli.utils.postgres import create_postgres_database
from mlopscli.utils.constants import INSTALLATION_LOCATION_LOCAL_STR, INSTALLATION_LOCATION_REMOTE_STR


@dataclass
class StorageValidationResult:
    """Result of storage component validation."""
    component_name: str
    is_valid: bool
    issues: List[str]
    recommendations: List[str]
    storage_assessment: Dict[str, Any]
    performance_metrics: Dict[str, Any]


@dataclass
class DatabaseConfiguration:
    """Database configuration parameters."""
    name: str
    username: str
    password: str
    host: str
    port: int
    ssl_mode: str = "require"
    max_connections: int = 100
    shared_buffers: str = "256MB"


@dataclass
class S3BucketConfiguration:
    """S3 bucket configuration."""
    name: str
    versioning: bool = True
    encryption: bool = True
    lifecycle_policy: Optional[Dict[str, Any]] = None
    public_access: bool = False


@dataclass
class ClickHouseConfiguration:
    """ClickHouse configuration parameters."""
    cluster_name: str = "default"
    replicas: int = 1
    shards: int = 1
    max_memory_usage: str = "2GB"
    max_connections: int = 1000
    compression: str = "zstd"


class StorageDatabaseAgent:
    """
    Specialized agent for managing storage and database components.
    
    Responsibilities:
    - PostgreSQL database installation and management
    - MinIO S3-compatible object storage setup
    - ClickHouse OLAP database configuration
    - Database backup and recovery procedures
    - Storage performance monitoring and optimization
    - Data retention and lifecycle management
    """
    
    def __init__(self, installation_location: str = INSTALLATION_LOCATION_LOCAL_STR):
        self.installation_location = installation_location
        self.logger = logging.getLogger(f"{__name__}.StorageAgent")
        self.clickhouse_manager = ClickHouse("olap-store")
        self.managed_components = self._get_managed_components()
        
    def _get_managed_components(self) -> List[str]:
        """Get list of components managed by this agent."""
        storage_components = component_registry.get_components_by_type(ComponentType.STORAGE)
        return [comp.name for comp in storage_components]
    
    async def validate_prerequisites(self) -> Dict[str, StorageValidationResult]:
        """
        Validate prerequisites for storage and database components.
        
        Returns:
            Dictionary mapping component names to validation results
        """
        results = {}
        
        for component_name in self.managed_components:
            component = component_registry.get_component(component_name)
            if not component:
                continue
                
            result = await self._validate_component_prerequisites(component)
            results[component_name] = result
            
        return results
    
    async def _validate_component_prerequisites(self, component: MLOpsComponent) -> StorageValidationResult:
        """Validate prerequisites for storage component."""
        issues = []
        recommendations = []
        storage_assessment = {}
        performance_metrics = {}
        
        if component.name == "postgresql":
            issues.extend(await self._validate_postgresql_prerequisites())
            storage_assessment = await self._assess_postgresql_storage()
            performance_metrics = await self._get_postgresql_metrics()
        elif component.name == "minio":
            issues.extend(await self._validate_minio_prerequisites())
            storage_assessment = await self._assess_minio_storage()
            performance_metrics = await self._get_minio_metrics()
        elif component.name == "clickhouse":
            issues.extend(await self._validate_clickhouse_prerequisites())
            storage_assessment = await self._assess_clickhouse_storage()
            performance_metrics = await self._get_clickhouse_metrics()
        
        # Common storage validations
        issues.extend(self._validate_common_storage_prerequisites(component))
        
        # Generate recommendations
        if issues:
            recommendations = self._generate_storage_recommendations(component, issues)
        
        return StorageValidationResult(
            component_name=component.name,
            is_valid=len(issues) == 0,
            issues=issues,
            recommendations=recommendations,
            storage_assessment=storage_assessment,
            performance_metrics=performance_metrics
        )
    
    async def _validate_postgresql_prerequisites(self) -> List[str]:
        """Validate PostgreSQL-specific prerequisites."""
        issues = []
        
        # Check Kubernetes cluster
        try:
            result = subprocess.run([
                "kubectl", "cluster-info"
            ], capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                issues.append("Kubernetes cluster is not accessible")
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            issues.append("Cannot verify Kubernetes cluster status")
        
        # Check storage class availability
        try:
            result = subprocess.run([
                "kubectl", "get", "storageclass", "--no-headers"
            ], capture_output=True, text=True, timeout=10)
            if result.returncode != 0 or not result.stdout.strip():
                issues.append("No storage classes available for persistent volumes")
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            issues.append("Cannot verify storage class availability")
        
        # Check if namespace exists
        try:
            result = subprocess.run([
                "kubectl", "get", "namespace", "sql-store"
            ], capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                self.logger.info("sql-store namespace will be created")
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            issues.append("Cannot verify namespace status")
        
        # Validate resource requirements
        issues.extend(await self._validate_postgresql_resources())
        
        return issues
    
    async def _validate_minio_prerequisites(self) -> List[str]:
        """Validate MinIO-specific prerequisites."""
        issues = []
        
        # Check Kubernetes cluster
        try:
            result = subprocess.run([
                "kubectl", "cluster-info"
            ], capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                issues.append("Kubernetes cluster is not accessible")
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            issues.append("Cannot verify Kubernetes cluster status")
        
        # Check storage class for object storage
        try:
            result = subprocess.run([
                "kubectl", "get", "storageclass", "--no-headers"
            ], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                storage_classes = result.stdout.strip().split('\n')
                has_suitable_storage = any('ssd' in sc.lower() or 'fast' in sc.lower() 
                                         for sc in storage_classes if sc.strip())
                if not has_suitable_storage:
                    issues.append("No high-performance storage class found for MinIO")
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            issues.append("Cannot verify storage class suitability")
        
        # Check if object-store namespace exists
        try:
            result = subprocess.run([
                "kubectl", "get", "namespace", "object-store"
            ], capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                self.logger.info("object-store namespace will be created")
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            issues.append("Cannot verify namespace status")
        
        return issues
    
    async def _validate_clickhouse_prerequisites(self) -> List[str]:
        """Validate ClickHouse-specific prerequisites."""
        issues = []
        
        # Check if MinIO is available (ClickHouse dependency)
        minio_component = component_registry.get_component("minio")
        if not minio_component or minio_component.status != ComponentStatus.INSTALLED:
            issues.append("MinIO object storage is required but not installed")
        
        # Check Kubernetes cluster
        try:
            result = subprocess.run([
                "kubectl", "cluster-info"
            ], capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                issues.append("Kubernetes cluster is not accessible")
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            issues.append("Cannot verify Kubernetes cluster status")
        
        # Check if olap-store namespace exists
        try:
            result = subprocess.run([
                "kubectl", "get", "namespace", "olap-store"
            ], capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                self.logger.info("olap-store namespace will be created")
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            issues.append("Cannot verify namespace status")
        
        # Validate ClickHouse resource requirements (higher than others)
        issues.extend(await self._validate_clickhouse_resources())
        
        return issues
    
    async def _validate_postgresql_resources(self) -> List[str]:
        """Validate PostgreSQL resource requirements."""
        issues = []
        
        try:
            # Check node resources
            result = subprocess.run([
                "kubectl", "describe", "nodes"
            ], capture_output=True, text=True, timeout=20)
            
            if result.returncode == 0:
                # Parse node resources (simplified check)
                if "memory" in result.stdout.lower():
                    # Look for available memory
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if "allocatable" in line.lower() and "memory" in line.lower():
                            # This is a simplified check
                            break
                    else:
                        issues.append("Could not verify available memory for PostgreSQL")
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            self.logger.warning("Could not validate PostgreSQL resource requirements")
        
        return issues
    
    async def _validate_clickhouse_resources(self) -> List[str]:
        """Validate ClickHouse resource requirements (more demanding)."""
        issues = []
        
        try:
            # ClickHouse needs more resources
            result = subprocess.run([
                "kubectl", "top", "nodes", "--no-headers"
            ], capture_output=True, text=True, timeout=15)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if line.strip():
                        parts = line.split()
                        if len(parts) >= 4:
                            memory_usage = parts[3]
                            if memory_usage.replace('%', '').isdigit():
                                usage_pct = int(memory_usage.replace('%', ''))
                                if usage_pct > 70:
                                    issues.append(f"High memory usage ({usage_pct}%) may affect ClickHouse performance")
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            self.logger.warning("Could not validate ClickHouse resource requirements")
        
        return issues
    
    def _validate_common_storage_prerequisites(self, component: MLOpsComponent) -> List[str]:
        """Validate common prerequisites for storage components."""
        issues = []
        
        # Validate dependencies
        dependency_issues = component_registry.validate_dependencies(component.name)
        issues.extend(dependency_issues)
        
        # Check Helm availability
        try:
            result = subprocess.run([
                "helm", "version", "--short"
            ], capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                issues.append("Helm package manager is not available")
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            issues.append("Cannot verify Helm installation")
        
        return issues
    
    async def _assess_postgresql_storage(self) -> Dict[str, Any]:
        """Assess PostgreSQL storage configuration."""
        assessment = {
            "storage_type": "persistent_volume",
            "backup_configured": False,
            "replication_enabled": False,
            "encryption_at_rest": False,
            "performance_tuning": "basic",
            "recommendations": []
        }
        
        assessment["recommendations"].extend([
            "Configure automated database backups",
            "Enable connection pooling for better performance",
            "Set up monitoring for database metrics",
            "Configure log rotation and retention",
            "Consider read replicas for high availability"
        ])
        
        return assessment
    
    async def _assess_minio_storage(self) -> Dict[str, Any]:
        """Assess MinIO storage configuration."""
        assessment = {
            "storage_type": "object_storage",
            "versioning_enabled": True,
            "encryption_enabled": True,
            "backup_configured": False,
            "lifecycle_policies": False,
            "access_policies": "secure",
            "recommendations": []
        }
        
        assessment["recommendations"].extend([
            "Configure bucket lifecycle policies for cost optimization",
            "Set up cross-region replication if needed",
            "Enable audit logging for compliance",
            "Configure bucket notifications for automation",
            "Implement backup strategy for critical data"
        ])
        
        return assessment
    
    async def _assess_clickhouse_storage(self) -> Dict[str, Any]:
        """Assess ClickHouse storage configuration."""
        assessment = {
            "storage_type": "columnar_storage",
            "compression_enabled": True,
            "sharding_configured": False,
            "replication_configured": False,
            "backup_configured": False,
            "performance_tuning": "basic",
            "recommendations": []
        }
        
        assessment["recommendations"].extend([
            "Configure data compression for storage efficiency",
            "Set up proper indexing strategy",
            "Configure data retention policies",
            "Enable query result caching",
            "Monitor query performance and optimize slow queries"
        ])
        
        return assessment
    
    async def _get_postgresql_metrics(self) -> Dict[str, Any]:
        """Get PostgreSQL performance metrics."""
        metrics = {
            "connections": {"active": 0, "max": 100},
            "storage": {"used": "0GB", "available": "0GB"},
            "queries": {"per_second": 0, "avg_duration": "0ms"},
            "cache": {"hit_ratio": "0%"}
        }
        
        # In a real implementation, these would be actual metrics
        return metrics
    
    async def _get_minio_metrics(self) -> Dict[str, Any]:
        """Get MinIO performance metrics."""
        metrics = {
            "storage": {"used": "0GB", "available": "0GB"},
            "requests": {"per_second": 0, "avg_latency": "0ms"},
            "buckets": {"count": 0, "objects": 0},
            "bandwidth": {"upload": "0MB/s", "download": "0MB/s"}
        }
        
        return metrics
    
    async def _get_clickhouse_metrics(self) -> Dict[str, Any]:
        """Get ClickHouse performance metrics."""
        metrics = {
            "queries": {"per_second": 0, "avg_duration": "0ms"},
            "storage": {"used": "0GB", "compressed_ratio": "3:1"},
            "memory": {"used": "0GB", "cache_hit_ratio": "0%"},
            "inserts": {"per_second": 0, "rows_per_second": 0}
        }
        
        return metrics
    
    def _generate_storage_recommendations(self, component: MLOpsComponent, issues: List[str]) -> List[str]:
        """Generate recommendations for storage issues."""
        recommendations = []
        
        for issue in issues:
            if "storage class" in issue.lower():
                recommendations.append("Configure appropriate storage classes for persistent volumes")
            elif "memory" in issue.lower():
                recommendations.append("Consider adding more memory or using memory-optimized nodes")
            elif "kubernetes" in issue.lower():
                recommendations.append("Ensure Kubernetes cluster is healthy and accessible")
            elif "namespace" in issue.lower():
                recommendations.append("Namespace will be created automatically during installation")
            elif "helm" in issue.lower():
                recommendations.append("Install Helm package manager for component deployment")
        
        # Component-specific recommendations
        if component.name == "postgresql":
            recommendations.extend([
                "Configure persistent volumes for data durability",
                "Set up automated backup schedules",
                "Configure connection pooling for scalability",
                "Monitor database performance metrics"
            ])
        elif component.name == "minio":
            recommendations.extend([
                "Use high-performance storage for better throughput",
                "Configure distributed mode for high availability",
                "Set up bucket policies and access controls",
                "Enable server-side encryption"
            ])
        elif component.name == "clickhouse":
            recommendations.extend([
                "Allocate sufficient memory for query processing",
                "Configure proper data partitioning strategy",
                "Set up monitoring for query performance",
                "Consider cluster configuration for scale"
            ])
        
        return recommendations
    
    async def install_component(self, component_name: str, config: Optional[Dict[str, Any]] = None) -> Tuple[bool, List[str]]:
        """
        Install storage/database component.
        
        Args:
            component_name: Name of component to install
            config: Additional configuration parameters
            
        Returns:
            Tuple of (success, error_messages)
        """
        component = component_registry.get_component(component_name)
        if not component:
            return False, [f"Component {component_name} not found"]
        
        if component.component_type != ComponentType.STORAGE:
            return False, [f"Component {component_name} is not a storage component"]
        
        # Update component status
        component.status = ComponentStatus.INSTALLING
        
        try:
            if component_name == "postgresql":
                success, errors = await self._install_postgresql(config or {})
            elif component_name == "minio":
                success, errors = await self._install_minio(config or {})
            elif component_name == "clickhouse":
                success, errors = await self._install_clickhouse(config or {})
            else:
                return False, [f"Installation not implemented for {component_name}"]
            
            if success:
                component.status = ComponentStatus.INSTALLED
                self.logger.info(f"Successfully installed {component_name}")
            else:
                component.status = ComponentStatus.FAILED
                self.logger.error(f"Failed to install {component_name}: {errors}")
            
            return success, errors
            
        except Exception as e:
            component.status = ComponentStatus.FAILED
            error_msg = f"Exception during {component_name} installation: {str(e)}"
            self.logger.error(error_msg)
            return False, [error_msg]
    
    async def _install_postgresql(self, config: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Install PostgreSQL database."""
        try:
            self.logger.info("Starting PostgreSQL installation...")
            
            # Create namespace
            result = subprocess.run([
                "kubectl", "create", "namespace", "sql-store", "--dry-run=client", "-o", "yaml"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                subprocess.run([
                    "kubectl", "apply", "-f", "-"
                ], input=result.stdout, text=True, timeout=30)
            
            # Create PostgreSQL secret
            postgres_password = config.get("postgres_password", "postgres123")
            secret_success = create_secret(
                "postgres-secret",
                "sql-store",
                {
                    "postgres-password": postgres_password,
                    "replication-password": config.get("replication_password", "replica123")
                }
            )
            
            if not secret_success:
                return False, ["Failed to create PostgreSQL secret"]
            
            # Add Bitnami Helm repository
            result = subprocess.run([
                "helm", "repo", "add", "bitnami", "https://charts.bitnami.com/bitnami"
            ], capture_output=True, text=True, timeout=30)
            
            result = subprocess.run([
                "helm", "repo", "update"
            ], capture_output=True, text=True, timeout=60)
            
            # Install PostgreSQL
            helm_cmd = [
                "helm", "install", "postgresql", "bitnami/postgresql",
                "--namespace", "sql-store",
                "--set", f"auth.postgresPassword={postgres_password}",
                "--set", "primary.persistence.size=10Gi",
                "--set", "metrics.enabled=true",
                "--set", "metrics.serviceMonitor.enabled=true"
            ]
            
            result = subprocess.run(helm_cmd, capture_output=True, text=True, timeout=600)
            
            if result.returncode != 0:
                return False, [f"Failed to install PostgreSQL: {result.stderr}"]
            
            # Wait for PostgreSQL to be ready
            max_wait = 300
            wait_time = 0
            while wait_time < max_wait:
                if await self._check_postgresql_ready():
                    break
                await asyncio.sleep(15)
                wait_time += 15
            else:
                return False, ["PostgreSQL failed to become ready within timeout period"]
            
            # Create initial databases for other components
            await self._create_initial_databases(config)
            
            return True, []
            
        except Exception as e:
            return False, [f"PostgreSQL installation failed: {str(e)}"]
    
    async def _install_minio(self, config: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Install MinIO object storage."""
        try:
            self.logger.info("Starting MinIO installation...")
            
            # Create namespace
            result = subprocess.run([
                "kubectl", "create", "namespace", "object-store", "--dry-run=client", "-o", "yaml"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                subprocess.run([
                    "kubectl", "apply", "-f", "-"
                ], input=result.stdout, text=True, timeout=30)
            
            # Create MinIO secret
            access_key = config.get("access_key", "minioadmin")
            secret_key = config.get("secret_key", "minioadmin123")
            
            secret_success = create_secret(
                "minio-secret",
                "object-store",
                {
                    "accesskey": access_key,
                    "secretkey": secret_key
                }
            )
            
            if not secret_success:
                return False, ["Failed to create MinIO secret"]
            
            # Add MinIO Helm repository
            result = subprocess.run([
                "helm", "repo", "add", "minio", "https://charts.min.io/"
            ], capture_output=True, text=True, timeout=30)
            
            result = subprocess.run([
                "helm", "repo", "update"
            ], capture_output=True, text=True, timeout=60)
            
            # Install MinIO
            helm_cmd = [
                "helm", "install", "minio", "minio/minio",
                "--namespace", "object-store",
                "--set", f"auth.rootUser={access_key}",
                "--set", f"auth.rootPassword={secret_key}",
                "--set", "defaultBuckets=lakefs,argo,clickhouse",
                "--set", "persistence.size=20Gi",
                "--set", "metrics.serviceMonitor.enabled=true"
            ]
            
            result = subprocess.run(helm_cmd, capture_output=True, text=True, timeout=600)
            
            if result.returncode != 0:
                return False, [f"Failed to install MinIO: {result.stderr}"]
            
            # Wait for MinIO to be ready
            max_wait = 300
            wait_time = 0
            while wait_time < max_wait:
                if await self._check_minio_ready():
                    break
                await asyncio.sleep(15)
                wait_time += 15
            else:
                return False, ["MinIO failed to become ready within timeout period"]
            
            # Create initial buckets
            await self._create_initial_buckets(config)
            
            return True, []
            
        except Exception as e:
            return False, [f"MinIO installation failed: {str(e)}"]
    
    async def _install_clickhouse(self, config: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Install ClickHouse OLAP database."""
        try:
            self.logger.info("Starting ClickHouse installation...")
            
            # Use the existing ClickHouse manager
            success = self.clickhouse_manager.install()
            
            if success:
                # Wait for ClickHouse to be ready
                max_wait = 300
                wait_time = 0
                while wait_time < max_wait:
                    if await self._check_clickhouse_ready():
                        break
                    await asyncio.sleep(15)
                    wait_time += 15
                else:
                    return False, ["ClickHouse failed to become ready within timeout period"]
                
                return True, []
            else:
                return False, ["ClickHouse installation failed"]
            
        except Exception as e:
            return False, [f"ClickHouse installation failed: {str(e)}"]
    
    async def _check_postgresql_ready(self) -> bool:
        """Check if PostgreSQL is ready."""
        try:
            result = subprocess.run([
                "kubectl", "get", "pods", "-n", "sql-store",
                "-l", "app.kubernetes.io/name=postgresql",
                "--no-headers"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode != 0:
                return False
            
            pods = result.stdout.strip().split('\n')
            for pod in pods:
                if pod.strip():
                    if "Running" not in pod or "1/1" not in pod:
                        return False
            
            return len(pods) > 0 and pods[0].strip()
            
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            return False
    
    async def _check_minio_ready(self) -> bool:
        """Check if MinIO is ready."""
        try:
            result = subprocess.run([
                "kubectl", "get", "pods", "-n", "object-store",
                "-l", "app=minio",
                "--no-headers"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode != 0:
                return False
            
            pods = result.stdout.strip().split('\n')
            for pod in pods:
                if pod.strip():
                    if "Running" not in pod or "1/1" not in pod:
                        return False
            
            return len(pods) > 0 and pods[0].strip()
            
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            return False
    
    async def _check_clickhouse_ready(self) -> bool:
        """Check if ClickHouse is ready."""
        try:
            result = subprocess.run([
                "kubectl", "get", "pods", "-n", "olap-store",
                "-l", "app=clickhouse",
                "--no-headers"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode != 0:
                return False
            
            pods = result.stdout.strip().split('\n')
            for pod in pods:
                if pod.strip():
                    if "Running" not in pod or "1/1" not in pod:
                        return False
            
            return len(pods) > 0 and pods[0].strip()
            
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            return False
    
    async def _create_initial_databases(self, config: Dict[str, Any]) -> None:
        """Create initial databases for other components."""
        try:
            databases = ["keycloak", "lakefs", "grafana"]
            for db_name in databases:
                success = create_postgres_database(db_name, "sql-store")
                if success:
                    self.logger.info(f"Created database: {db_name}")
                else:
                    self.logger.warning(f"Failed to create database: {db_name}")
        except Exception as e:
            self.logger.error(f"Failed to create initial databases: {str(e)}")
    
    async def _create_initial_buckets(self, config: Dict[str, Any]) -> None:
        """Create initial S3 buckets for other components."""
        try:
            buckets = ["lakefs", "argo-workflows", "clickhouse-backup"]
            for bucket_name in buckets:
                success = create_s3_bucket(bucket_name, "object-store")
                if success:
                    self.logger.info(f"Created S3 bucket: {bucket_name}")
                else:
                    self.logger.warning(f"Failed to create S3 bucket: {bucket_name}")
        except Exception as e:
            self.logger.error(f"Failed to create initial buckets: {str(e)}")
    
    async def health_check(self, component_name: str) -> Dict[str, Any]:
        """Perform health check on storage component."""
        component = component_registry.get_component(component_name)
        if not component:
            return {"status": "error", "message": f"Component {component_name} not found"}
        
        if component_name == "postgresql":
            return await self._health_check_postgresql()
        elif component_name == "minio":
            return await self._health_check_minio()
        elif component_name == "clickhouse":
            return await self._health_check_clickhouse()
        else:
            return {"status": "not_implemented", "message": f"Health check not implemented for {component_name}"}
    
    async def _health_check_postgresql(self) -> Dict[str, Any]:
        """Health check for PostgreSQL."""
        try:
            result = subprocess.run([
                "kubectl", "get", "pods", "-n", "sql-store",
                "-l", "app.kubernetes.io/name=postgresql",
                "--no-headers"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode != 0:
                return {"status": "unhealthy", "message": "Cannot check PostgreSQL pods"}
            
            pods = result.stdout.strip().split('\n')
            running_pods = sum(1 for pod in pods if pod.strip() and "Running" in pod and "1/1" in pod)
            total_pods = sum(1 for pod in pods if pod.strip())
            
            if running_pods == total_pods and total_pods > 0:
                return {
                    "status": "healthy",
                    "message": f"All {total_pods} PostgreSQL pods are running",
                    "pods": total_pods
                }
            else:
                return {
                    "status": "degraded",
                    "message": f"{running_pods}/{total_pods} PostgreSQL pods are running"
                }
                
        except Exception as e:
            return {"status": "error", "message": f"Health check failed: {str(e)}"}
    
    async def _health_check_minio(self) -> Dict[str, Any]:
        """Health check for MinIO."""
        try:
            result = subprocess.run([
                "kubectl", "get", "pods", "-n", "object-store",
                "-l", "app=minio",
                "--no-headers"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode != 0:
                return {"status": "unhealthy", "message": "Cannot check MinIO pods"}
            
            pods = result.stdout.strip().split('\n')
            running_pods = sum(1 for pod in pods if pod.strip() and "Running" in pod and "1/1" in pod)
            total_pods = sum(1 for pod in pods if pod.strip())
            
            if running_pods == total_pods and total_pods > 0:
                return {
                    "status": "healthy",
                    "message": f"All {total_pods} MinIO pods are running",
                    "pods": total_pods
                }
            else:
                return {
                    "status": "degraded",
                    "message": f"{running_pods}/{total_pods} MinIO pods are running"
                }
                
        except Exception as e:
            return {"status": "error", "message": f"Health check failed: {str(e)}"}
    
    async def _health_check_clickhouse(self) -> Dict[str, Any]:
        """Health check for ClickHouse."""
        try:
            result = subprocess.run([
                "kubectl", "get", "pods", "-n", "olap-store",
                "-l", "app=clickhouse",
                "--no-headers"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode != 0:
                return {"status": "unhealthy", "message": "Cannot check ClickHouse pods"}
            
            pods = result.stdout.strip().split('\n')
            running_pods = sum(1 for pod in pods if pod.strip() and "Running" in pod and "1/1" in pod)
            total_pods = sum(1 for pod in pods if pod.strip())
            
            if running_pods == total_pods and total_pods > 0:
                return {
                    "status": "healthy",
                    "message": f"All {total_pods} ClickHouse pods are running",
                    "pods": total_pods
                }
            else:
                return {
                    "status": "degraded",
                    "message": f"{running_pods}/{total_pods} ClickHouse pods are running"
                }
                
        except Exception as e:
            return {"status": "error", "message": f"Health check failed: {str(e)}"}
    
    def get_managed_components_status(self) -> Dict[str, ComponentStatus]:
        """Get status of all managed storage components."""
        status_map = {}
        for component_name in self.managed_components:
            component = component_registry.get_component(component_name)
            if component:
                status_map[component_name] = component.status
        return status_map