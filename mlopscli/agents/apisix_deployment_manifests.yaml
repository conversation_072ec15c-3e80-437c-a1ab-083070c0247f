# Apache APISIX Kubernetes Deployment Manifests
# Production-ready deployment for enterprise MLOps platform

---
# =============================================================================
# NAMESPACE
# =============================================================================

apiVersion: v1
kind: Namespace
metadata:
  name: api-gateway
  labels:
    name: api-gateway
    environment: production
    component: gateway

---
# =============================================================================
# ETCD CLUSTER DEPLOYMENT
# =============================================================================

apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: etcd
  namespace: api-gateway
  labels:
    app: etcd
    component: datastore
spec:
  serviceName: etcd
  replicas: 3
  selector:
    matchLabels:
      app: etcd
  template:
    metadata:
      labels:
        app: etcd
    spec:
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app
                operator: In
                values: ["etcd"]
            topologyKey: kubernetes.io/hostname
      containers:
      - name: etcd
        image: quay.io/coreos/etcd:v3.5.10
        ports:
        - containerPort: 2379
          name: client
        - containerPort: 2380
          name: peer
        env:
        - name: ETCD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: ETCD_INITIAL_CLUSTER
          value: "etcd-0=http://etcd-0.etcd:2380,etcd-1=http://etcd-1.etcd:2380,etcd-2=http://etcd-2.etcd:2380"
        - name: ETCD_INITIAL_CLUSTER_STATE
          value: "new"
        - name: ETCD_INITIAL_CLUSTER_TOKEN
          value: "etcd-cluster"
        - name: ETCD_LISTEN_CLIENT_URLS
          value: "http://0.0.0.0:2379"
        - name: ETCD_ADVERTISE_CLIENT_URLS
          value: "http://$(ETCD_NAME).etcd:2379"
        - name: ETCD_LISTEN_PEER_URLS
          value: "http://0.0.0.0:2380"
        - name: ETCD_INITIAL_ADVERTISE_PEER_URLS
          value: "http://$(ETCD_NAME).etcd:2380"
        - name: ETCD_DATA_DIR
          value: "/etcd-data"
        - name: ETCD_AUTO_COMPACTION_RETENTION
          value: "1"
        volumeMounts:
        - name: etcd-data
          mountPath: /etcd-data
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 2379
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 2379
          initialDelaySeconds: 5
          periodSeconds: 5
  volumeClaimTemplates:
  - metadata:
      name: etcd-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 10Gi
      storageClassName: fast-ssd

---
apiVersion: v1
kind: Service
metadata:
  name: etcd
  namespace: api-gateway
  labels:
    app: etcd
spec:
  clusterIP: None
  ports:
  - port: 2379
    name: client
  - port: 2380
    name: peer
  selector:
    app: etcd

---
# =============================================================================
# APISIX GATEWAY DEPLOYMENT
# =============================================================================

apiVersion: apps/v1
kind: Deployment
metadata:
  name: apisix-gateway
  namespace: api-gateway
  labels:
    app: apisix-gateway
    component: gateway
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
  selector:
    matchLabels:
      app: apisix-gateway
  template:
    metadata:
      labels:
        app: apisix-gateway
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9091"
        prometheus.io/path: "/apisix/prometheus/metrics"
    spec:
      serviceAccountName: apisix-gateway
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values: ["apisix-gateway"]
              topologyKey: kubernetes.io/hostname
      containers:
      - name: apisix
        image: apache/apisix:3.7.0-debian
        ports:
        - containerPort: 9080
          name: http
        - containerPort: 9443
          name: https
        - containerPort: 9180
          name: admin
        - containerPort: 9091
          name: metrics
        env:
        - name: APISIX_STAND_ALONE
          value: "false"
        - name: ENVIRONMENT
          value: "production"
        - name: CLUSTER_NAME
          value: "mlops-cluster"
        - name: DOMAIN
          valueFrom:
            configMapKeyRef:
              name: apisix-env
              key: domain
        - name: ADMIN_API_KEY
          valueFrom:
            secretKeyRef:
              name: apisix-secrets
              key: admin-api-key
        - name: VIEWER_API_KEY
          valueFrom:
            secretKeyRef:
              name: apisix-secrets
              key: viewer-api-key
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: apisix-secrets  
              key: jwt-secret
        - name: OIDC_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: apisix-secrets
              key: oidc-client-id
        - name: OIDC_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: apisix-secrets
              key: oidc-client-secret
        - name: KEYCLOAK_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: apisix-secrets
              key: keycloak-client-id
        - name: KEYCLOAK_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: apisix-secrets
              key: keycloak-client-secret
        - name: CLICKHOUSE_USER
          valueFrom:
            secretKeyRef:
              name: apisix-secrets
              key: clickhouse-user
        - name: CLICKHOUSE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: apisix-secrets
              key: clickhouse-password
        volumeMounts:
        - name: apisix-config
          mountPath: /usr/local/apisix/conf/config.yaml
          subPath: config.yaml
        - name: ssl-certs
          mountPath: /ssl
          readOnly: true
        - name: etcd-ssl
          mountPath: /etcd-ssl
          readOnly: true
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /apisix/status
            port: 9080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /apisix/status
            port: 9080
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /apisix/status
            port: 9080
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
      volumes:
      - name: apisix-config
        configMap:
          name: apisix-config
      - name: ssl-certs
        secret:
          secretName: apisix-tls-certs
      - name: etcd-ssl
        secret:
          secretName: etcd-tls-certs
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000

---
apiVersion: v1
kind: Service
metadata:
  name: apisix-gateway
  namespace: api-gateway
  labels:
    app: apisix-gateway
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: nlb
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: tcp
spec:
  type: LoadBalancer
  ports:
  - port: 80
    targetPort: 9080
    name: http
  - port: 443
    targetPort: 9443
    name: https
  selector:
    app: apisix-gateway

---
apiVersion: v1
kind: Service
metadata:
  name: apisix-admin
  namespace: api-gateway
  labels:
    app: apisix-gateway
    component: admin
spec:
  type: ClusterIP
  ports:
  - port: 9180
    targetPort: 9180
    name: admin
  selector:
    app: apisix-gateway

---
apiVersion: v1
kind: Service
metadata:
  name: apisix-metrics
  namespace: api-gateway
  labels:
    app: apisix-gateway
    component: metrics
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9091"
    prometheus.io/path: "/apisix/prometheus/metrics"
spec:
  type: ClusterIP
  ports:
  - port: 9091
    targetPort: 9091
    name: metrics
  selector:
    app: apisix-gateway

---
# =============================================================================
# APISIX DASHBOARD DEPLOYMENT
# =============================================================================

apiVersion: apps/v1
kind: Deployment
metadata:
  name: apisix-dashboard
  namespace: api-gateway
  labels:
    app: apisix-dashboard
    component: dashboard
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: apisix-dashboard
  template:
    metadata:
      labels:
        app: apisix-dashboard
    spec:
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values: ["apisix-dashboard"]
              topologyKey: kubernetes.io/hostname
      containers:
      - name: apisix-dashboard
        image: apache/apisix-dashboard:3.0.1-alpine
        ports:
        - containerPort: 9000
          name: http
        env:
        - name: APISIX_DASHBOARD_DB_TYPE
          value: "etcd"
        - name: APISIX_DASHBOARD_ETCD_ENDPOINTS
          value: "http://etcd:2379"
        - name: APISIX_DASHBOARD_ETCD_PREFIX
          value: "/apisix"
        - name: APISIX_DASHBOARD_USERNAME
          valueFrom:
            secretKeyRef:
              name: apisix-dashboard-secrets
              key: username
        - name: APISIX_DASHBOARD_PASSWORD
          valueFrom:
            secretKeyRef:
              name: apisix-dashboard-secrets
              key: password
        volumeMounts:
        - name: dashboard-config
          mountPath: /usr/local/apisix-dashboard/conf/conf.yaml
          subPath: conf.yaml
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /
            port: 9000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 9000
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: dashboard-config
        configMap:
          name: apisix-dashboard-config
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000

---
apiVersion: v1
kind: Service
metadata:
  name: apisix-dashboard
  namespace: api-gateway
  labels:
    app: apisix-dashboard
spec:
  type: ClusterIP
  ports:
  - port: 9000
    targetPort: 9000
    name: http
  selector:
    app: apisix-dashboard

---
# =============================================================================
# SERVICE ACCOUNT AND RBAC
# =============================================================================

apiVersion: v1
kind: ServiceAccount
metadata:
  name: apisix-gateway
  namespace: api-gateway
  labels:
    app: apisix-gateway

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: apisix-gateway
  labels:
    app: apisix-gateway
rules:
- apiGroups: [""]
  resources: ["endpoints", "services", "pods"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list", "watch", "create", "update", "patch"]
- apiGroups: ["discovery.k8s.io"]
  resources: ["endpointslices"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["networking.k8s.io"]
  resources: ["ingresses"]
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: apisix-gateway
  labels:
    app: apisix-gateway
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: apisix-gateway
subjects:
- kind: ServiceAccount
  name: apisix-gateway
  namespace: api-gateway

---
# =============================================================================
# SECRETS
# =============================================================================

apiVersion: v1
kind: Secret
metadata:
  name: apisix-secrets
  namespace: api-gateway
  labels:
    app: apisix-gateway
type: Opaque
data:
  # Base64 encoded values - replace with actual secrets
  admin-api-key: YWRtaW4tc2VjcmV0LWtleQ==  # admin-secret-key
  viewer-api-key: dmlld2VyLXNlY3JldC1rZXk=  # viewer-secret-key
  jwt-secret: and0LXNlY3JldC1rZXktZm9yLWFwaXNpeA==  # jwt-secret-key-for-apisix
  oidc-client-id: YXBpc2l4LW9pZGMtY2xpZW50  # apisix-oidc-client
  oidc-client-secret: b2lkYy1jbGllbnQtc2VjcmV0LWZvci1hcGlzaXg=  # oidc-client-secret-for-apisix
  keycloak-client-id: YXBpc2l4LWtleWNsb2FrLWNsaWVudA==  # apisix-keycloak-client
  keycloak-client-secret: a2V5Y2xvYWstY2xpZW50LXNlY3JldC1mb3ItYXBpc2l4  # keycloak-client-secret-for-apisix
  clickhouse-user: YXBpc2l4X3VzZXI=  # apisix_user
  clickhouse-password: Y2xpY2tob3VzZS1wYXNzd29yZC1mb3ItYXBpc2l4  # clickhouse-password-for-apisix

---
apiVersion: v1
kind: Secret
metadata:
  name: apisix-dashboard-secrets
  namespace: api-gateway
  labels:
    app: apisix-dashboard
type: Opaque
data:
  username: YWRtaW4=  # admin
  password: YWRtaW4tcGFzc3dvcmQ=  # admin-password

---
# =============================================================================
# TLS CERTIFICATES
# =============================================================================

apiVersion: v1
kind: Secret
metadata:
  name: apisix-tls-certs
  namespace: api-gateway
  labels:
    app: apisix-gateway
type: kubernetes.io/tls
data:
  # Replace with actual TLS certificate and key
  tls.crt: LS0tLS1CRUdJTi... # Base64 encoded certificate
  tls.key: LS0tLS1CRUdJTi... # Base64 encoded private key

---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: apisix-gateway-tls
  namespace: api-gateway
spec:
  secretName: apisix-gateway-tls-cert
  issuerRef:
    name: external-ca-issuer
    kind: ClusterIssuer
  dnsNames:
  - "api.example.com"
  - "*.api.example.com"
  - "gateway.example.com"

---
# =============================================================================
# CONFIGMAPS
# =============================================================================

apiVersion: v1
kind: ConfigMap
metadata:
  name: apisix-env
  namespace: api-gateway
  labels:
    app: apisix-gateway
data:
  domain: "example.com"
  realm: "mlops"
  environment: "production"
  cluster: "mlops-cluster"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: apisix-dashboard-config
  namespace: api-gateway
  labels:
    app: apisix-dashboard
data:
  conf.yaml: |
    conf:
      listen:
        host: 0.0.0.0
        port: 9000
      etcd:
        endpoints:
          - http://etcd:2379
        prefix: /apisix
        timeout: 30
      log:
        error_log:
          level: warn
          file_path: logs/error.log
        access_log:
          file_path: logs/access.log
      security:
        access_control_allow_origin: "https://dashboard.example.com"
        access_control_allow_credentials: true
        content_security_policy: "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'"
        x_frame_options: "DENY"
      authentication:
        secret: "secret-for-dashboard-jwt"
        expire_time: 86400
        users:
          - username: admin
            password: "$2a$10$YyLfUqJD1a.1V7YTB5GnMu4BF2YrRQgx6LBONdqAEu7VJ2FWMjxWm"  # password: admin-password

---
# =============================================================================
# HORIZONTAL POD AUTOSCALER
# =============================================================================

apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: apisix-gateway-hpa
  namespace: api-gateway
  labels:
    app: apisix-gateway
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: apisix-gateway
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: http_requests_per_second
      target:
        type: AverageValue
        averageValue: "1000"
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
      - type: Pods
        value: 2
        periodSeconds: 60
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60

---
# =============================================================================
# POD DISRUPTION BUDGET
# =============================================================================

apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: apisix-gateway-pdb
  namespace: api-gateway
  labels:
    app: apisix-gateway
spec:
  maxUnavailable: 1
  selector:
    matchLabels:
      app: apisix-gateway

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: etcd-pdb
  namespace: api-gateway
  labels:
    app: etcd
spec:
  maxUnavailable: 1
  selector:
    matchLabels:
      app: etcd

---
# =============================================================================
# NETWORK POLICIES
# =============================================================================

apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: apisix-gateway-netpol
  namespace: api-gateway
  labels:
    app: apisix-gateway
spec:
  podSelector:
    matchLabels:
      app: apisix-gateway
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 9080
    - protocol: TCP
      port: 9443
    - protocol: TCP
      port: 9091
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: api-gateway
    ports:
    - protocol: TCP
      port: 2379
  - to:
    - namespaceSelector:
        matchLabels:
          name: mlops-toolchain
  - to:
    - namespaceSelector:
        matchLabels:
          name: processing
  - to:
    - namespaceSelector:
        matchLabels:
          name: monitoring
  - to:
    - namespaceSelector:
        matchLabels:
          name: object-store
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 443

---
# =============================================================================
# INGRESS FOR DASHBOARD ACCESS
# =============================================================================

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: apisix-dashboard-ingress
  namespace: api-gateway
  labels:
    app: apisix-dashboard
  annotations:
    cert-manager.io/cluster-issuer: external-ca-issuer
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: apisix-dashboard-auth
    nginx.ingress.kubernetes.io/auth-realm: "APISIX Dashboard - Authentication Required"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - dashboard.api.example.com
    secretName: apisix-dashboard-tls-cert
  rules:
  - host: dashboard.api.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: apisix-dashboard
            port:
              number: 9000