"""KASTCTL Agent for managing KAST package deployments and KPACK orchestration."""

import asyncio
import json
import logging
import os
import shutil
import subprocess
import time
import yaml
from typing import Dict, List, Optional, Tuple, Any, Set
from dataclasses import dataclass, field
from pathlib import Path
from enum import Enum

from mlopscli.agents.component_model import (
    ComponentRegistry, M<PERSON><PERSON><PERSON><PERSON>omponent, ComponentStatus, ComponentType,
    component_registry
)
from mlopscli.kast.manage_installation_base import KASTCTL_INSTALL_COMMAND, KASTCTL_UNINSTALL_COMMAND
from mlopscli.utils.system import run_command, is_command_available, get_arch
from mlopscli.utils.kubernetes import is_component_installed, is_helm_chart_installed
from mlopscli.utils.constants import (
    INSTALLATION_LOCATION_LOCAL_STR, INSTALLATION_LOCATION_REMOTE_STR,
    KAST_WORKING_DIR, <PERSON><PERSON>_AMD64, <PERSON><PERSON>_ARM64, SUPPORT<PERSON>_ARCH
)


class KPackType(Enum):
    """KPACK deployment types."""
    MINIMAL = "minimal"
    ABAC_MINIO = "abac_minio"
    MINIMAL_FALCO = "minimal_falco"
    MINIMAL_2NODES = "minimal_2nodes"
    CUSTOM = "custom"


class KPackStatus(Enum):
    """KPACK deployment status."""
    NOT_DEPLOYED = "not_deployed"
    DEPLOYING = "deploying"
    DEPLOYED = "deployed"
    FAILED = "failed"
    UPDATING = "updating"


@dataclass
class KPackConfiguration:
    """KPACK configuration definition."""
    name: str
    version: str
    kpack_type: KPackType
    namespace: str
    components: List[Dict[str, Any]] = field(default_factory=list)
    global_config: Dict[str, Any] = field(default_factory=dict)
    component_configs: Dict[str, Any] = field(default_factory=dict)
    install_flags: Dict[str, str] = field(default_factory=dict)
    file_path: Optional[str] = None


@dataclass
class KastctlValidationResult:
    """Result of KASTCTL validation."""
    component_name: str
    is_valid: bool
    issues: List[str]
    recommendations: List[str]
    kpack_assessment: Dict[str, Any]
    kastctl_version: Optional[str] = None


@dataclass
class KPackDeploymentResult:
    """Result of KPACK deployment."""
    kpack_name: str
    success: bool
    status: KPackStatus
    deployed_components: List[str] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    deployment_time: float = 0.0


class KastctlAgent:
    """
    Specialized agent for managing KASTCTL and KPACK deployments.
    
    Responsibilities:
    - KASTCTL binary management and validation
    - KPACK template management and customization
    - Component deployment via KASTCTL
    - KAST repository integration
    - Package version management
    - Deployment status monitoring
    - KPACK-based component lifecycle management
    """
    
    def __init__(self, installation_location: str = INSTALLATION_LOCATION_LOCAL_STR):
        self.installation_location = installation_location
        self.logger = logging.getLogger(f"{__name__}.KastctlAgent")
        self.kastctl_version: Optional[str] = None
        self.kpack_registry: Dict[str, KPackConfiguration] = {}
        self.deployment_status: Dict[str, KPackStatus] = {}
        
        # Initialize standard KPACK configurations
        self._initialize_standard_kpacks()
        
    def _initialize_standard_kpacks(self) -> None:
        """Initialize standard KPACK configurations from YAML files."""
        kpack_base_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "yaml", "kpack")
        
        if not os.path.exists(kpack_base_dir):
            self.logger.warning(f"KPACK directory not found: {kpack_base_dir}")
            return
        
        # Standard KPACK components
        standard_kpacks = [
            "clickhouse", "keycloak", "minio", "postgresql", 
            "argo", "prometheus-grafana", "mlflow", "certmanager", "ingress"
        ]
        
        for kpack_name in standard_kpacks:
            kpack_dir = os.path.join(kpack_base_dir, kpack_name)
            kpack_file = os.path.join(kpack_dir, "kpack.yaml")
            
            if os.path.exists(kpack_file):
                try:
                    with open(kpack_file, 'r') as f:
                        kpack_data = yaml.safe_load(f)
                    
                    kpack_config = self._parse_kpack_configuration(kpack_name, kpack_data, kpack_file)
                    self.register_kpack(kpack_config)
                    
                except Exception as e:
                    self.logger.error(f"Failed to load KPACK {kpack_name}: {str(e)}")
    
    def _parse_kpack_configuration(self, name: str, kpack_data: Dict[str, Any], file_path: str) -> KPackConfiguration:
        """Parse KPACK YAML configuration into KPackConfiguration object."""
        components = kpack_data.get("components", [])
        
        # Determine namespace from first component or use default
        namespace = "default"
        if components:
            namespace = components[0].get("namespace", "default")
        
        # Extract global configuration
        global_config = kpack_data.get("global", {})
        
        # Extract component-specific configurations
        component_configs = {}
        for key, value in kpack_data.items():
            if key not in ["components", "global"] and isinstance(value, dict):
                component_configs[key] = value
        
        return KPackConfiguration(
            name=name,
            version=components[0].get("version", "unknown") if components else "unknown",
            kpack_type=KPackType.CUSTOM,
            namespace=namespace,
            components=components,
            global_config=global_config,
            component_configs=component_configs,
            file_path=file_path
        )
    
    def register_kpack(self, kpack_config: KPackConfiguration) -> None:
        """Register a KPACK configuration."""
        self.kpack_registry[kpack_config.name] = kpack_config
        self.deployment_status[kpack_config.name] = KPackStatus.NOT_DEPLOYED
        self.logger.info(f"Registered KPACK: {kpack_config.name}")
    
    async def validate_kastctl_prerequisites(self) -> KastctlValidationResult:
        """Validate KASTCTL prerequisites and environment."""
        issues = []
        recommendations = []
        kpack_assessment = {}
        
        # Check if kastctl is available
        if not is_command_available("kastctl"):
            issues.append("kastctl command not found - KASTCTL must be installed")
            recommendations.append("Install KASTCTL from Thales Digital artifactory")
        else:
            # Get kastctl version
            try:
                result = subprocess.run([
                    "kastctl", "version"
                ], capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    self.kastctl_version = result.stdout.strip()
                    self.logger.info(f"KASTCTL version: {self.kastctl_version}")
                else:
                    issues.append("Could not determine KASTCTL version")
            except (subprocess.TimeoutExpired, subprocess.CalledProcessError) as e:
                issues.append(f"KASTCTL version check failed: {str(e)}")
        
        # Validate architecture support
        arch = get_arch()
        if arch not in SUPPORTED_ARCH:
            issues.append(f"Unsupported architecture: {arch}")
        
        # Check KAST working directory
        if not os.path.exists(KAST_WORKING_DIR):
            try:
                os.makedirs(KAST_WORKING_DIR, exist_ok=True)
                self.logger.info(f"Created KAST working directory: {KAST_WORKING_DIR}")
            except OSError as e:
                issues.append(f"Cannot create KAST working directory: {str(e)}")
        
        # Validate KPACK files
        kpack_assessment = await self._assess_kpack_files()
        
        # Check Docker availability (required for some KPACK operations)
        if not is_command_available("docker"):
            issues.append("Docker not available - required for some KPACK deployments")
            recommendations.append("Install Docker container runtime")
        
        # Check kubectl availability
        if not is_command_available("kubectl"):
            issues.append("kubectl not available - required for Kubernetes operations")
            recommendations.append("Install and configure kubectl")
        
        # Generate additional recommendations
        if not issues:
            recommendations.extend([
                "Ensure KAST repository credentials are configured",
                "Verify network connectivity to Thales Digital artifactory",
                "Consider setting up KPACK template customizations",
                "Configure KAST backup and recovery procedures"
            ])
        
        return KastctlValidationResult(
            component_name="kastctl",
            is_valid=len(issues) == 0,
            issues=issues,
            recommendations=recommendations,
            kpack_assessment=kpack_assessment,
            kastctl_version=self.kastctl_version
        )
    
    async def _assess_kpack_files(self) -> Dict[str, Any]:
        """Assess available KPACK files and their validity."""
        assessment = {
            "total_kpacks": len(self.kpack_registry),
            "valid_kpacks": 0,
            "invalid_kpacks": 0,
            "kpack_details": {},
            "missing_templates": []
        }
        
        for kpack_name, kpack_config in self.kpack_registry.items():
            kpack_detail = {
                "name": kpack_name,
                "version": kpack_config.version,
                "components": len(kpack_config.components),
                "file_exists": False,
                "valid_yaml": False,
                "issues": []
            }
            
            if kpack_config.file_path and os.path.exists(kpack_config.file_path):
                kpack_detail["file_exists"] = True
                
                try:
                    # Validate YAML structure
                    with open(kpack_config.file_path, 'r') as f:
                        yaml.safe_load(f)
                    kpack_detail["valid_yaml"] = True
                    assessment["valid_kpacks"] += 1
                except yaml.YAMLError as e:
                    kpack_detail["issues"].append(f"Invalid YAML: {str(e)}")
                    assessment["invalid_kpacks"] += 1
            else:
                kpack_detail["issues"].append("KPACK file not found")
                assessment["missing_templates"].append(kpack_name)
                assessment["invalid_kpacks"] += 1
            
            assessment["kpack_details"][kpack_name] = kpack_detail
        
        return assessment
    
    async def deploy_kpack(self, kpack_name: str, config: Optional[Dict[str, Any]] = None) -> KPackDeploymentResult:
        """
        Deploy a KPACK using KASTCTL.
        
        Args:
            kpack_name: Name of the KPACK to deploy
            config: Additional configuration parameters
            
        Returns:
            KPackDeploymentResult with deployment information
        """
        config = config or {}
        start_time = time.time()
        
        kpack_config = self.kpack_registry.get(kpack_name)
        if not kpack_config:
            return KPackDeploymentResult(
                kpack_name=kpack_name,
                success=False,
                status=KPackStatus.FAILED,
                errors=[f"KPACK {kpack_name} not found in registry"]
            )
        
        self.deployment_status[kpack_name] = KPackStatus.DEPLOYING
        
        try:
            self.logger.info(f"Starting KPACK deployment: {kpack_name}")
            
            # Prepare KPACK file
            kpack_file = await self._prepare_kpack_file(kpack_config, config)
            if not kpack_file:
                return KPackDeploymentResult(
                    kpack_name=kpack_name,
                    success=False,
                    status=KPackStatus.FAILED,
                    errors=["Failed to prepare KPACK file"],
                    deployment_time=time.time() - start_time
                )
            
            # Execute KASTCTL deployment
            success, errors = await self._execute_kastctl_install(kpack_name, kpack_file, config)
            
            if success:
                self.deployment_status[kpack_name] = KPackStatus.DEPLOYED
                
                # Wait for deployment to be ready
                deployed_components = await self._wait_for_deployment_ready(kpack_config)
                
                return KPackDeploymentResult(
                    kpack_name=kpack_name,
                    success=True,
                    status=KPackStatus.DEPLOYED,
                    deployed_components=deployed_components,
                    deployment_time=time.time() - start_time
                )
            else:
                self.deployment_status[kpack_name] = KPackStatus.FAILED
                return KPackDeploymentResult(
                    kpack_name=kpack_name,
                    success=False,
                    status=KPackStatus.FAILED,
                    errors=errors,
                    deployment_time=time.time() - start_time
                )
                
        except Exception as e:
            self.deployment_status[kpack_name] = KPackStatus.FAILED
            return KPackDeploymentResult(
                kpack_name=kpack_name,
                success=False,
                status=KPackStatus.FAILED,
                errors=[f"Deployment exception: {str(e)}"],
                deployment_time=time.time() - start_time
            )
    
    async def _prepare_kpack_file(self, kpack_config: KPackConfiguration, config: Dict[str, Any]) -> Optional[str]:
        """Prepare KPACK file with customizations."""
        try:
            if not kpack_config.file_path or not os.path.exists(kpack_config.file_path):
                self.logger.error(f"KPACK file not found: {kpack_config.file_path}")
                return None
            
            # Create working directory for this KPACK
            kpack_work_dir = os.path.join(KAST_WORKING_DIR, "kpack", kpack_config.name)
            os.makedirs(kpack_work_dir, exist_ok=True)
            
            # Copy template to working directory
            target_file = os.path.join(kpack_work_dir, "kpack.yaml")
            shutil.copy2(kpack_config.file_path, target_file)
            
            # Apply customizations
            if config:
                await self._apply_kpack_customizations(target_file, kpack_config, config)
            
            return target_file
            
        except Exception as e:
            self.logger.error(f"Failed to prepare KPACK file: {str(e)}")
            return None
    
    async def _apply_kpack_customizations(self, kpack_file: str, kpack_config: KPackConfiguration, config: Dict[str, Any]) -> None:
        """Apply customizations to KPACK file."""
        try:
            with open(kpack_file, 'r') as f:
                kpack_data = yaml.safe_load(f)
            
            # Apply domain customizations
            if "domain" in config:
                domain = config["domain"]
                await self._update_kpack_domains(kpack_data, domain)
            
            # Apply resource customizations
            if "resources" in config:
                await self._update_kpack_resources(kpack_data, config["resources"])
            
            # Apply security customizations
            if "security" in config:
                await self._update_kpack_security(kpack_data, config["security"])
            
            # Apply component-specific customizations
            for component_name, component_config in config.get("components", {}).items():
                if component_name in kpack_data:
                    await self._merge_component_config(kpack_data[component_name], component_config)
            
            # Write updated KPACK file
            with open(kpack_file, 'w') as f:
                yaml.dump(kpack_data, f, default_flow_style=False, sort_keys=False)
            
            self.logger.info(f"Applied customizations to KPACK: {kpack_config.name}")
            
        except Exception as e:
            self.logger.error(f"Failed to apply KPACK customizations: {str(e)}")
            raise
    
    async def _update_kpack_domains(self, kpack_data: Dict[str, Any], domain: str) -> None:
        """Update domain references in KPACK configuration."""
        def update_hosts_recursive(obj):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    if key == "hosts" and isinstance(value, list):
                        # Update host entries to use new domain
                        for i, host in enumerate(value):
                            if isinstance(host, str) and ".dpsc" in host:
                                value[i] = host.replace(".dpsc", f".{domain}")
                    else:
                        update_hosts_recursive(value)
            elif isinstance(obj, list):
                for item in obj:
                    update_hosts_recursive(item)
        
        update_hosts_recursive(kpack_data)
    
    async def _update_kpack_resources(self, kpack_data: Dict[str, Any], resources: Dict[str, Any]) -> None:
        """Update resource requirements in KPACK configuration."""
        def update_resources_recursive(obj):
            if isinstance(obj, dict):
                if "resources" in obj and isinstance(obj["resources"], dict):
                    # Merge resource configurations
                    if "requests" in resources:
                        obj["resources"].setdefault("requests", {}).update(resources["requests"])
                    if "limits" in resources:
                        obj["resources"].setdefault("limits", {}).update(resources["limits"])
                else:
                    for value in obj.values():
                        update_resources_recursive(value)
            elif isinstance(obj, list):
                for item in obj:
                    update_resources_recursive(item)
        
        update_resources_recursive(kpack_data)
    
    async def _update_kpack_security(self, kpack_data: Dict[str, Any], security: Dict[str, Any]) -> None:
        """Update security configurations in KPACK."""
        # Update TLS configurations
        if "tls_enabled" in security and security["tls_enabled"]:
            def enable_tls_recursive(obj):
                if isinstance(obj, dict):
                    if "ingress" in obj and isinstance(obj["ingress"], dict):
                        obj["ingress"]["tls"] = True
                        if "annotations" not in obj["ingress"]:
                            obj["ingress"]["annotations"] = {}
                        obj["ingress"]["annotations"]["cert-manager.io/cluster-issuer"] = "external-ca-issuer"
                    else:
                        for value in obj.values():
                            enable_tls_recursive(value)
                elif isinstance(obj, list):
                    for item in obj:
                        enable_tls_recursive(item)
            
            enable_tls_recursive(kpack_data)
    
    async def _merge_component_config(self, target: Dict[str, Any], source: Dict[str, Any]) -> None:
        """Recursively merge component configuration."""
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                await self._merge_component_config(target[key], value)
            else:
                target[key] = value
    
    async def _execute_kastctl_install(self, kpack_name: str, kpack_file: str, config: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Execute KASTCTL install command."""
        try:
            kpack_dir = os.path.dirname(kpack_file)
            os.chdir(kpack_dir)
            
            # Build KASTCTL command
            command_parts = [
                "kastctl", "install",
                "--prompt=false",
                f"--file={os.path.basename(kpack_file)}"
            ]
            
            # Add optional parameters
            if config.get("repo_alias"):
                command_parts.append(f"--repo-alias={config['repo_alias']}")
            
            if config.get("namespace"):
                command_parts.append(f"--namespace={config['namespace']}")
            
            if config.get("force", False):
                command_parts.append("--force")
            
            if config.get("fetch", True):
                command_parts.append("--fetch=true")
            else:
                command_parts.append("--fetch=false")
            
            command = " ".join(command_parts)
            
            self.logger.info(f"Executing KASTCTL command: {command}")
            
            # Execute command with timeout
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=1800  # 30 minutes timeout
            )
            
            if result.returncode == 0:
                self.logger.info(f"KASTCTL deployment successful: {kpack_name}")
                return True, []
            else:
                error_msg = f"KASTCTL deployment failed: {result.stderr}"
                self.logger.error(error_msg)
                return False, [error_msg]
                
        except subprocess.TimeoutExpired:
            return False, ["KASTCTL deployment timed out"]
        except Exception as e:
            return False, [f"KASTCTL execution failed: {str(e)}"]
    
    async def _wait_for_deployment_ready(self, kpack_config: KPackConfiguration) -> List[str]:
        """Wait for KPACK deployment components to be ready."""
        deployed_components = []
        max_wait = 600  # 10 minutes
        wait_time = 0
        
        for component in kpack_config.components:
            component_name = component.get("name")
            if not component_name:
                continue
            
            self.logger.info(f"Waiting for component to be ready: {component_name}")
            
            # Wait for component to be installed
            while wait_time < max_wait:
                if await self._is_component_ready(component_name, kpack_config.namespace):
                    deployed_components.append(component_name)
                    break
                
                await asyncio.sleep(10)
                wait_time += 10
            else:
                self.logger.warning(f"Component {component_name} did not become ready within timeout")
        
        return deployed_components
    
    async def _is_component_ready(self, component_name: str, namespace: str) -> bool:
        """Check if a component is ready."""
        try:
            # Check if helm chart is installed
            if is_helm_chart_installed(component_name):
                # Check if pods are running
                result = subprocess.run([
                    "kubectl", "get", "pods", "-n", namespace,
                    "-l", f"app.kubernetes.io/name={component_name}",
                    "--no-headers"
                ], capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    pods = result.stdout.strip().split('\n')
                    if pods and pods[0].strip():
                        for pod in pods:
                            if pod.strip() and ("Running" not in pod or "1/1" not in pod):
                                return False
                        return True
            
            return False
            
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            return False
    
    async def uninstall_kpack(self, kpack_name: str) -> KPackDeploymentResult:
        """Uninstall a KPACK using KASTCTL."""
        start_time = time.time()
        
        kpack_config = self.kpack_registry.get(kpack_name)
        if not kpack_config:
            return KPackDeploymentResult(
                kpack_name=kpack_name,
                success=False,
                status=KPackStatus.FAILED,
                errors=[f"KPACK {kpack_name} not found in registry"]
            )
        
        try:
            self.logger.info(f"Starting KPACK uninstallation: {kpack_name}")
            
            # Build KASTCTL uninstall command
            if kpack_config.file_path:
                kpack_dir = os.path.dirname(kpack_config.file_path)
                os.chdir(kpack_dir)
                
                command = f"kastctl uninstall --prompt=false --file={os.path.basename(kpack_config.file_path)}"
                
                result = subprocess.run(
                    command,
                    shell=True,
                    capture_output=True,
                    text=True,
                    timeout=600  # 10 minutes timeout
                )
                
                if result.returncode == 0:
                    self.deployment_status[kpack_name] = KPackStatus.NOT_DEPLOYED
                    return KPackDeploymentResult(
                        kpack_name=kpack_name,
                        success=True,
                        status=KPackStatus.NOT_DEPLOYED,
                        deployment_time=time.time() - start_time
                    )
                else:
                    return KPackDeploymentResult(
                        kpack_name=kpack_name,
                        success=False,
                        status=KPackStatus.FAILED,
                        errors=[f"KASTCTL uninstall failed: {result.stderr}"],
                        deployment_time=time.time() - start_time
                    )
            else:
                return KPackDeploymentResult(
                    kpack_name=kpack_name,
                    success=False,
                    status=KPackStatus.FAILED,
                    errors=["KPACK file path not available"],
                    deployment_time=time.time() - start_time
                )
                
        except Exception as e:
            return KPackDeploymentResult(
                kpack_name=kpack_name,
                success=False,
                status=KPackStatus.FAILED,
                errors=[f"Uninstallation exception: {str(e)}"],
                deployment_time=time.time() - start_time
            )
    
    def get_kpack_status(self, kpack_name: str) -> Optional[KPackStatus]:
        """Get deployment status of a KPACK."""
        return self.deployment_status.get(kpack_name)
    
    def list_available_kpacks(self) -> Dict[str, Dict[str, Any]]:
        """List all available KPACKs."""
        kpack_list = {}
        
        for name, config in self.kpack_registry.items():
            kpack_list[name] = {
                "name": name,
                "version": config.version,
                "type": config.kpack_type.value,
                "namespace": config.namespace,
                "components": [comp.get("name") for comp in config.components],
                "status": self.deployment_status.get(name, KPackStatus.NOT_DEPLOYED).value,
                "file_path": config.file_path
            }
        
        return kpack_list
    
    async def validate_kpack_components(self, kpack_name: str) -> Dict[str, Any]:
        """Validate components within a KPACK."""
        kpack_config = self.kpack_registry.get(kpack_name)
        if not kpack_config:
            return {"error": f"KPACK {kpack_name} not found"}
        
        validation_results = {
            "kpack_name": kpack_name,
            "total_components": len(kpack_config.components),
            "component_validations": [],
            "overall_valid": True
        }
        
        for component in kpack_config.components:
            component_name = component.get("name")
            component_version = component.get("version")
            component_namespace = component.get("namespace", kpack_config.namespace)
            
            component_validation = {
                "name": component_name,
                "version": component_version,
                "namespace": component_namespace,
                "is_deployed": await self._is_component_ready(component_name, component_namespace),
                "issues": []
            }
            
            # Check for component-specific issues
            if not component_name:
                component_validation["issues"].append("Component name not specified")
                validation_results["overall_valid"] = False
            
            if not component_version:
                component_validation["issues"].append("Component version not specified")
            
            validation_results["component_validations"].append(component_validation)
        
        return validation_results
    
    def get_kastctl_version(self) -> Optional[str]:
        """Get KASTCTL version."""
        return self.kastctl_version
    
    def get_deployment_status_summary(self) -> Dict[str, Any]:
        """Get summary of all KPACK deployment statuses."""
        status_counts = {}
        for status in KPackStatus:
            status_counts[status.value] = 0
        
        for status in self.deployment_status.values():
            status_counts[status.value] += 1
        
        return {
            "total_kpacks": len(self.kpack_registry),
            "status_counts": status_counts,
            "kpack_statuses": {
                name: status.value for name, status in self.deployment_status.items()
            }
        }