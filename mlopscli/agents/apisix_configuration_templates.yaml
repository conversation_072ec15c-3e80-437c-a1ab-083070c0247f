# Apache APISIX Enterprise Configuration Templates
# This file contains production-ready configuration templates for Apache APISIX

# =============================================================================
# CORE APISIX CONFIGURATION
# =============================================================================

apiVersion: v1
kind: ConfigMap
metadata:
  name: apisix-config
  namespace: api-gateway
data:
  config.yaml: |
    apisix:
      node_listen: 9080
      enable_ipv6: false
      enable_control: true
      control:
        ip: "0.0.0.0"
        port: 9092
      
      # Router configuration
      router:
        http: 'radixtree_uri'
        ssl: 'radixtree_sni'
      
      # Stream proxy configuration
      stream_proxy:
        only: false
        tcp:
          - addr: 9100
        udp:
          - addr: 9200
    
    deployment:
      role: traditional
      role_traditional:
        config_provider: etcd
      
      admin:
        admin_key:
          - name: "admin"
            key: "${ADMIN_API_KEY}"
            role: admin
          - name: "viewer"
            key: "${VIEWER_API_KEY}"
            role: viewer
        enable_admin_cors: true
        admin_listen:
          ip: 0.0.0.0
          port: 9180
        https_admin: true
        admin_ssl_cert: "/ssl/admin.crt"
        admin_ssl_cert_key: "/ssl/admin.key"
    
    # etcd configuration
    etcd:
      host:
        - "https://etcd-0.etcd:2379"
        - "https://etcd-1.etcd:2379"
        - "https://etcd-2.etcd:2379"
      prefix: "/apisix"
      timeout: 30
      user: "${ETCD_USER}"
      password: "${ETCD_PASSWORD}"
      tls:
        cert: "/etcd-ssl/etcd-client.crt"
        key: "/etcd-ssl/etcd-client.key"
        ca: "/etcd-ssl/etcd-ca.crt"
        verify: true
    
    # Plugin configuration
    plugin_attr:
      prometheus:
        export_addr:
          ip: 0.0.0.0
          port: 9091
        export_uri: /apisix/prometheus/metrics
        metric_prefix: apisix_
        enable_export_server: true
      
      skywalking:
        service_name: apisix-gateway
        service_instance_name: "${HOSTNAME}"
        endpoint_addr: http://skywalking-oap:12800
        report_interval: 3
      
      opentelemetry:
        trace_id_source: x-request-id
        resource:
          service.name: apisix-gateway
          service.version: 3.7.0
        collector:
          address: jaeger-collector:14268
          request_timeout: 3
          request_headers:
            Authorization: "Bearer ${JAEGER_TOKEN}"
      
      log-rotate:
        interval: 3600    # 1 hour
        max_kept: 168     # 7 days
        enable_compression: true
      
      server-info:
        report_ttl: 60
        report_interval: 36
    
    # Nginx configuration
    nginx_config:
      error_log: "/dev/stderr"
      error_log_level: "warn"
      worker_processes: "auto"
      enable_cpu_affinity: true
      worker_rlimit_nofile: 20480
      worker_shutdown_timeout: "240s"
      
      event:
        worker_connections: 10620
        use: epoll
        multi_accept: "on"
      
      http:
        access_log: "/dev/stdout"
        keepalive_timeout: "60s"
        client_header_timeout: "60s"
        client_body_timeout: "60s"
        send_timeout: "10s"
        underscores_in_headers: "on"
        real_ip_header: "X-Real-IP"
        real_ip_recursive: "on"
        real_ip_from:
          - "127.0.0.1"
          - "**********/12"
          - "10.0.0.0/8"
          - "***********/16"

---
# =============================================================================
# ENTERPRISE SECURITY CONFIGURATION
# =============================================================================

apiVersion: v1
kind: ConfigMap
metadata:
  name: apisix-security-config
  namespace: api-gateway
data:
  security.yaml: |
    # Global security plugins
    global_plugins:
      - name: "request-id"
        config:
          header_name: "X-Request-ID"
          include_in_response: true
          algorithm: "uuid"
      
      - name: "real-ip"
        config:
          source: "http_x_forwarded_for"
          trusted_addresses:
            - "10.0.0.0/8"
            - "**********/12"
            - "***********/16"
      
      - name: "prometheus"
        config:
          prefer_name: true
          default_labels:
            service_name: "apisix-gateway"
            environment: "${ENVIRONMENT}"
            cluster: "${CLUSTER_NAME}"
      
      - name: "cors"
        config:
          allow_origins: "https://*.${DOMAIN}"
          allow_methods: "GET,POST,PUT,DELETE,PATCH,HEAD,OPTIONS"
          allow_headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-API-Key,X-Request-ID"
          expose_headers: "Content-Length,Content-Range,X-Request-ID,X-Response-Time"
          max_age: 86400
          allow_credential: true
    
    # Security policies
    security_policies:
      # WAF Protection
      waf:
        enabled: true
        mode: "protection"  # detection, protection
        paranoia_level: 2
        rules:
          - id: "200001"
            description: "Block SQL injection attempts"
            pattern: "(?i)(union|select|insert|delete|update|drop|create|alter|exec|execute)"
            action: "deny"
          - id: "200002"
            description: "Block XSS attempts"
            pattern: "(?i)(<script|javascript:|onerror|onload|onclick)"
            action: "deny"
          - id: "200003"
            description: "Block directory traversal"
            pattern: "(\\.\\./|\\.\\\\)"
            action: "deny"
      
      # Rate limiting policies
      rate_limiting:
        global:
          rate: 1000
          burst: 2000
          key_type: "var"
          key: "remote_addr"
          rejected_code: 429
          rejected_msg: "Rate limit exceeded"
          policy: "redis-cluster"
        
        authenticated:
          rate: 5000
          burst: 10000
          key_type: "var"
          key: "consumer_name"
          rejected_code: 429
          rejected_msg: "Rate limit exceeded for authenticated users"
        
        admin:
          rate: 100
          burst: 200
          key_type: "var"
          key: "consumer_name"
          rejected_code: 429
          rejected_msg: "Admin rate limit exceeded"
      
      # IP restrictions
      ip_restrictions:
        admin_whitelist:
          - "10.0.0.0/8"
          - "**********/12"
          - "***********/16"
        
        global_blacklist:
          - "*************"  # Example blocked IP
        
        geo_blocking:
          blocked_countries: ["CN", "RU", "KP"]  # Example blocked countries

---
# =============================================================================
# AUTHENTICATION CONFIGURATION
# =============================================================================

apiVersion: v1
kind: ConfigMap
metadata:
  name: apisix-auth-config
  namespace: api-gateway
data:
  auth.yaml: |
    # JWT Authentication configuration
    jwt_auth:
      secret: "${JWT_SECRET}"
      algorithm: "HS256"
      exp: 86400  # 24 hours
      base64_secret: false
      header: "Authorization"
      query: "jwt"
      cookie: "jwt"
      hide_credentials: true
      clock_skew: 60  # Allow 60 seconds clock skew
    
    # OpenID Connect configuration
    openid_connect:
      client_id: "${OIDC_CLIENT_ID}"
      client_secret: "${OIDC_CLIENT_SECRET}"
      discovery: "https://keycloak.${DOMAIN}/auth/realms/${REALM}/.well-known/openid_configuration"
      scope: "openid profile email groups"
      bearer_only: false
      use_nonce: true
      introspection_endpoint_auth_method: "client_secret_post"
      token_endpoint_auth_method: "client_secret_post"
      ssl_verify: true
      timeout: 3000
      access_token_in_authorization_header: true
      password_grant_token_generation_incoming_uri: "/api/v1/oauth/token"
    
    # Keycloak Authorization
    authz_keycloak:
      discovery: "https://keycloak.${DOMAIN}/auth/realms/${REALM}/.well-known/openid_configuration"
      client_id: "${KEYCLOAK_CLIENT_ID}"
      client_secret: "${KEYCLOAK_CLIENT_SECRET}"
      token_endpoint_auth_method: "client_secret_post"
      resource_registration_endpoint: "https://keycloak.${DOMAIN}/auth/realms/${REALM}/authz/protection/resource_set"
      permissions:
        - "resource_name:scope"
      audience: "${KEYCLOAK_AUDIENCE}"
      ssl_verify: true
      policy_enforcement_mode: "ENFORCING"
    
    # API Key Authentication
    key_auth:
      header: "X-API-KEY"
      query: "api-key"
      hide_credentials: true
    
    # Basic Authentication
    basic_auth:
      hide_credentials: true
    
    # HMAC Authentication
    hmac_auth:
      access_key: "X-HMAC-ACCESS-KEY"
      signed_headers: ["host", "date", "x-custom-header"]
      algorithm: "hmac-sha256"
      clock_skew: 300
      hide_credentials: true

---
# =============================================================================
# SERVICE DISCOVERY CONFIGURATION
# =============================================================================

apiVersion: v1
kind: ConfigMap
metadata:
  name: apisix-discovery-config
  namespace: api-gateway
data:
  discovery.yaml: |
    # Kubernetes service discovery
    kubernetes:
      service:
        schema: "http"
        host: "${KUBERNETES_SERVICE_HOST}"
        port: "${KUBERNETES_SERVICE_PORT}"
      client:
        token_file: "/var/run/secrets/kubernetes.io/serviceaccount/token"
        ca_file: "/var/run/secrets/kubernetes.io/serviceaccount/ca.crt"
      default_weight: 50
      
      # Watch specific namespaces
      watch_endpoint_slices: true
      namespaces:
        - "mlops-toolchain"
        - "processing"
        - "monitoring"
        - "object-store"
        - "lakefs"
    
    # DNS-based service discovery
    dns:
      servers:
        - "kube-dns.kube-system.svc.cluster.local:53"
        - "*******:53"
      order: ["last", "SRV", "A", "AAAA", "CNAME"]
    
    # Consul service discovery (optional)
    consul:
      servers:
        - "http://consul:8500"
      prefix: "upstreams"
      skip_services:
        - "consul"
      timeout:
        connect: 2000
        read: 2000
        wait: 60
      weight: 1
      fetch_interval: 3
      default_args:
        scheme: "http"

---
# =============================================================================
# ROUTE CONFIGURATIONS
# =============================================================================

apiVersion: v1
kind: ConfigMap
metadata:
  name: apisix-routes-config
  namespace: api-gateway
data:
  routes.yaml: |
    # Polycore GRPC Services
    polycore_grpc:
      uri: "/polyflow.v1.*"
      methods: ["POST"]
      host: "polycore.${DOMAIN}"
      upstream:
        type: "roundrobin"
        scheme: "grpc"
        discovery_type: "kubernetes"
        service_name: "polycore.mlops-toolchain.svc.cluster.local"
        service_port: 8080
      plugins:
        - name: "grpc-transcode"
          config:
            proto_id: "polycore-proto"
            service: "polyflow.v1.PolyCoreService"
        - name: "jwt-auth"
        - name: "authz-keycloak"
          config:
            permissions: ["polycore:read", "polycore:write"]
        - name: "limit-rate"
          config:
            rate: 1000
            burst: 2000
    
    # Polycore REST API
    polycore_rest:
      uri: "/api/v1/polycore/*"
      methods: ["GET", "POST", "PUT", "DELETE", "PATCH"]
      host: "polycore-obs.${DOMAIN}"
      upstream:
        type: "roundrobin"
        scheme: "http"
        discovery_type: "kubernetes"
        service_name: "polycore-rest.mlops-toolchain.svc.cluster.local"
        service_port: 8081
      plugins:
        - name: "jwt-auth"
        - name: "authz-keycloak"
          config:
            permissions: ["polycore:read", "polycore:write"]
        - name: "limit-rate"
          config:
            rate: 500
            burst: 1000
        - name: "proxy-rewrite"
          config:
            regex_uri: ["^/api/v1/polycore/(.*)", "/$1"]
    
    # MLflow API
    mlflow_api:
      uri: "/api/2.0/mlflow/*"
      methods: ["GET", "POST", "PUT", "DELETE", "PATCH"]
      host: "mlflow.${DOMAIN}"
      upstream:
        type: "roundrobin"
        scheme: "http"
        discovery_type: "kubernetes"
        service_name: "mlflow.processing.svc.cluster.local"
        service_port: 5000
      plugins:
        - name: "jwt-auth"
        - name: "authz-keycloak"
          config:
            permissions: ["mlflow:read", "mlflow:write"]
        - name: "limit-rate"
          config:
            rate: 200
            burst: 400
    
    # Keycloak Authentication
    keycloak_auth:
      uri: "/auth/*"
      methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
      host: "keycloak.${DOMAIN}"
      upstream:
        type: "roundrobin"
        scheme: "http"
        discovery_type: "kubernetes"
        service_name: "keycloak-http.processing.svc.cluster.local"
        service_port: 80
      plugins:
        - name: "limit-rate"
          config:
            rate: 100
            burst: 200
            key: "remote_addr"
    
    # MinIO S3 API
    minio_api:
      uri: "/minio/*"
      methods: ["GET", "POST", "PUT", "DELETE", "HEAD"]
      host: "minio.${DOMAIN}"
      upstream:
        type: "roundrobin"
        scheme: "https"
        discovery_type: "kubernetes"
        service_name: "minio.object-store.svc.cluster.local"
        service_port: 9000
      plugins:
        - name: "jwt-auth"
        - name: "authz-keycloak"
          config:
            permissions: ["storage:read", "storage:write"]
        - name: "limit-rate"
          config:
            rate: 1000
            burst: 2000
        - name: "proxy-rewrite"
          config:
            regex_uri: ["^/minio/(.*)", "/$1"]
    
    # Grafana Dashboard
    grafana_dashboard:
      uri: "/grafana/*"
      methods: ["GET", "POST", "PUT", "DELETE", "PATCH"]
      host: "grafana.admin.${DOMAIN}"
      upstream:
        type: "roundrobin"
        scheme: "http"
        discovery_type: "kubernetes"
        service_name: "grafana.monitoring.svc.cluster.local"
        service_port: 3000
      plugins:
        - name: "jwt-auth"
        - name: "authz-keycloak"
          config:
            permissions: ["monitoring:read", "monitoring:admin"]
        - name: "limit-rate"
          config:
            rate: 50
            burst: 100
        - name: "proxy-rewrite"
          config:
            regex_uri: ["^/grafana/(.*)", "/$1"]
    
    # Argo Workflows
    argo_workflows:
      uri: "/argo/*"
      methods: ["GET", "POST", "PUT", "DELETE", "PATCH"]
      host: "argo.${DOMAIN}"
      upstream:
        type: "roundrobin"
        scheme: "http"
        discovery_type: "kubernetes"
        service_name: "argo-processing-server.processing.svc.cluster.local"
        service_port: 2746
      plugins:
        - name: "jwt-auth"
        - name: "authz-keycloak"
          config:
            permissions: ["workflows:read", "workflows:write"]
        - name: "limit-rate"
          config:
            rate: 100
            burst: 200
        - name: "proxy-rewrite"
          config:
            regex_uri: ["^/argo/(.*)", "/$1"]

---
# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================

apiVersion: v1
kind: ConfigMap
metadata:
  name: apisix-monitoring-config
  namespace: api-gateway
data:
  monitoring.yaml: |
    # Prometheus metrics configuration
    prometheus:
      export_addr:
        ip: "0.0.0.0"
        port: 9091
      export_uri: "/apisix/prometheus/metrics"
      metric_prefix: "apisix_"
      enable_export_server: true
      default_labels:
        service_name: "apisix-gateway"
        environment: "${ENVIRONMENT}"
        cluster: "${CLUSTER_NAME}"
        version: "3.7.0"
    
    # OpenTelemetry tracing
    opentelemetry:
      sampler:
        name: "trace_id_ratio"
        options:
          fraction: 0.1  # Sample 10% of traces
      resource:
        service.name: "apisix-gateway"
        service.version: "3.7.0"
        service.namespace: "api-gateway"
        deployment.environment: "${ENVIRONMENT}"
      collector:
        address: "jaeger-collector:14268"
        request_timeout: 3
        request_headers:
          Authorization: "Bearer ${JAEGER_TOKEN}"
      batch_span_processor:
        drop_on_queue_full: false
        max_export_batch_size: 256
        max_queue_size: 2048
        schedule_delay_millis: 5000
        export_timeout_millis: 30000
    
    # SkyWalking APM
    skywalking:
      endpoint_addr: "http://skywalking-oap:12800"
      service_name: "apisix-gateway"
      service_instance_name: "${HOSTNAME}"
      report_interval: 3
      authentication: "${SKYWALKING_TOKEN}"
    
    # ClickHouse logging
    clickhouse_logger:
      uri: "http://clickhouse:8123"
      database: "apisix_logs"
      table: "access_logs"
      logtable: "error_logs"
      user: "${CLICKHOUSE_USER}"
      password: "${CLICKHOUSE_PASSWORD}"
      timeout: 3000
      retry_delay: 1
      batch_max_size: 1000
      inactive_timeout: 5
      buffer_duration: 60
      max_retry_count: 0
      ssl_verify: false
    
    # File logging
    file_logger:
      path: "/var/log/apisix/access.log"
      log_format:
        host: "$host"
        client_ip: "$remote_addr"
        timestamp: "$time_iso8601"
        method: "$request_method"
        uri: "$request_uri"
        status: "$status"
        size: "$body_bytes_sent"
        referer: "$http_referer"
        user_agent: "$http_user_agent"
        request_id: "$http_x_request_id"
        response_time: "$upstream_response_time"
        upstream_addr: "$upstream_addr"
        upstream_status: "$upstream_status"
    
    # Health check configuration
    health_checks:
      active:
        enabled: true
        http_path: "/health"
        host: "api-gateway.${DOMAIN}"
        port: 9080
        https_verify_certificate: false
        healthy:
          interval: 5
          http_statuses: [200, 302]
          successes: 2
        unhealthy:
          interval: 5
          http_statuses: [429, 404, 500, 501, 502, 503, 504, 505]
          http_failures: 3
          timeouts: 3
      
      passive:
        enabled: true
        healthy:
          http_statuses: [200, 201, 202, 203, 204, 205, 206, 300, 301, 302, 303, 304, 305, 306, 307, 308]
          successes: 3
        unhealthy:
          http_statuses: [429, 500, 503]
          http_failures: 3
          timeouts: 3

---
# =============================================================================
# ADVANCED PLUGIN CONFIGURATIONS
# =============================================================================

apiVersion: v1
kind: ConfigMap
metadata:
  name: apisix-advanced-plugins
  namespace: api-gateway
data:
  advanced-plugins.yaml: |
    # Circuit breaker configuration
    fault_injection:
      abort:
        http_status: 503
        percentage: 0  # Disabled by default
        body: "Service temporarily unavailable"
      delay:
        duration: 0  # No artificial delay by default
        percentage: 0
    
    # Request/Response transformation
    proxy_rewrite:
      scheme: "https"
      headers:
        set:
          X-API-Version: "v1"
          X-Forwarded-Proto: "https"
          X-Real-IP: "$remote_addr"
        remove:
          - "Server"
          - "X-Powered-By"
    
    response_rewrite:
      status_code: 200
      headers:
        set:
          X-API-Gateway: "APISIX"
          X-Response-Time: "$upstream_response_time"
          Cache-Control: "no-cache, no-store, must-revalidate"
          X-Content-Type-Options: "nosniff"
          X-Frame-Options: "DENY"
          X-XSS-Protection: "1; mode=block"
        remove:
          - "Server"
          - "X-Powered-By"
    
    # Request validation
    request_validation:
      header_schema:
        type: "object"
        properties:
          Authorization:
            type: "string"
            pattern: "^Bearer .+"
          Content-Type:
            type: "string"
            enum: ["application/json", "application/grpc"]
      body_schema:
        type: "object"
        required: ["action"]
        properties:
          action:
            type: "string"
            enum: ["create", "read", "update", "delete"]
          data:
            type: "object"
    
    # Caching configuration
    proxy_cache:
      cache_strategy: "disk"
      cache_zone: "disk_cache_one"
      cache_key: ["$host", "$request_uri"]
      cache_bypass: ["$arg_bypass"]  
      cache_method: ["GET", "HEAD"]
      cache_http_status: [200, 301, 404]
      hide_cache_headers: false
      cache_control: false
      no_cache: ["$arg_test"]
    
    # WebSocket proxy
    websocket:
      enabled: true
      timeout: 60000
      max_payload_len: 65535
    
    # gRPC transcode
    grpc_transcode:
      proto_id: "polycore-proto"
      service: "polyflow.v1.PolyCoreService"
      method: "*"
      deadline: 5.0
      pb_option: ["int64_as_number", "int64_as_string"]
    
    # Workflow orchestration
    workflow:
      rules:
        - case:
            - ["uri", "==", "/api/v1/models"]
            - ["request_method", "==", "POST"]
          actions:
            - [
                "limit-rate",
                {
                  "rate": 10,
                  "burst": 20,
                  "rejected_code": 429
                }
              ]
        - case:
            - ["uri", "==", "/api/v1/admin/*"]
          actions:
            - [
                "ip-restriction",
                {
                  "whitelist": ["10.0.0.0/8", "**********/12"]
                }
              ]