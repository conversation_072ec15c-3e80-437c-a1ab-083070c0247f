# KServe Integration with Apache APISIX Configuration
# Dynamic model serving configuration templates

---
# =============================================================================
# KSERVE CUSTOM DISCOVERY PLUGIN FOR APISIX
# =============================================================================

apiVersion: v1
kind: ConfigMap
metadata:
  name: apisix-kserve-discovery
  namespace: api-gateway
data:
  kserve-discovery.lua: |
    -- KServe InferenceService Discovery Plugin for APISIX
    local core = require("apisix.core")
    local http = require("resty.http")
    local json = require("cjson")
    local ipairs = ipairs
    local pairs = pairs
    local string_format = string.format
    local ngx_timer_at = ngx.timer.at
    local ngx_timer_every = ngx.timer.every
    
    local plugin_name = "kserve-discovery"
    local schema = {
        type = "object",
        properties = {
            kubernetes_host = {
                type = "string",
                default = "https://kubernetes.default.svc"
            },
            namespace = {
                type = "string",
                default = "kserve-test"
            },
            sync_interval = {
                type = "integer",
                default = 30
            },
            service_port = {
                type = "integer", 
                default = 80
            }
        }
    }
    
    local _M = {}
    
    function _M.check_schema(conf)
        return core.schema.check(schema, conf)
    end
    
    local function fetch_inference_services(conf)
        local httpc = http.new()
        httpc:set_timeout(5000)
        
        local token_file = "/var/run/secrets/kubernetes.io/serviceaccount/token"
        local token = core.io.get_file(token_file)
        if not token then
            core.log.error("failed to read service account token")
            return nil
        end
        
        local headers = {
            ["Authorization"] = "Bearer " .. token,
            ["Content-Type"] = "application/json"
        }
        
        local url = string_format("%s/apis/serving.kserve.io/v1beta1/namespaces/%s/inferenceservices",
                                  conf.kubernetes_host, conf.namespace)
        
        local res, err = httpc:request_uri(url, {
            method = "GET",
            headers = headers,
            ssl_verify = false
        })
        
        if not res then
            core.log.error("failed to fetch inference services: ", err)
            return nil
        end
        
        if res.status ~= 200 then
            core.log.error("kubernetes API returned status: ", res.status)
            return nil
        end
        
        local data = json.decode(res.body)
        return data.items
    end
    
    local function create_upstream_nodes(inference_services, conf)
        local nodes = {}
        
        for _, service in ipairs(inference_services) do
            local name = service.metadata.name
            local namespace = service.metadata.namespace
            local status = service.status
            
            if status and status.url then
                -- Extract host from the URL
                local host = status.url:match("https?://([^/]+)")
                if host then
                    nodes[host .. ":" .. conf.service_port] = 1
                    core.log.info("discovered inference service: ", name, " at ", host)
                end
            end
        end
        
        return nodes
    end
    
    local function update_apisix_routes(inference_services, conf)
        for _, service in ipairs(inference_services) do
            local name = service.metadata.name
            local namespace = service.metadata.namespace
            local annotations = service.metadata.annotations or {}
            
            -- Create route configuration
            local route_config = {
                uri = "/models/" .. name .. "/*",
                methods = {"GET", "POST", "PUT", "DELETE"},
                upstream = {
                    type = "roundrobin",
                    nodes = create_upstream_nodes({service}, conf),
                    checks = {
                        active = {
                            http_path = "/v1/models/" .. name,
                            healthy = {
                                interval = 5,
                                successes = 2
                            },
                            unhealthy = {
                                interval = 5,
                                http_failures = 3
                            }
                        }
                    }
                },
                plugins = {
                    ["proxy-rewrite"] = {
                        regex_uri = {"^/models/" .. name .. "/(.*)", "/v1/models/" .. name .. "/$1"}
                    },
                    ["prometheus"] = {
                        prefer_name = true,
                        default_labels = {
                            model_name = name,
                            model_namespace = namespace,
                            model_version = annotations["serving.kserve.io/model-version"] or "unknown"
                        }
                    }
                }
            }
            
            -- Apply authentication if specified
            if annotations["kserve.io/auth-required"] == "true" then
                route_config.plugins["jwt-auth"] = {}
                route_config.plugins["authz-keycloak"] = {
                    permissions = {"model:" .. name .. ":predict"}
                }
            end
            
            -- Apply rate limiting based on annotations
            local rate_limit = annotations["kserve.io/rate-limit"]
            if rate_limit then
                local rate = tonumber(rate_limit) or 100
                route_config.plugins["limit-rate"] = {
                    rate = rate,
                    burst = rate * 2,
                    key = "consumer_name"
                }
            end
            
            core.log.info("updated route for model: ", name)
        end
    end
    
    local function sync_inference_services(premature, conf)
        if premature then
            return
        end
        
        local inference_services = fetch_inference_services(conf)
        if inference_services then
            update_apisix_routes(inference_services, conf)
        end
    end
    
    function _M.init_worker()
        local conf = {
            kubernetes_host = os.getenv("KUBERNETES_SERVICE_HOST") and 
                             "https://" .. os.getenv("KUBERNETES_SERVICE_HOST") or
                             "https://kubernetes.default.svc",
            namespace = os.getenv("KSERVE_NAMESPACE") or "kserve-test",
            sync_interval = tonumber(os.getenv("SYNC_INTERVAL")) or 30,
            service_port = tonumber(os.getenv("SERVICE_PORT")) or 80
        }
        
        -- Initial sync
        ngx_timer_at(0, sync_inference_services, conf)
        
        -- Periodic sync
        ngx_timer_every(conf.sync_interval, sync_inference_services, conf)
    end
    
    return _M

---
# =============================================================================
# DYNAMIC MODEL ROUTING CONFIGURATION
# =============================================================================

apiVersion: v1
kind: ConfigMap
metadata:
  name: apisix-model-routing-config
  namespace: api-gateway
data:
  model-routing.yaml: |
    # Model versioning and routing strategies
    model_routing:
      # Version-based routing
      version_routing:
        enabled: true
        header_name: "Model-Version"
        query_param: "version"
        default_version: "latest"
        fallback_strategy: "latest"  # latest, stable, default
        
      # Traffic splitting for A/B testing
      traffic_splitting:
        enabled: true
        strategies:
          canary:
            stable_weight: 90
            canary_weight: 10
            decision_header: "X-Canary-Traffic"
            
          blue_green:
            active_version: "blue"
            standby_version: "green"
            switch_header: "X-Traffic-Switch"
            
          shadow:
            shadow_percentage: 5
            shadow_header: "X-Shadow-Traffic"
            
      # Feature-based routing
      feature_routing:
        enabled: true
        routing_rules:
          - feature_type: "text"
            model_type: "nlp"
            route_pattern: "/models/nlp/*"
            
          - feature_type: "image" 
            model_type: "cv"
            route_pattern: "/models/cv/*"
            
          - feature_type: "tabular"
            model_type: "ml"
            route_pattern: "/models/ml/*"
            
      # Model selection based on request characteristics
      intelligent_routing:
        enabled: true
        selection_criteria:
          - metric: "response_time"
            threshold: 100  # ms
            action: "route_to_fastest"
            
          - metric: "accuracy_score"
            threshold: 0.95
            action: "route_to_most_accurate"
            
          - metric: "cost_per_prediction"
            threshold: 0.01  # USD
            action: "route_to_cheapest"

---
# =============================================================================
# KSERVE INFERENCE SERVICE TEMPLATES
# =============================================================================

apiVersion: v1
kind: ConfigMap
metadata:
  name: kserve-inference-templates
  namespace: kserve-test
data:
  sklearn-template.yaml: |
    apiVersion: serving.kserve.io/v1beta1
    kind: InferenceService
    metadata:
      name: sklearn-iris-{MODEL_VERSION}
      namespace: kserve-test
      annotations:
        serving.kserve.io/model-version: "{MODEL_VERSION}"
        kserve.io/auth-required: "true"
        kserve.io/rate-limit: "100"
        autoscaling.knative.dev/minScale: "1"
        autoscaling.knative.dev/maxScale: "10"
        autoscaling.knative.dev/target: "10"
    spec:
      predictor:
        serviceAccountName: kserve-sa
        sklearn:
          storageUri: "s3://mlops-models/sklearn/iris/{MODEL_VERSION}"
          resources:
            requests:
              cpu: "100m"
              memory: "256Mi"
            limits:
              cpu: "1000m"
              memory: "1Gi"
        tolerations:
        - key: "model-serving"
          operator: "Equal"
          value: "true"
          effect: "NoSchedule"
        nodeSelector:
          node-type: "model-serving"
      transformer:
        serviceAccountName: kserve-sa
        containers:
        - name: transformer
          image: "kserve/sklearn-transformer:latest"
          env:
          - name: MODEL_NAME
            value: "sklearn-iris"
          - name: MODEL_VERSION  
            value: "{MODEL_VERSION}"
          resources:
            requests:
              cpu: "50m"
              memory: "128Mi"
            limits:
              cpu: "500m"
              memory: "512Mi"
              
  pytorch-template.yaml: |
    apiVersion: serving.kserve.io/v1beta1
    kind: InferenceService
    metadata:
      name: pytorch-resnet-{MODEL_VERSION}
      namespace: kserve-test
      annotations:
        serving.kserve.io/model-version: "{MODEL_VERSION}"
        kserve.io/auth-required: "true"
        kserve.io/rate-limit: "50"
        autoscaling.knative.dev/minScale: "0"
        autoscaling.knative.dev/maxScale: "5"
        autoscaling.knative.dev/target: "5"
    spec:
      predictor:
        serviceAccountName: kserve-sa
        pytorch:
          storageUri: "s3://mlops-models/pytorch/resnet/{MODEL_VERSION}"
          resources:
            requests:
              cpu: "500m"
              memory: "1Gi"
              nvidia.com/gpu: "1"
            limits:
              cpu: "2000m"
              memory: "4Gi"
              nvidia.com/gpu: "1"
        tolerations:
        - key: "gpu-model-serving"
          operator: "Equal" 
          value: "true"
          effect: "NoSchedule"
        nodeSelector:
          accelerator: "nvidia-tesla-v100"
          
  tensorflow-template.yaml: |
    apiVersion: serving.kserve.io/v1beta1
    kind: InferenceService
    metadata:
      name: tensorflow-sentiment-{MODEL_VERSION}
      namespace: kserve-test
      annotations:
        serving.kserve.io/model-version: "{MODEL_VERSION}"
        kserve.io/auth-required: "true"
        kserve.io/rate-limit: "200"
        autoscaling.knative.dev/minScale: "2"
        autoscaling.knative.dev/maxScale: "20"
        autoscaling.knative.dev/target: "20"
    spec:
      predictor:
        serviceAccountName: kserve-sa
        tensorflow:
          storageUri: "s3://mlops-models/tensorflow/sentiment/{MODEL_VERSION}"
          resources:
            requests:
              cpu: "200m"
              memory: "512Mi"
            limits:
              cpu: "1000m"
              memory: "2Gi"
      explainer:
        serviceAccountName: kserve-sa
        containers:
        - name: explainer
          image: "kserve/alibi-explainer:latest"
          env:
          - name: MODEL_NAME
            value: "tensorflow-sentiment"
          - name: EXPLAINER_TYPE
            value: "AnchorText"
          resources:
            requests:
              cpu: "100m"
              memory: "256Mi"
            limits:
              cpu: "500m"
              memory: "1Gi"

---
# =============================================================================
# APISIX ROUTES FOR KSERVE MODELS
# =============================================================================

apiVersion: v1
kind: ConfigMap
metadata:
  name: apisix-kserve-routes
  namespace: api-gateway
data:
  kserve-routes.yaml: |
    # KServe Model Serving Routes Configuration
    routes:
      # Generic model prediction endpoint
      - id: "kserve-predict"
        uri: "/v1/models/:model_name/infer"
        methods: ["POST"]
        upstream:
          type: "roundrobin"
          discovery_type: "kserve"
          service_name: ":model_name"
          pass_host: "rewrite"
          upstream_host: ":model_name.kserve-test.svc.cluster.local"
        plugins:
          jwt-auth: {}
          authz-keycloak:
            permissions: ["model::model_name:predict"]
          request-validation:
            body_schema:
              type: "object"
              required: ["instances"]
              properties:
                instances:
                  type: "array"
                  minItems: 1
          prometheus:
            prefer_name: true
            default_labels:
              route_type: "model_prediction"
              model_name: ":model_name"
          limit-rate:
            rate: 100
            burst: 200
            key: "consumer_name"
            
      # Model metadata endpoint
      - id: "kserve-metadata"
        uri: "/v1/models/:model_name"
        methods: ["GET"]
        upstream:
          type: "roundrobin"
          discovery_type: "kserve"
          service_name: ":model_name"
        plugins:
          jwt-auth: {}
          authz-keycloak:
            permissions: ["model::model_name:read"]
          response-rewrite:
            headers:
              set:
                Cache-Control: "public, max-age=300"
          prometheus:
            prefer_name: true
            default_labels:
              route_type: "model_metadata"
              
      # Model health check endpoint  
      - id: "kserve-health"
        uri: "/v1/models/:model_name/ready"
        methods: ["GET"]
        upstream:
          type: "roundrobin"
          discovery_type: "kserve"
          service_name: ":model_name"
        plugins:
          prometheus:
            prefer_name: true
            default_labels:
              route_type: "health_check"
              
      # Model explainability endpoint
      - id: "kserve-explain"
        uri: "/v1/models/:model_name/explain"
        methods: ["POST"]
        upstream:
          type: "roundrobin"
          discovery_type: "kserve"
          service_name: ":model_name-explainer"
        plugins:
          jwt-auth: {}
          authz-keycloak:
            permissions: ["model::model_name:explain"]
          request-validation:
            body_schema:
              type: "object"
              required: ["instances"]
          limit-rate:
            rate: 10
            burst: 20
            key: "consumer_name"
            
      # Batch prediction endpoint
      - id: "kserve-batch-predict"
        uri: "/v1/models/:model_name/batch-infer"
        methods: ["POST"]
        upstream:
          type: "roundrobin"
          discovery_type: "kserve"
          service_name: ":model_name-batch"
        plugins:
          jwt-auth: {}
          authz-keycloak:
            permissions: ["model::model_name:batch-predict"]
          request-validation:
            body_schema:
              type: "object"
              required: ["instances"]
              properties:
                instances:
                  type: "array"
                  minItems: 1
                  maxItems: 1000
          limit-rate:
            rate: 5
            burst: 10
            key: "consumer_name"
            
      # Model versioning routes
      - id: "kserve-version-predict"
        uri: "/v1/models/:model_name/versions/:version/infer"
        methods: ["POST"]
        upstream:
          type: "roundrobin"
          discovery_type: "kserve"
          service_name: ":model_name-:version"
        plugins:
          jwt-auth: {}
          authz-keycloak:
            permissions: ["model::model_name:predict"]
          prometheus:
            prefer_name: true
            default_labels:
              route_type: "versioned_prediction"
              model_name: ":model_name"
              model_version: ":version"

---
# =============================================================================  
# CANARY DEPLOYMENT CONFIGURATION
# =============================================================================

apiVersion: v1
kind: ConfigMap
metadata:
  name: apisix-canary-config
  namespace: api-gateway
data:
  canary-config.yaml: |
    # Canary deployment configuration for KServe models
    canary_deployments:
      sklearn-iris:
        enabled: true
        stable_version: "v1.0"
        canary_version: "v2.0"
        traffic_split:
          stable: 90
          canary: 10
        promotion_criteria:
          success_rate_threshold: 0.95
          latency_p95_threshold: 100  # ms
          error_rate_threshold: 0.01
          min_requests: 1000
          evaluation_window: "10m"
        rollback_criteria:
          success_rate_threshold: 0.90
          latency_p95_threshold: 200  # ms
          error_rate_threshold: 0.05
          max_error_count: 10
        monitoring:
          metrics_endpoint: "/metrics"
          health_check_interval: "30s"
          alert_channels: ["slack", "email"]
          
      pytorch-resnet:
        enabled: true
        stable_version: "v1.5"
        canary_version: "v2.0"
        traffic_split:
          stable: 95
          canary: 5
        promotion_criteria:
          accuracy_threshold: 0.92
          inference_time_threshold: 50  # ms
          gpu_utilization_threshold: 0.8
          min_requests: 500
          evaluation_window: "15m"
        feature_flags:
          use_tensorrt: true
          batch_inference: false
          model_optimization: true
          
      tensorflow-sentiment:
        enabled: true
        stable_version: "v3.1"
        canary_version: "v3.2"
        traffic_split:
          stable: 80
          canary: 20
        promotion_criteria:
          f1_score_threshold: 0.88
          throughput_threshold: 1000  # requests/min
          memory_usage_threshold: 0.7
          min_requests: 2000
          evaluation_window: "5m"
        ab_testing:
          enabled: true
          control_group: "stable"
          treatment_group: "canary"
          significance_level: 0.05
          minimum_detectable_effect: 0.02

---
# =============================================================================
# AUTO-SCALING CONFIGURATION  
# =============================================================================

apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: kserve-sklearn-iris-hpa
  namespace: kserve-test
spec:
  scaleTargetRef:
    apiVersion: serving.knative.dev/v1
    kind: Service
    name: sklearn-iris-predictor-default
  minReplicas: 1
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: inference_requests_per_second
      target:
        type: AverageValue
        averageValue: "10"
  - type: Object
    object:
      metric:
        name: model_prediction_latency_p95
      target:
        type: Value
        value: "100m"
      describedObject:
        apiVersion: serving.kserve.io/v1beta1
        kind: InferenceService
        name: sklearn-iris
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
      - type: Pods
        value: 2
        periodSeconds: 60
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60

---
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: kserve-pytorch-resnet-scaler
  namespace: kserve-test
spec:
  scaleTargetRef:
    name: pytorch-resnet-predictor-default
  minReplicaCount: 0
  maxReplicaCount: 5
  triggers:
  - type: prometheus
    metadata:
      serverAddress: http://prometheus.monitoring.svc.cluster.local:9090
      metricName: inference_queue_depth
      threshold: '5'
      query: sum(rate(apisix_http_requests_total{route="kserve-predict",model_name="pytorch-resnet"}[1m]))
  - type: prometheus
    metadata:
      serverAddress: http://prometheus.monitoring.svc.cluster.local:9090
      metricName: gpu_utilization
      threshold: '60'
      query: avg(nvidia_gpu_utilization_percentage{pod=~"pytorch-resnet-.*"})

---
# =============================================================================
# MODEL SECURITY AND GOVERNANCE
# =============================================================================

apiVersion: v1
kind: ConfigMap
metadata:
  name: kserve-security-config
  namespace: kserve-test
data:
  security-policies.yaml: |
    # Model security and governance policies
    security_policies:
      # Authentication and authorization
      authentication:
        required: true
        methods: ["jwt", "api-key", "oauth2"]
        token_validation:
          issuer: "https://keycloak.example.com/auth/realms/mlops"
          audience: "kserve-models"
          algorithms: ["RS256", "HS256"]
          
      authorization:
        rbac_enabled: true
        roles:
          - name: "data-scientist"
            permissions: ["model:read", "model:predict"]
            models: ["sklearn-*", "tensorflow-*"]
            
          - name: "ml-engineer"
            permissions: ["model:read", "model:predict", "model:deploy"]
            models: ["*"]
            
          - name: "business-user"
            permissions: ["model:predict"]
            models: ["sklearn-iris", "tensorflow-sentiment"]
            rate_limits:
              requests_per_minute: 100
              requests_per_day: 10000
              
      # Data privacy and protection
      data_protection:
        pii_detection:
          enabled: true
          fields_to_check: ["text", "description", "content"]
          action: "mask"  # mask, reject, log
          
        data_retention:
          prediction_logs: "90d"
          model_artifacts: "2y"
          audit_logs: "7y"
          
        encryption:
          at_rest: true
          in_transit: true
          key_management: "external"  # k8s-secrets, external, vault
          
      # Model governance
      governance:
        model_approval:
          required: true
          approvers: ["ml-lead", "data-science-lead"]
          criteria:
            - "performance_threshold_met"
            - "bias_testing_passed"
            - "security_scan_passed"
            
        model_versioning:
          strategy: "semantic"  # semantic, timestamp, sequential
          retention_policy: "latest_5_versions"
          
        compliance_frameworks:
          - name: "GDPR"
            requirements: ["right_to_explanation", "data_minimization"]
          - name: "HIPAA" 
            requirements: ["phi_protection", "access_logging"]
          - name: "SOX"
            requirements: ["audit_trail", "change_management"]

---
# =============================================================================
# MONITORING AND OBSERVABILITY
# =============================================================================

apiVersion: v1
kind: ConfigMap
metadata:
  name: kserve-monitoring-config
  namespace: kserve-test
data:
  monitoring.yaml: |
    # Comprehensive monitoring configuration for KServe models
    monitoring:
      # Metrics collection
      metrics:
        # Model performance metrics
        - name: "model_prediction_latency"
          type: "histogram"
          labels: ["model_name", "model_version", "instance_id"]
          buckets: [0.01, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0]
          
        - name: "model_prediction_accuracy"
          type: "gauge"
          labels: ["model_name", "model_version"]
          
        - name: "model_requests_total"
          type: "counter"
          labels: ["model_name", "model_version", "status_code"]
          
        - name: "model_resource_utilization"
          type: "gauge"
          labels: ["model_name", "resource_type", "pod_name"]
          
        # Business metrics
        - name: "model_prediction_cost"
          type: "counter"
          labels: ["model_name", "cost_center"]
          
        - name: "model_business_impact"
          type: "gauge"
          labels: ["model_name", "metric_type"]
          
      # Alerting rules
      alerts:
        - alert: "ModelHighLatency"
          expr: "histogram_quantile(0.95, model_prediction_latency) > 1.0"
          for: "2m"
          severity: "warning"
          annotations:
            summary: "Model {{ $labels.model_name }} has high prediction latency"
            
        - alert: "ModelHighErrorRate"
          expr: "rate(model_requests_total{status_code!~\"2..\"}[5m]) > 0.1"
          for: "1m"
          severity: "critical"
          annotations:
            summary: "Model {{ $labels.model_name }} has high error rate"
            
        - alert: "ModelAccuracyDrop"
          expr: "model_prediction_accuracy < 0.8"
          for: "5m"
          severity: "warning"
          annotations:
            summary: "Model {{ $labels.model_name }} accuracy dropped below threshold"
            
        - alert: "ModelResourceExhaustion"
          expr: "model_resource_utilization{resource_type=\"memory\"} > 0.9"
          for: "2m"
          severity: "critical"
          annotations:
            summary: "Model {{ $labels.model_name }} is running out of memory"
            
      # Dashboards
      dashboards:
        - name: "KServe Model Performance"
          panels:
            - title: "Request Rate"
              type: "graph"
              targets:
                - expr: "rate(model_requests_total[5m])"
                  legend: "{{ model_name }}"
                  
            - title: "Prediction Latency"
              type: "graph"
              targets:
                - expr: "histogram_quantile(0.95, model_prediction_latency)"
                  legend: "P95 - {{ model_name }}"
                  
            - title: "Model Accuracy"
              type: "stat"
              targets:
                - expr: "model_prediction_accuracy"
                  legend: "{{ model_name }}"
                  
        - name: "KServe Resource Utilization"
          panels:
            - title: "CPU Usage"
              type: "graph"
              targets:
                - expr: "model_resource_utilization{resource_type=\"cpu\"}"
                  
            - title: "Memory Usage"
              type: "graph"
              targets:
                - expr: "model_resource_utilization{resource_type=\"memory\"}"
                  
            - title: "GPU Utilization"
              type: "graph"
              targets:
                - expr: "model_resource_utilization{resource_type=\"gpu\"}"
                  
      # Distributed tracing
      tracing:
        enabled: true
        sampler:
          type: "probabilistic"
          param: 0.1
        jaeger:
          endpoint: "http://jaeger-collector:14268/api/traces"
        spans:
          - name: "model_prediction"
            tags: ["model_name", "model_version", "request_id"]
          - name: "preprocessing"
            tags: ["transformer_type", "execution_time"]
          - name: "inference"
            tags: ["framework", "device", "batch_size"]
          - name: "postprocessing"
            tags: ["output_format", "response_size"]