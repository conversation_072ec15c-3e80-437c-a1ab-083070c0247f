# mlopscli/kast/manage_apisix_installation.py

import asyncio
import os
import tempfile
import yaml
from pathlib import Path
from typing import Dict, List, Optional


from mlopscli.kast.manage_installation_base import ManageInstallationBase
from mlopscli.utils.constants import (
    INSTALLATION_LOCATION_LOCAL_STR,
    INSTALLATION_LOCATION_REMOTE_STR,
    TDS_PROJECT_DOMAIN_NAME,
    DPSC_DOMAIN_NAME
)
from mlopscli.utils.installation_ingress_resolver import (
    configure_ingress_class,
    create_service_ingress_config
)
from mlopscli.utils.kubernetes import (
    is_helm_chart_installed,
    run_command,
    is_component_installed
)
from mlopscli.utils.logger import get_logger

logger = get_logger(__name__)

# APISIX Configuration Constants
APISIX_NAMESPACE = "api-gateway"
APISIX_HELM_CHART_NAME = "apisix"
APISIX_HELM_REPO = "https://charts.apiseven.com"
APISIX_CHART_VERSION = "2.9.0"
ETCD_HELM_CHART_NAME = "etcd"
ETCD_HELM_REPO = "https://charts.bitnami.com/bitnami"
ETCD_CHART_VERSION = "10.2.8"

# Default APISIX Values
APISIX_DEFAULT_VALUES = {
    "apisix": {
        "image": {
            "repository": "apache/apisix",
            "tag": "3.7.0-debian"
        },
        "replicaCount": 3,
        "podAntiAffinity": {
            "enabled": True
        },
        "resources": {
            "limits": {
                "cpu": "2000m",
                "memory": "2Gi"
            },
            "requests": {
                "cpu": "500m",
                "memory": "512Mi"
            }
        }
    },
    "gateway": {
        "type": "LoadBalancer",
        "http": {
            "enabled": True,
            "servicePort": 80,
            "containerPort": 9080
        },
        "https": {
            "enabled": True,
            "servicePort": 443,
            "containerPort": 9443
        },
        "stream": {
            "enabled": False
        }
    },
    "admin": {
        "enabled": True,
        "type": "ClusterIP",
        "port": 9180,  
        "servicePort": 9180
    },
    "nginx": {
        "workerProcesses": "auto",
        "workerConnections": 10620,
        "workerRlimitNofile": 20480,
        "errorLogLevel": "warn",
        "accessLogFormat": "$remote_addr - $remote_user [$time_local] \"$request\" $status $body_bytes_sent \"$http_referer\" \"$http_user_agent\" $request_time",
        "enableCpuAffinity": True
    },
    "etcd": {
        "enabled": False,  # We'll deploy etcd separately
        "host": ["http://etcd:2379"],
        "prefix": "/apisix",
        "timeout": 30
    },
    "serviceAccount": {
        "create": True,
        "name": "apisix"
    },
    "rbac": {
        "create": True
    }
}

ETCD_DEFAULT_VALUES = {
    "replicaCount": 3,
    "auth": {
        "rbac": {
            "create": False
        }
    },
    "persistence": {
        "enabled": True,
        "size": "10Gi",
        "storageClass": "fast-ssd"
    },
    "resources": {
        "limits": {
            "cpu": "500m",
            "memory": "512Mi"
        },
        "requests": {
            "cpu": "250m", 
            "memory": "256Mi"
        }
    },
    "podAntiAffinity": {
        "type": "hard"
    },
    "metrics": {
        "enabled": True,
        "serviceMonitor": {
            "enabled": True
        }
    }
}


class ManageAPISIXInstallation(ManageInstallationBase):
    """Manages Apache APISIX API Gateway installation and configuration."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._logger = logger

    def install_apisix(
        self,
        installation_location: str = INSTALLATION_LOCATION_LOCAL_STR,
        ingress_class_name: str = "",
        domain_name: str = "",
        clickhouse_host: str = "clickhouse.monitoring.svc.cluster.local",
        clickhouse_port: int = 8123,
        clickhouse_database: str = "apisix_logs",
        otel_endpoint: str = "jaeger-collector.monitoring.svc.cluster.local:14268"
    ) -> None:
        """
        Install Apache APISIX API Gateway with etcd backend.
        
        Args:
            installation_location: Installation location (local/remote)
            ingress_class_name: Ingress class to use
            domain_name: Domain name for the installation
            clickhouse_host: ClickHouse host for logging
            clickhouse_port: ClickHouse port
            clickhouse_database: ClickHouse database name
            otel_endpoint: OpenTelemetry collector endpoint
        """
        self._logger.info("Starting Apache APISIX installation...")
        
        # Auto-detect ingress class if not provided
        if not ingress_class_name:
            try:
                ingress_class_name = configure_ingress_class()
                self._logger.info(f"Auto-detected ingress class: {ingress_class_name}")
            except Exception as e:
                self._logger.error(f"Failed to auto-detect ingress class: {e}")
                ingress_class_name = "nginx"
        
        # Create namespace
        self._create_namespace()
        
        # Add Helm repositories
        self._add_helm_repositories()
        
        # Install etcd first
        self._install_etcd()
        
        # Install APISIX
        self._install_apisix_gateway(
            installation_location=installation_location,
            ingress_class_name=ingress_class_name,
            domain_name=domain_name,
            clickhouse_host=clickhouse_host,
            clickhouse_port=clickhouse_port,
            clickhouse_database=clickhouse_database,
            otel_endpoint=otel_endpoint
        )
        
        # Configure APISIX with dynamic routes for existing services
        self._configure_dynamic_routes(
            installation_location=installation_location,
            ingress_class_name=ingress_class_name,
            domain_name=domain_name
        )
        
        self._logger.info("Apache APISIX installation completed successfully!")

    def _create_namespace(self) -> None:
        """Create the API Gateway namespace."""
        self._logger.info(f"Creating namespace: {APISIX_NAMESPACE}")
        
        if not is_component_installed(type="namespace", name=APISIX_NAMESPACE):
            command = f"kubectl create namespace {APISIX_NAMESPACE}"
            run_command(command=command, check=True)
            
            # Label namespace for monitoring
            label_command = f"kubectl label namespace {APISIX_NAMESPACE} name={APISIX_NAMESPACE} --overwrite"
            run_command(command=label_command, check=True)
        else:
            self._logger.info(f"Namespace {APISIX_NAMESPACE} already exists")

    def _add_helm_repositories(self) -> None:
        """Add required Helm repositories."""
        self._logger.info("Adding Helm repositories...")
        
        # Add APISIX Helm repository
        command = f"helm repo add apisix {APISIX_HELM_REPO}"
        run_command(command=command, check=True)
        
        # Add Bitnami repository for etcd
        command = f"helm repo add bitnami {ETCD_HELM_REPO}"
        run_command(command=command, check=True)
        
        # Update repositories
        command = "helm repo update"
        run_command(command=command, check=True)

    def _install_etcd(self) -> None:
        """Install etcd cluster for APISIX configuration storage."""
        self._logger.info("Installing etcd cluster...")
        
        if is_helm_chart_installed(ETCD_HELM_CHART_NAME):
            self._logger.info(f"Helm chart ({ETCD_HELM_CHART_NAME}) is already deployed")
            return
            
        # Create etcd values file
        etcd_values = ETCD_DEFAULT_VALUES.copy()
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as values_file:
            yaml.dump(etcd_values, values_file, default_flow_style=False)
            values_file_path = values_file.name
        
        try:
            command = (
                f"helm install {ETCD_HELM_CHART_NAME} bitnami/etcd "
                f"--namespace {APISIX_NAMESPACE} "
                f"--version {ETCD_CHART_VERSION} "
                f"--values {values_file_path} "
                f"--wait --timeout=10m"
            )
            run_command(command=command, check=True)
            self._logger.info("etcd installation completed")
        finally:
            os.unlink(values_file_path)

    def _install_apisix_gateway(
        self,
        installation_location: str,
        ingress_class_name: str,
        domain_name: str,
        clickhouse_host: str,
        clickhouse_port: int,
        clickhouse_database: str,
        otel_endpoint: str
    ) -> None:
        """Install APISIX Gateway."""
        self._logger.info("Installing APISIX Gateway...")
        
        if is_helm_chart_installed(APISIX_HELM_CHART_NAME):
            self._logger.info(f"Helm chart ({APISIX_HELM_CHART_NAME}) is already deployed")
            return
        
        # Determine effective domain
        effective_domain = domain_name or (
            TDS_PROJECT_DOMAIN_NAME if installation_location == INSTALLATION_LOCATION_REMOTE_STR 
            else DPSC_DOMAIN_NAME
        )
        
        # Create APISIX configuration values
        apisix_values = self._create_apisix_values(
            installation_location=installation_location,
            ingress_class_name=ingress_class_name,
            domain_name=effective_domain,
            clickhouse_host=clickhouse_host,
            clickhouse_port=clickhouse_port,
            clickhouse_database=clickhouse_database,
            otel_endpoint=otel_endpoint
        )
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as values_file:
            yaml.dump(apisix_values, values_file, default_flow_style=False)
            values_file_path = values_file.name
        
        try:
            command = (
                f"helm install {APISIX_HELM_CHART_NAME} apisix/apisix "
                f"--namespace {APISIX_NAMESPACE} "
                f"--version {APISIX_CHART_VERSION} "
                f"--values {values_file_path} "
                f"--wait --timeout=15m"
            )
            run_command(command=command, check=True)
            self._logger.info("APISIX Gateway installation completed")
        except Exception as e:
            self._logger.error(f"APISIX installation failed: {e}")
            raise
        finally:
            os.unlink(values_file_path)

    def _create_apisix_values(
        self,
        installation_location: str,
        ingress_class_name: str,
        domain_name: str,
        clickhouse_host: str,
        clickhouse_port: int,
        clickhouse_database: str,
        otel_endpoint: str
    ) -> Dict:
        """Create APISIX Helm values configuration."""
        values = APISIX_DEFAULT_VALUES.copy()
        
        # Configure etcd connection
        values["etcd"] = {
            "enabled": False,
            "host": [f"http://etcd.{APISIX_NAMESPACE}.svc.cluster.local:2379"],
            "prefix": "/apisix",
            "timeout": 30
        }
        
        # Configure plugins
        values["apisix"]["plugins"] = [
            # Core plugins
            "real-ip",
            "client-control", 
            "proxy-control",
            "request-id",
            "prometheus",
            "node-status",
            
            # Authentication plugins
            "jwt-auth",
            "key-auth",
            "basic-auth",
            "authz-keycloak",
            "openid-connect",
            
            # Security plugins
            "cors",
            "ip-restriction",
            "ua-restriction",
            "request-validation",
            "csrf",
            
            # Traffic management
            "limit-rate",
            "fault-injection",
            "proxy-rewrite",
            "response-rewrite",
            "proxy-cache",
            
            # Observability plugins (OTEL and ClickHouse only)
            "opentelemetry",
            "clickhouse-logger",
            
            # Utility plugins
            "workflow",
            "batch-requests",
            "grpc-transcode"
        ]
        
        # Configure plugin attributes
        values["apisix"]["pluginAttrs"] = {
            "prometheus": {
                "export_addr": {
                    "ip": "0.0.0.0",
                    "port": 9091
                },
                "export_uri": "/apisix/prometheus/metrics",
                "metric_prefix": "apisix_",
                "enable_export_server": True,
                "default_labels": {
                    "service_name": "apisix-gateway",
                    "environment": "production",
                    "cluster": "mlops-cluster"
                }
            },
            "opentelemetry": {
                "trace_id_source": "x-request-id",
                "resource": {
                    "service.name": "apisix-gateway",
                    "service.version": "3.7.0",
                    "deployment.environment": "production"
                },
                "collector": {
                    "address": otel_endpoint,
                    "request_timeout": 3
                },
                "batch_span_processor": {
                    "drop_on_queue_full": False,
                    "max_export_batch_size": 256,
                    "max_queue_size": 2048,
                    "schedule_delay_millis": 5000
                }
            },
            "clickhouse-logger": {
                "uri": f"http://{clickhouse_host}:{clickhouse_port}",
                "database": clickhouse_database,
                "table": "access_logs",
                "logtable": "error_logs",
                "timeout": 3000,
                "retry_delay": 1,
                "batch_max_size": 1000,
                "inactive_timeout": 5,
                "buffer_duration": 60
            }
        }
        
        # Configure ingress
        if ingress_class_name:
            values["ingress-controller"] = {
                "enabled": True,
                "config": {
                    "apisix": {
                        "serviceNamespace": APISIX_NAMESPACE,
                        "serviceName": "apisix-admin",
                        "servicePort": 9180
                    },
                    "ingressClass": ingress_class_name
                }
            }
        
        # Configure service monitor for Prometheus
        values["serviceMonitor"] = {
            "enabled": True,
            "namespace": APISIX_NAMESPACE,
            "interval": "15s",
            "labels": {
                "app": "apisix-gateway"
            }
        }
        
        # Configure autoscaling
        values["autoscaling"] = {
            "enabled": True,
            "minReplicas": 3,
            "maxReplicas": 10,
            "targetCPUUtilizationPercentage": 70,
            "targetMemoryUtilizationPercentage": 80
        }
        
        # Configure pod disruption budget
        values["podDisruptionBudget"] = {
            "enabled": True,
            "minAvailable": 1
        }
        
        return values

    def _configure_dynamic_routes(
        self,
        installation_location: str,
        ingress_class_name: str,
        domain_name: str
    ) -> None:
        """Configure dynamic routes for existing MLOps services."""
        self._logger.info("Configuring dynamic routes for MLOps services...")
        
        # Wait for APISIX to be ready
        self._wait_for_apisix_ready()
        
        # Configure routes for existing services
        routes_config = self._create_routes_configuration(
            installation_location=installation_location,
            domain_name=domain_name
        )
        
        # Apply routes using APISIX Admin API
        self._apply_routes_via_admin_api(routes_config)

    def _wait_for_apisix_ready(self, timeout: int = 300) -> None:
        """Wait for APISIX to be ready."""
        self._logger.info("Waiting for APISIX to be ready...")
        
        command = (
            f"kubectl wait --namespace={APISIX_NAMESPACE} "
            f"--for=condition=ready pod "
            f"--selector=app.kubernetes.io/name=apisix "
            f"--timeout={timeout}s"
        )
        run_command(command=command, check=True)
        self._logger.info("APISIX is ready")

    def _create_routes_configuration(
        self,
        installation_location: str,
        domain_name: str
    ) -> Dict:
        """Create routes configuration for MLOps services."""
        effective_domain = domain_name or (
            TDS_PROJECT_DOMAIN_NAME if installation_location == INSTALLATION_LOCATION_REMOTE_STR 
            else DPSC_DOMAIN_NAME
        )
        
        routes = {
            # Polycore GRPC Services
            "polycore-grpc": {
                "uri": "/polyflow.v1.*",
                "methods": ["POST"],
                "host": f"polycore.{effective_domain}",
                "upstream": {
                    "type": "roundrobin",
                    "scheme": "grpc",
                    "discovery_type": "kubernetes",
                    "service_name": "polycore.mlops-toolchain.svc.cluster.local",
                    "service_port": 8080
                },
                "plugins": {
                    "grpc-transcode": {
                        "proto_id": "polycore-proto",
                        "service": "polyflow.v1.PolyCoreService"
                    },
                    "jwt-auth": {},
                    "limit-rate": {
                        "rate": 1000,
                        "burst": 2000,
                        "key": "consumer_name"
                    },
                    "opentelemetry": {},
                    "clickhouse-logger": {}
                }
            },
            
            # Polycore REST API
            "polycore-rest": {
                "uri": "/api/v1/polycore/*",
                "methods": ["GET", "POST", "PUT", "DELETE", "PATCH"],
                "host": f"polycore-obs.{effective_domain}",
                "upstream": {
                    "type": "roundrobin",
                    "scheme": "http",
                    "discovery_type": "kubernetes", 
                    "service_name": "polycore-rest.mlops-toolchain.svc.cluster.local",
                    "service_port": 8081
                },
                "plugins": {
                    "jwt-auth": {},
                    "limit-rate": {
                        "rate": 500,
                        "burst": 1000,
                        "key": "consumer_name"
                    },
                    "proxy-rewrite": {
                        "regex_uri": ["^/api/v1/polycore/(.*)", "/$1"]
                    },
                    "opentelemetry": {},
                    "clickhouse-logger": {}
                }
            },
            
            # MLflow API
            "mlflow-api": {
                "uri": "/api/2.0/mlflow/*",
                "methods": ["GET", "POST", "PUT", "DELETE", "PATCH"],
                "host": f"mlflow.{effective_domain}",
                "upstream": {
                    "type": "roundrobin",
                    "scheme": "http",
                    "discovery_type": "kubernetes",
                    "service_name": "mlflow.processing.svc.cluster.local",
                    "service_port": 5000
                },
                "plugins": {
                    "jwt-auth": {},
                    "limit-rate": {
                        "rate": 200,
                        "burst": 400,
                        "key": "consumer_name"
                    },
                    "opentelemetry": {},
                    "clickhouse-logger": {}
                }
            },
            
            # KServe Model Serving - Dynamic route for all models
            "kserve-models": {
                "uri": "/v1/models/*",
                "methods": ["GET", "POST", "PUT", "DELETE"],
                "upstream": {
                    "type": "roundrobin",
                    "discovery_type": "kubernetes",
                    "service_name": "*.kserve-test.svc.cluster.local",
                    "service_port": 80
                },
                "plugins": {
                    "jwt-auth": {},
                    "limit-rate": {
                        "rate": 100,
                        "burst": 200,
                        "key": "consumer_name"
                    },
                    "request-validation": {
                        "body_schema": {
                            "type": "object",
                            "required": ["instances"],
                            "properties": {
                                "instances": {
                                    "type": "array",
                                    "minItems": 1
                                }
                            }
                        }
                    },
                    "opentelemetry": {},
                    "clickhouse-logger": {}
                }
            }
        }
        
        return routes

    def _apply_routes_via_admin_api(self, routes_config: Dict) -> None:
        """Apply routes configuration via APISIX Admin API."""
        self._logger.info("Applying routes via APISIX Admin API...")
        
        # Get admin API endpoint
        admin_endpoint = self._get_admin_api_endpoint()
        
        for route_id, route_config in routes_config.items():
            self._logger.info(f"Creating route: {route_id}")
            
            # Create route via Admin API
            route_data = {
                "id": route_id,
                "uri": route_config["uri"],
                "methods": route_config["methods"],
                "upstream": route_config["upstream"],
                "plugins": route_config.get("plugins", {})
            }
            
            if "host" in route_config:
                route_data["host"] = route_config["host"]
            
            # Apply route using kubectl exec to avoid networking issues
            self._apply_route_via_kubectl(route_id, route_data)

    def _get_admin_api_endpoint(self) -> str:
        """Get APISIX Admin API endpoint."""
        return f"http://apisix-admin.{APISIX_NAMESPACE}.svc.cluster.local:9180"

    def _apply_route_via_kubectl(self, route_id: str, route_data: Dict) -> None:
        """Apply route configuration using kubectl exec."""
        import json
        
        admin_endpoint = "http://localhost:9180"
        route_json = json.dumps(route_data)
        
        # Use kubectl exec to create route
        command = (
            f"kubectl exec -n {APISIX_NAMESPACE} deployment/apisix -- "
            f"curl -X PUT '{admin_endpoint}/apisix/admin/routes/{route_id}' "
            f"-H 'X-API-KEY: edd1c9f034335f136f87ad84b625c8f1' "
            f"-H 'Content-Type: application/json' "
            f"-d '{route_json}'"
        )
        
        try:
            run_command(command=command, check=True)
            self._logger.info(f"Route {route_id} created successfully")
        except Exception as e:
            self._logger.error(f"Failed to create route {route_id}: {e}")

    def uninstall_apisix(self) -> None:
        """Uninstall Apache APISIX and etcd."""
        self._logger.info("Starting Apache APISIX uninstallation...")
        
        # Uninstall APISIX
        if is_helm_chart_installed(APISIX_HELM_CHART_NAME):
            command = f"helm uninstall {APISIX_HELM_CHART_NAME} --namespace {APISIX_NAMESPACE}"
            run_command(command=command, check=True)
            self._logger.info("APISIX uninstalled")
        
        # Uninstall etcd
        if is_helm_chart_installed(ETCD_HELM_CHART_NAME):
            command = f"helm uninstall {ETCD_HELM_CHART_NAME} --namespace {APISIX_NAMESPACE}"
            run_command(command=command, check=True)
            self._logger.info("etcd uninstalled")
        
        # Delete namespace
        if is_component_installed(type="namespace", name=APISIX_NAMESPACE):
            command = f"kubectl delete namespace {APISIX_NAMESPACE}"
            run_command(command=command, check=True)
            self._logger.info(f"Namespace {APISIX_NAMESPACE} deleted")
        
        self._logger.info("Apache APISIX uninstallation completed")

    def get_apisix_status(self) -> Dict:
        """Get APISIX installation status."""
        status = {
            "namespace_exists": is_component_installed(type="namespace", name=APISIX_NAMESPACE),
            "etcd_installed": is_helm_chart_installed(ETCD_HELM_CHART_NAME),
            "apisix_installed": is_helm_chart_installed(APISIX_HELM_CHART_NAME),
            "admin_api_endpoint": self._get_admin_api_endpoint()
        }
        
        return status