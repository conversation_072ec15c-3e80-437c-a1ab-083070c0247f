import os

ARCH_ARM64 = "arm64"
ARCH_AMD64 = "x86_64"
ARTIFACTORY_REGISTRY_URL = "artifactory.thalesdigital.io"
ARTIFACTORY_SECRET_NAME = "reg-cred"  # noqa: S105
SUPPORTED_ARCH = [ARCH_AMD64, ARCH_ARM64]
LOG_DIR = os.path.expanduser("~/Library/Logs")
WORKING_DIR = os.path.expanduser("~/.cache/mlopscli")
KAST_WORKING_DIR = os.path.join(WORKING_DIR, "kast_installation")
K3S_WORKING_DIR = os.path.join(WORKING_DIR, "k3s_installation")
TMP_WORKING_DIR = os.path.join(WORKING_DIR, "tmp")
DEFAULT_LOG_FILE = os.path.join(LOG_DIR, "mlops.log")
PACKAGE_FILE = os.path.join(TMP_WORKING_DIR, "mlops-logs.tgz")
CA_CERT_DIR = os.path.join(K3S_WORKING_DIR, "certs")

INSTALLATION_LOCATION_LOCAL_STR = "local"
INSTALLATION_LOCATION_REMOTE_STR = "remote"
INSTALLATION_LOCATIONS = [INSTALLATION_LOCATION_LOCAL_STR, INSTALLATION_LOCATION_REMOTE_STR]

DEFAULT_MLOPS_DIR = os.path.expanduser("~/mlops")

UNKNOWN = "unknown"
FAILED = "failed"
DEFAULT = "default"

SENSITIVE_VALUES = set()
SENSITIVE_KEYS = [
    "access_key_id",
    "accesskey",
    "authEncryptSecretKey",
    "databaseConnectionString",
    "existingSecret",
    "kast_artifactory_token",
    "kast_artifactory_user",
    "password",
    "postgres_db_password",
    "secret_access_key",
    "secretkey",
    "s3_root_password",
    "s3_secret_key",
]
OBFUSCATED_REPLACEMENT_VALUE = "*****OBFUSCATED*****"

TDS_VLAN_DOMAIN_NAME = "prod.k8s.collaborative.vlan"
TDS_PROJECT_DOMAIN_NAMESPACE = "mlops"
TDS_PROJECT_DOMAIN_NAME = f"{TDS_PROJECT_DOMAIN_NAMESPACE}.{TDS_VLAN_DOMAIN_NAME}"

INGRESS_CLASS_ANNOTATION = "kubernetes.io/ingress.class"
INGRESS_CLASS_DEFAULT = "nginx"
INGRESS_CLASS_CILIUM = "cilium"
INGRESS_CLASS_NGINX = "nginx"

CLUSTER_ISSUER = "cert-manager.io/cluster-issuer"
NGINX_PROXY_BODY_SIZE = "nginx.ingress.kubernetes.io/proxy-body-size"

# Ingress controller specific annotations mapping
INGRESS_ANNOTATIONS = {
    INGRESS_CLASS_CILIUM: {
        "tls_passthrough": "ingress.cilium.io/tls-passthrough",
        "backend_protocol": "ingress.cilium.io/backend-protocol",
        "loadbalancer_mode": "ingress.cilium.io/loadbalancer-mode",
        "proxy_body_size": None,  # Not supported in Cilium
        "proxy_read_timeout": None,  # Not supported in Cilium
        "proxy_send_timeout": None,  # Not supported in Cilium
        "server_snippet": None,  # Not supported in Cilium
    },
    INGRESS_CLASS_NGINX: {
        "tls_passthrough": "nginx.ingress.kubernetes.io/ssl-passthrough",
        "backend_protocol": "nginx.ingress.kubernetes.io/backend-protocol",
        "loadbalancer_mode": None,  # Not needed in NGINX
        "proxy_body_size": "nginx.ingress.kubernetes.io/proxy-body-size",
        "proxy_read_timeout": "nginx.ingress.kubernetes.io/proxy-read-timeout",
        "proxy_send_timeout": "nginx.ingress.kubernetes.io/proxy-send-timeout",
        "server_snippet": "nginx.ingress.kubernetes.io/server-snippet",
    },
}
