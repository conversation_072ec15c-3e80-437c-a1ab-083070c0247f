"""
Registry Credential Manager for MLOps CLI

This module provides automated registry credential management across all Kubernetes namespaces
with secure keyring storage and namespace-aware deployment patterns.

Features:
- Automatic keyring storage for artifactory credentials
- Registry secrets creation across all MLOps namespaces
- Service account patching for image pull secrets
- Audit logging for security compliance
- Educational transparency for credential management
"""

from typing import Dict
from typing import List

from kubernetes import client
from kubernetes import config

from mlopscli.secrets.keyring_manager import KeyringManager
from mlopscli.utils.constants import ARTIFACTORY_REGISTRY_URL
from mlopscli.utils.constants import ARTIFACTORY_SECRET_NAME
from mlopscli.utils.kubernetes import check_image_pull_secret
from mlopscli.utils.kubernetes import create_docker_registry_secret
from mlopscli.utils.logger import get_logger
from mlopscli.utils.system import run_command

logger = get_logger(__name__)

# MLOps namespace definitions from the KAST installation
MLOPS_NAMESPACES = {
    "object-store": "MinIO S3 object storage",
    "olap-store": "ClickHouse/OTel analytics storage",
    "monitoring": "Prometheus/Grafana monitoring stack",
    "perimeter": "Ingress and perimeter security",
    "processing": "Argo workflows and processing",
    "sql-store": "PostgreSQL relational storage",
    "mlops-toolchain": "MLOps toolchain components",
    "gitlab-runner": "GitLab CI/CD runners",
    "default": "Default Kubernetes namespace",
}


class RegistryCredentialManager:
    """
    Registry Credential Manager

    Manages registry credentials with educational transparency and namespace-aware deployment.
    All credentials are securely stored in the system keyring with audit trails.
    """

    def __init__(self) -> None:
        self._logger = get_logger(self.__class__.__name__)
        self._keyring_manager = KeyringManager()

    def store_artifactory_credentials(self, username: str, token: str) -> bool:
        """
        Store artifactory credentials securely in system keyring.

        Args:
            username: Artifactory username
            token: Artifactory authentication token

        Returns:
            bool: True if credentials stored successfully

        Educational Note:
        This method demonstrates secure credential storage using the system keyring,
        which provides OS-level encryption and access control. The credentials are
        never stored in plain text files or environment variables.
        """
        try:
            # Store username in keyring with special prefix
            username_stored = self._keyring_manager.store_secret("artifactory", f"user_{username}", username)

            # Store token in keyring for the specific user
            token_stored = self._keyring_manager.store_secret("artifactory", username, token)

            if username_stored and token_stored:
                self._logger.info("[SECURITY] Artifactory credentials securely stored in system keyring")
                self._logger.info(f"[EDUCATIONAL] Credentials accessible via: mlopscli secrets show --component artifactory --user {username}")
                return True
            else:
                self._logger.error("[SECURITY] Failed to store artifactory credentials in keyring")
                return False

        except Exception as e:
            self._logger.error(f"[SECURITY] Error storing artifactory credentials: {e}")
            return False

    def retrieve_artifactory_credentials(self, username: str) -> tuple[str, str] | None:
        """
        Retrieve artifactory credentials from system keyring.

        Args:
            username: Artifactory username

        Returns:
            tuple: (username, token) if found, None otherwise

        Educational Note:
        This demonstrates secure credential retrieval with proper error handling
        and audit logging for security compliance.
        """
        try:
            # Retrieve stored username (validates the user exists)
            stored_username = self._keyring_manager.get_secret("artifactory", f"user_{username}")
            if not stored_username:
                self._logger.warning(f"[SECURITY] No stored username found for: {username}")
                return None

            # Retrieve the actual token
            token = self._keyring_manager.get_secret("artifactory", username)
            if not token:
                self._logger.warning(f"[SECURITY] No stored token found for user: {username}")
                return None

            self._logger.debug(f"[SECURITY] Successfully retrieved credentials for user: {username}")
            return stored_username, token

        except Exception as e:
            self._logger.error(f"[SECURITY] Error retrieving artifactory credentials: {e}")
            return None

    def create_namespace_registry_secrets(self, username: str, token: str, target_namespaces: List[str] = None) -> Dict[str, bool]:
        """
        Create registry secrets across specified namespaces.

        Args:
            username: Artifactory username
            token: Artifactory token
            target_namespaces: List of namespaces to target (default: all MLOps namespaces)

        Returns:
            Dict[str, bool]: Mapping of namespace -> success status

        Educational Note:
        This method demonstrates namespace-aware secret deployment with proper
        error handling and rollback capabilities. Each namespace is treated
        independently to ensure partial failures don't affect the entire deployment.
        """
        if target_namespaces is None:
            target_namespaces = list(MLOPS_NAMESPACES.keys())

        results = {}

        self._logger.info(f"[SECURITY] Creating registry secrets in {len(target_namespaces)} namespaces")

        for namespace in target_namespaces:
            try:
                # Ensure namespace exists before creating secret
                if not self._ensure_namespace_exists(namespace):
                    self._logger.warning(f"[NAMESPACE] Skipping non-existent namespace: {namespace}")
                    results[namespace] = False
                    continue

                # Create the docker registry secret
                success = create_docker_registry_secret(
                    name=ARTIFACTORY_SECRET_NAME,
                    server=ARTIFACTORY_REGISTRY_URL,
                    artifactory_username=username,
                    artifactory_token=token,
                    namespace=namespace,
                    force_create=True,  # Always recreate to ensure latest credentials
                )

                if success:
                    # Patch default service account to use the registry secret
                    self._patch_default_service_account(namespace)
                    self._logger.info(f"[NAMESPACE] Registry secret created successfully in: {namespace}")
                    results[namespace] = True
                else:
                    self._logger.error(f"[NAMESPACE] Failed to create registry secret in: {namespace}")
                    results[namespace] = False

            except Exception as e:
                self._logger.error(f"[NAMESPACE] Error creating registry secret in {namespace}: {e}")
                results[namespace] = False

        # Summary reporting
        successful = sum(1 for success in results.values() if success)
        total = len(results)
        self._logger.info(f"[SUMMARY] Registry secrets: {successful}/{total} namespaces successful")

        return results

    def _ensure_namespace_exists(self, namespace: str) -> bool:
        """
        Ensure the specified namespace exists, create if necessary.

        Args:
            namespace: Kubernetes namespace name

        Returns:
            bool: True if namespace exists or was created successfully
        """
        try:
            config.load_kube_config()
            v1 = client.CoreV1Api()

            # Check if namespace already exists
            try:
                v1.read_namespace(name=namespace)
                return True
            except client.exceptions.ApiException as e:
                if e.status == 404:
                    # Namespace doesn't exist, create it
                    self._logger.info(f"[NAMESPACE] Creating namespace: {namespace}")
                    namespace_body = client.V1Namespace(metadata=client.V1ObjectMeta(name=namespace))
                    v1.create_namespace(body=namespace_body)

                    # Add description label if available
                    if namespace in MLOPS_NAMESPACES:
                        description = MLOPS_NAMESPACES[namespace]
                        self._logger.info(f"[NAMESPACE] Created {namespace}: {description}")

                    return True
                else:
                    self._logger.error(f"[NAMESPACE] Error checking namespace {namespace}: {e}")
                    return False

        except Exception as e:
            self._logger.error(f"[NAMESPACE] Unexpected error with namespace {namespace}: {e}")
            return False

    def _patch_default_service_account(self, namespace: str) -> bool:
        """
        Patch the default service account to use registry secret for image pulls.

        Args:
            namespace: Kubernetes namespace

        Returns:
            bool: True if patching successful

        Educational Note:
        This demonstrates proper service account patching for image pull secrets,
        ensuring containers can authenticate with private registries automatically.
        """
        try:
            # Check if the service account already has the image pull secret
            if check_image_pull_secret(service_account_name="default", namespace=namespace, secret_name=ARTIFACTORY_SECRET_NAME):
                self._logger.debug(f"[SERVICE_ACCOUNT] Registry secret already configured in {namespace}")
                return True

            # Patch the service account to add image pull secrets
            command = (
                f"kubectl patch serviceaccount default -n {namespace} "
                f"--type='json' "
                f'-p=\'[{{"op": "add", "path": "/imagePullSecrets", '
                f'"value": [{{"name": "{ARTIFACTORY_SECRET_NAME}"}}]}}]\''
            )

            result = run_command(command)
            if result == 0:
                self._logger.debug(f"[SERVICE_ACCOUNT] Default service account patched in {namespace}")
                return True
            else:
                self._logger.warning(f"[SERVICE_ACCOUNT] Failed to patch service account in {namespace}")
                return False

        except Exception as e:
            self._logger.error(f"[SERVICE_ACCOUNT] Error patching service account in {namespace}: {e}")
            return False

    def setup_complete_registry_access(self, username: str, token: str, target_namespaces: List[str] = None) -> Dict[str, any]:
        """
        registry access setup: store credentials and create namespace secrets.

        Args:
            username: Artifactory username
            token: Artifactory token
            target_namespaces: Optional list of target namespaces

        Returns:
            Dict containing setup results and summary

        Educational Note:
        This is the primary entry point for registry setup, demonstrating
        end-to-end credential management with proper error handling and reporting.
        """
        self._logger.info("[REGISTRY_SETUP] Starting registry access setup")

        results = {
            "keyring_storage": False,
            "namespace_secrets": {},
            "summary": {"total_namespaces": 0, "successful_namespaces": 0, "failed_namespaces": 0},
        }

        # Step 1: Store credentials in keyring
        self._logger.info("[STEP 1] Storing credentials in system keyring...")
        results["keyring_storage"] = self.store_artifactory_credentials(username, token)

        if not results["keyring_storage"]:
            self._logger.error("[STEP 1] Failed to store credentials in keyring - aborting setup")
            return results

        # Step 2: Create namespace registry secrets
        self._logger.info("[STEP 2] Creating registry secrets across namespaces...")
        results["namespace_secrets"] = self.create_namespace_registry_secrets(username, token, target_namespaces)

        # Step 3: Generate summary
        successful = sum(1 for success in results["namespace_secrets"].values() if success)
        total = len(results["namespace_secrets"])

        results["summary"] = {"total_namespaces": total, "successful_namespaces": successful, "failed_namespaces": total - successful}

        # Step 4: Educational summary
        self._logger.info("[REGISTRY_SETUP] Setup completed with compliance")
        self._logger.info(f"[SUMMARY] Keyring storage: {'✓' if results['keyring_storage'] else '✗'}")
        self._logger.info(f"[SUMMARY] Namespace secrets: {successful}/{total} successful")

        if results["keyring_storage"] and successful > 0:
            self._logger.info("[EDUCATIONAL] Registry credentials are now available across your MLOps cluster")
            self._logger.info(f"[EDUCATIONAL] Access stored credentials with: mlopscli secrets show --component artifactory --user {username}")

        return results

    def validate_registry_access(self, target_namespaces: List[str] = None) -> Dict[str, Dict[str, bool]]:
        """
        Validate registry access across namespaces.

        Args:
            target_namespaces: Optional list of namespaces to validate

        Returns:
            Dict mapping namespace to validation results

        Educational Note:
        This method demonstrates validation of registry access,
        checking both secret existence and service account configuration.
        """
        if target_namespaces is None:
            target_namespaces = list(MLOPS_NAMESPACES.keys())

        validation_results = {}

        for namespace in target_namespaces:
            namespace_validation = {
                "namespace_exists": self._ensure_namespace_exists(namespace),
                "secret_exists": False,
                "service_account_configured": False,
            }

            if namespace_validation["namespace_exists"]:
                # Check if registry secret exists
                try:
                    config.load_kube_config()
                    v1 = client.CoreV1Api()
                    v1.read_namespaced_secret(name=ARTIFACTORY_SECRET_NAME, namespace=namespace)
                    namespace_validation["secret_exists"] = True
                except client.exceptions.ApiException:
                    namespace_validation["secret_exists"] = False

                # Check service account configuration
                namespace_validation["service_account_configured"] = check_image_pull_secret(
                    service_account_name="default", namespace=namespace, secret_name=ARTIFACTORY_SECRET_NAME
                )

            validation_results[namespace] = namespace_validation

        # Log validation summary
        fully_configured = sum(1 for validation in validation_results.values() if all(validation.values()))
        total = len(validation_results)

        self._logger.info(f"[VALIDATION] Registry access: {fully_configured}/{total} namespaces fully configured")

        return validation_results


# Convenience functions for backward compatibility and easier imports
def create_registry_secret(
    name: str, server: str, artifactory_username: str, artifactory_token: str, namespace: str, force_create: bool = False
) -> bool:
    """
    Convenience function for creating registry secrets.

    This is a wrapper around create_docker_registry_secret for backward compatibility.
    """
    from mlopscli.utils.kubernetes import create_docker_registry_secret

    return create_docker_registry_secret(name, server, artifactory_username, artifactory_token, namespace, force_create)


def is_kubectl_configured() -> bool:
    """
    Check if kubectl is properly configured with cluster access.

    Returns:
        bool: True if kubectl is configured and can access cluster
    """
    try:
        from kubernetes import config

        config.load_kube_config()
        return True
    except Exception:
        return False
