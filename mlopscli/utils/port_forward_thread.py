import subprocess
import threading
import time

from mlopscli.utils.logger import LogColors
from mlopscli.utils.logger import get_logger

logger = get_logger(__name__)

PORT_FORWARD_TYPE_POD = "pod"
PORT_FORWARD_TYPE_SERVICE = "service"
PORT_FORWARD_START_WAIT_TIME = 5


# CODEREVIEW: Can we use a kubectl python sdk instead of subprocess
class PortForwardThread(threading.Thread):
    def __init__(self, type: str, name: str, namespace: str, local_port: int, remote_port: int) -> None:
        super().__init__()
        self._type = type
        self._name = name
        self._local_port = local_port
        self._remote_port = remote_port
        self._namespace = namespace
        self._stop_event = threading.Event()  # Event to signal stop
        self._process = None  # Placeholder for subprocess

    def run(self) -> None:
        if self._type == PORT_FORWARD_TYPE_POD:
            cmd = [
                "kubectl",
                "port-forward",
                "--address",
                "127.0.0.1",
                f"pod/{self._name}",
                f"{self._local_port}:{self._remote_port}",
                "-n",
                self._namespace,
            ]
        elif self._type == PORT_FORWARD_TYPE_SERVICE:
            cmd = [
                "kubectl",
                "port-forward",
                "--address",
                "127.0.0.1",
                f"service/{self._name}",
                f"{self._local_port}:{self._remote_port}",
                "-n",
                self._namespace,
            ]
        else:
            raise ValueError(f"Unsupported type ({self._type})")

        try:
            self._process = subprocess.Popen(
                cmd, 
                stdout=subprocess.PIPE, 
                stderr=subprocess.PIPE,
                text=True
            )  # noqa: S603
        except Exception as e:
            logger.error(f"Failed to start port forwarding process: {e}")
            return

        try:
            # Keep the thread alive until a stop signal is received
            while not self._stop_event.is_set():
                time.sleep(1)  # Free up CPU
                return_code = self._process.poll()
                if return_code is not None:
                    stdout, stderr = self._process.communicate()
                    if return_code != 0:
                        logger.warning(f"Port forward process terminated with exit code {return_code}")
                        if stderr:
                            logger.warning(f"Port forward stderr: {stderr.strip()}")
                        if stdout:
                            logger.debug(f"Port forward stdout: {stdout.strip()}")
                    else:
                        logger.debug("Port forward process terminated normally")
                    self.stop()
                    break
        finally:
            # Terminate subprocess when stop signal is received
            if self._process is not None:  # Make sure process is initiated
                try:
                    self._process.terminate()  # Gracefully terminate the process
                    self._process.wait(timeout=5)  # Wait for the process to terminate
                except subprocess.TimeoutExpired:
                    logger.warning("Port forward process didn't terminate gracefully, killing it")
                    self._process.kill()
                    self._process.wait()
                except Exception as e:
                    logger.warning(f"Error stopping port forward process: {e}")
                logger.debug("Port forward process stopped")

    def stop(self) -> None:
        self._stop_event.set()  # Set the stop event to True
    
    def is_running(self) -> bool:
        """Check if the port forwarding process is still running."""
        if self._process is None:
            return False
        return self._process.poll() is None
