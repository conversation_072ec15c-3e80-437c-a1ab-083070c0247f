import json
import os
from pathlib import Path
from typing import Dict

from mlopscli.utils.constants import UNKNOWN
from mlopscli.utils.constants import WORKING_DIR
from mlopscli.utils.logger import get_logger

logger = get_logger(__name__)


class InstallationLogger:
    K3S_VERSION = "k3s_version"
    INSTALLATION_INFO_FILE = os.path.join(WORKING_DIR, "mlops_installation_info.json")

    @staticmethod
    def get_log_k3s_version() -> str:
        return InstallationLogger._get_installation_log_value(InstallationLogger.K3S_VERSION)

    @staticmethod
    def set_k3s_version(k3s_version: str | None) -> None:
        InstallationLogger._update_installation_log(InstallationLogger.K3S_VERSION, k3s_version or UNKNOWN)

    @staticmethod
    def delete_k3s() -> None:
        InstallationLogger.set_k3s_version(UNKNOWN)

    @staticmethod
    def _update_installation_log(key: str, value: str) -> None:
        filename = Path(InstallationLogger.INSTALLATION_INFO_FILE)
        directory_path = os.path.dirname(filename)
        if not os.path.exists(directory_path):
            Path(directory_path).mkdir(parents=True, exist_ok=True)

        if InstallationLogger._log_exists_and_not_empty():
            with open(filename, "r") as file:
                installation_log: Dict[str, str] = json.load(file)
        else:
            logger.debug("json set to empty.")
            installation_log: Dict[str, str] = {}

        with open(filename, "w") as file:
            installation_log[key] = value
            json.dump(installation_log, file)

    @staticmethod
    def _log_exists_and_not_empty() -> bool:
        """Checks if the file exists and if there's anything inside of it"""
        filename = Path(InstallationLogger.INSTALLATION_INFO_FILE)
        return os.path.exists(filename) and os.path.getsize(filename) > 0

    @staticmethod
    def _get_installation_log_value(key: str) -> str:
        value = UNKNOWN
        if InstallationLogger._log_exists_and_not_empty():
            with open(InstallationLogger.INSTALLATION_INFO_FILE) as file:
                installation_log: Dict[str, str] = json.load(file)
                value = installation_log.get(key, UNKNOWN)
        else:
            logger.debug(f"Couldn't get {key} because json is empty or doesn't exist.")
        return value
