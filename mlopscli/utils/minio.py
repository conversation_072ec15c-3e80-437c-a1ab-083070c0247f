import time

from minio import Minio
import urllib3

from mlopscli.utils.logger import get_logger
from mlopscli.utils.port_forward_thread import PORT_FORWARD_TYPE_SERVICE
from mlopscli.utils.port_forward_thread import PortForwardThread

logger = get_logger(__name__)

VALID_CERTIFICATE = False
if not VALID_CERTIFICATE:
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


def _create_bucket(minio_url: str, access_key: str, secret_key: str, bucket_name: str) -> None:
    minio_client = Minio(minio_url, access_key=access_key, secret_key=secret_key, secure=True, cert_check=VALID_CERTIFICATE)

    if minio_client.bucket_exists(bucket_name):
        logger.info(f"Bucket '{bucket_name}' already exists.")
    else:
        minio_client.make_bucket(bucket_name)
        logger.info(f"Bucket '{bucket_name}' created successfully.")


def create_s3_bucket(
    service_name: str,
    namespace: str,
    s3_access_key: str,
    s3_secret_key: str,
    s3_host: str,
    s3_port: int,
    bucket_name: str,
) -> None:
    # Start port-forwarding in another thread
    pf_thread = PortForwardThread(
        type=PORT_FORWARD_TYPE_SERVICE,
        name=service_name,
        namespace=namespace,
        local_port=s3_port,
        remote_port=s3_port,
    )
    pf_thread.start()

    # Allow some time for the port-forwarding to establish
    time.sleep(5)
    logger.info("Port forwarding established.")

    try:
        _create_bucket(
            minio_url=f"{s3_host}:{s3_port}",
            access_key=s3_access_key,
            secret_key=s3_secret_key,
            bucket_name=bucket_name,
        )
    except Exception as e:
        logger.error(f"An error occurred: {e}")
        raise e

    finally:
        pf_thread.stop()  # Call the stop method defined in the class
        pf_thread.join()  # Wait for the thread to finish
        logger.info("Port forwarding stopped.")
