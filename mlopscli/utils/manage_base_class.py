from getpass import getpass
import os
from pathlib import Path
from typing import List
from typing import <PERSON>ple

from mlopscli.utils.constants import ARCH_AMD64
from mlopscli.utils.constants import ARCH_ARM64
from mlopscli.utils.constants import TMP_WORKING_DIR
from mlopscli.utils.constants import UNKNOWN
from mlopscli.utils.exceptions import UnsupportedArchitectureException
from mlopscli.utils.logger import get_logger
from mlopscli.utils.system import extract_ipv4_addresses
from mlopscli.utils.system import log_etc_host_file
from mlopscli.utils.system import run_command_with_output
from mlopscli.utils.system import run_sudo_command_with_output

user_password = None


class ManageBaseClass:
    """
    This is a base class that initializes a logger.
    It uses the get_logger function from mlopscli.utils.logger module.
    """

    def __init__(self, logger_name: str = __name__) -> None:
        self._logger = get_logger(logger_name)
        self.get_sudo_password()  # Force getting sudo password at the begining of execution

    def get_sudo_password(self) -> str | None:
        global user_password
        if user_password is None:
            user_password = os.getenv("MLOPSCLI_USER_PASSWORD")
            if user_password is None:
                self._logger.debug("Request for user's password sent.")
                user_password = getpass("Enter your password: ")
                self._logger.debug("sudo password obtained.")

        return user_password

    def get_ips(self, hostname: str, arch: str) -> List[str]:
        if arch == ARCH_ARM64:
            multipass_vm_info = run_sudo_command_with_output(
                command=f"multipass info {hostname}", exit_on_error=False, password=self.get_sudo_password()
            )
            if multipass_vm_info:
                ips = extract_ipv4_addresses(multipass_vm_info)
            else:
                ips = []
            self._logger.debug(f"IPs ({ips})")

        elif arch == ARCH_AMD64:
            ips = run_command_with_output("hostname -I")
            ips = ips.split(" ")
            self._logger.debug(f"IPs ({ips})")
        else:
            raise UnsupportedArchitectureException(arch)

        return ips

    def get_hostname_and_ip(self, arch: str) -> Tuple[str, str]:
        if arch == ARCH_ARM64:
            hostname = "k3s"
            ips = self.get_ips(hostname, arch)
            if len(ips) > 0:
                host_ip = ips[0]
            else:
                host_ip = UNKNOWN
        elif arch == ARCH_AMD64:
            hostname = run_command_with_output("hostname")
            host_ip = "127.0.0.1"
        else:
            raise UnsupportedArchitectureException(arch)
        self._logger.debug(f"hostname: {hostname}, ip: {host_ip}")
        return (hostname, host_ip)

    def _write_update_etc_hosts(self, updates: dict) -> None:
        host_file = "/etc/hosts"
        host_file_temp = os.path.join(TMP_WORKING_DIR, "hosts_temp")
        directory_path = os.path.dirname(host_file_temp)

        # Ensure the temporary directory exists
        if not os.path.exists(directory_path):
            Path(directory_path).mkdir(parents=True, exist_ok=True)

        with open(host_file, "r") as file:
            lines = file.readlines()

        new_lines = []
        for line in lines:
            parts = line.split()
            if len(parts) > 1:
                if parts[1] not in updates.keys():
                    new_lines.append(line)
            else:
                new_lines.append(line)

        for hostname, new_ip in updates.items():
            new_lines.append(f"{new_ip} {hostname} # Managed by mlops-toolchain\n")
            self._logger.debug(f"Adding host ({hostname}) with IP ({new_ip})")

        with open(host_file_temp, "w") as temp_file:
            temp_file.writelines(new_lines)

        self._logger.info(f"Replacing {host_file} content ...")
        run_sudo_command_with_output(f"mv {host_file_temp} {host_file}", password=self.get_sudo_password())
        log_etc_host_file()
