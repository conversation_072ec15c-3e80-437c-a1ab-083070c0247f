import time

import psycopg
from psycopg import OperationalError
from psycopg import sql

from mlopscli.utils.kubernetes import find_available_port
from mlopscli.utils.logger import get_logger
from mlopscli.utils.port_forward_thread import PORT_FORWARD_START_WAIT_TIME
from mlopscli.utils.port_forward_thread import PORT_FORWARD_TYPE_POD
from mlopscli.utils.port_forward_thread import PortForwardThread

logger = get_logger(__name__)


def check_database_exists(dbname: str, user: str, password: str, host: str, port: str) -> bool:
    try:
        cursor = None
        connection = None

        connection = psycopg.connect(user=user, password=password, host=host, port=port, dbname=dbname)
        cursor = connection.cursor()

        # SQL statement to check if the database exists
        query = sql.SQL("SELECT 1 FROM pg_database WHERE datname = %s;")
        cursor.execute(query, (dbname,))

        # Fetch result. If the database exists, it should return 1
        exists = cursor.fetchone()

        # Return True if it exists, False otherwise
        return exists is not None

    except OperationalError:
        # Exception is expected is the database doesn't exist
        return False
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()


def create_connection_string(
    postgresql_username: str,
    postgresql_password: str,
    postgresql_host: str,
    postgresql_port: int,
    db_name: str,
) -> str:
    return f"postgres://{postgresql_username}:{postgresql_password}@{postgresql_host}:{postgresql_port}/{db_name}"


def create_postgres_database(
    pod_name: str,
    namespace: str,
    postgresql_username: str,
    postgresql_password: str,
    postgresql_host: str,
    postgresql_port: int,
    db_name: str,
) -> None:
    # Find an available port for port forwarding
    local_port = find_available_port(start_port=postgresql_port)
    if local_port != postgresql_port:
        logger.info(f"Port {postgresql_port} is busy, using port {local_port} for port forwarding")

    # Start port-forwarding in another thread
    pf_thread = PortForwardThread(
        type=PORT_FORWARD_TYPE_POD,
        name=pod_name,
        namespace=namespace,
        local_port=local_port,
        remote_port=postgresql_port,
    )
    pf_thread.start()

    # Allow some time for the port-forwarding to establish
    time.sleep(PORT_FORWARD_START_WAIT_TIME)
    logger.info("Port forwarding established.")

    try:
        if check_database_exists(
            dbname=db_name,
            user=postgresql_username,
            password=postgresql_password,
            host=postgresql_host,
            port=str(local_port),
        ):
            logger.info(f"Database ({db_name}) already exist")
        else:
            cursor = None
            connection = None
            connection = psycopg.connect(
                dbname="postgres",
                user=postgresql_username,
                password=postgresql_password,
                host=postgresql_host,
                port=local_port,
            )
            connection.autocommit = True  # Enable autocommit mode
            cursor = connection.cursor()
            create_db_query = sql.SQL("CREATE DATABASE {}").format(sql.Identifier(db_name))

            try:
                cursor.execute(create_db_query)
                logger.info(f"Database '{db_name}' created successfully.")
            except psycopg.Error as e:
                logger.error(f"Error creating database: {e}")
            finally:
                if cursor:
                    cursor.close()
                if connection:
                    connection.close()
    except Exception as e:
        logger.error(f"An error occurred: {e}")

    finally:
        pf_thread.stop()  # Call the stop method defined in the class
        pf_thread.join()  # Wait for the thread to finish
        logger.info("Port forwarding stopped.")


def create_postgres_database_without_port_forwarding(
    postgresql_username: str,
    postgresql_password: str,
    postgresql_host: str,
    postgresql_port: int,
    db_name: str,
) -> None:
    try:
        if check_database_exists(
            dbname=db_name,
            user=postgresql_username,
            password=postgresql_password,
            host=postgresql_host,
            port=str(postgresql_port),
        ):
            logger.info(f"Database ({db_name}) already exist")
        else:
            connection = psycopg.connect(
                dbname="postgres",
                user=postgresql_username,
                password=postgresql_password,
                host=postgresql_host,
                port=postgresql_port,
            )
            connection.autocommit = True  # Enable autocommit mode
            cursor = connection.cursor()
            create_db_query = sql.SQL("CREATE DATABASE {}").format(sql.Identifier(db_name))

            try:
                cursor.execute(create_db_query)
                logger.info(f"Database '{db_name}' created successfully.")
            except psycopg.Error as e:
                logger.error(f"Error creating database: {e}")
            finally:
                if cursor:
                    cursor.close()
                if connection:
                    connection.close()
    except Exception as e:
        logger.error(f"An error occurred: {e}")