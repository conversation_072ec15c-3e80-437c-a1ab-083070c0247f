# mlopscli/utils/installation_ingress_resolver.py

import json
import yaml
import os
from kubernetes import client
from kubernetes import config
from kubernetes.client.exceptions import ApiException

from mlopscli.utils.constants import CLUSTER_ISSUER
from mlopscli.utils.constants import INGRESS_ANNOTATIONS
from mlopscli.utils.constants import INSTALLATION_LOCATION_REMOTE_STR
from mlopscli.utils.constants import TDS_PROJECT_DOMAIN_NAME
from mlopscli.utils.logger import get_logger

logger = get_logger(__name__)

# Service-specific constants
ZOT_DPSC = "zot.dpsc"
LAKEFS_FQDN = "lakefs.dpsc"

# Polycore GRPC paths for service routing
POLYCORE_GRPC_PATHS = [
    {"path": "/health", "pathType": "Prefix"},
    {"path": "/polyflow.v1.ObservabilityService", "pathType": "Prefix"},
    {"path": "/polyflow.v1.core.PipelineService", "pathType": "Prefix"},
    {"path": "/polyflow.v1.PolyCoreService", "pathType": "Prefix"},
    {"path": "/polyflow.v1.core.ModelService", "pathType": "Prefix"},
]

def _get_component_fqdn(component: str, domain_name: str) -> str:
    """Helper function to generate component FQDN."""
    return f"{component}.{domain_name}"

def configure_ingress_class() -> str:
    """Get the recommended ingress class for the current cluster environment."""
    common_ingress_classes = ["cilium", "nginx", "default"]
    for ic in common_ingress_classes:
        if check_ingress_class_exists(ic):
            logger.debug(f"Found available ingress class: {ic}")
            return ic
    default_ic = get_default_ingress_class()
    if default_ic:
        return default_ic
    logger.warning("No ingress class found, falling back to 'nginx'")
    return "nginx"

def check_ingress_class_exists(ingress_class_name: str) -> bool:
    config.load_kube_config()
    networking_v1 = client.NetworkingV1Api()
    try:
        networking_v1.read_ingress_class(name=ingress_class_name)
        logger.debug(f"Ingress class '{ingress_class_name}' exists.")
        return True
    except ApiException as e:
        if e.status == 404:
            logger.debug(f"Ingress class '{ingress_class_name}' not found.")
            return False
        else:
            logger.error(f"Error checking ingress class '{ingress_class_name}': {e}")
            return False

def get_default_ingress_class() -> str | None:
    config.load_kube_config()
    networking_v1 = client.NetworkingV1Api()
    try:
        ingress_classes = networking_v1.list_ingress_class()
        for ic in ingress_classes.items:
            if ic.metadata.annotations and ic.metadata.annotations.get("ingressclass.kubernetes.io/is-default-class") == "true":
                logger.debug(f"Default ingress class found: {ic.metadata.name}")
                return ic.metadata.name
        logger.debug("No default ingress class found.")
        return None
    except ApiException as e:
        logger.error(f"Error getting default ingress class: {e}")
        return None

def get_ingress_annotations(ingress_class: str, annotation_configs: dict) -> dict:
    annotations = {}
    controller_mapping = INGRESS_ANNOTATIONS.get(ingress_class, INGRESS_ANNOTATIONS["nginx"])
    for logical_name, value in annotation_configs.items():
        annotation_key = controller_mapping.get(logical_name)
        if annotation_key is not None and value is not None:
            annotations[annotation_key] = value
    return annotations

def create_service_ingress_config(service_name: str, ingress_class: str = "", **kwargs) -> dict:
    """
    Create a complete ingress configuration for a service with controller-specific annotations.
    Args:
        service_name: Name of the service
        ingress_class: Ingress controller class (auto-detected if None)
        **kwargs: Service-specific configuration options:
            - hosts: List of hostnames (strings or dicts)
            - tls_secret: TLS secret name
            - backend_protocol: 'HTTP', 'HTTPS', 'GRPC'
            - enable_tls_passthrough: Boolean
            - proxy_body_size: Body size limit (e.g., '0', '64m')
            - proxy_timeouts: Dict with 'read' and 'send' timeout values
            - server_snippet: Custom server configuration
            - paths: List of path configurations
    Returns:
        Complete ingress configuration dict
    """
    if ingress_class is None or ingress_class.strip() == "":
        logger.debug("No ingress class provided, auto-detecting...")
        try:
            ingress_class = configure_ingress_class()
        except Exception as e:
            logger.error(f"Failed to auto-detect ingress class: {e}")
            raise
    logger.debug(f"Using ingress class: {ingress_class}")

    annotation_config = {}
    backend_protocol = kwargs.get("backend_protocol", "HTTP")
    if backend_protocol in ["HTTPS", "GRPC"]:
        annotation_config["backend_protocol"] = backend_protocol
    if kwargs.get("enable_tls_passthrough", False):
        annotation_config["tls_passthrough"] = "true"
    if "proxy_body_size" in kwargs:
        annotation_config["proxy_body_size"] = kwargs["proxy_body_size"]
    if "proxy_timeouts" in kwargs:
        timeouts = kwargs["proxy_timeouts"]
        if "read" in timeouts:
            annotation_config["proxy_read_timeout"] = str(timeouts["read"])
        if "send" in timeouts:
            annotation_config["proxy_send_timeout"] = str(timeouts["send"])
    if "server_snippet" in kwargs:
        annotation_config["server_snippet"] = kwargs["server_snippet"]
    if "loadbalancer_mode" in kwargs:
        annotation_config["loadbalancer_mode"] = kwargs["loadbalancer_mode"]

    controller_annotations = get_ingress_annotations(ingress_class, annotation_config)
    controller_annotations[CLUSTER_ISSUER] = "external-ca-issuer"

    hosts = kwargs.get("hosts", [f"{service_name}.dpsc"])
    # If all hosts are strings, output as list of strings (for Helm compatibility)
    if all(isinstance(h, str) for h in hosts):
        host_configs = [str(h) for h in hosts]
    else:
        host_configs = []
        for host in hosts:
            if isinstance(host, dict):
                host_configs.append(host)
            else:
                paths = kwargs.get("paths", [{"path": "/", "pathType": "Prefix"}])
                host_configs.append({"host": host, "paths": paths})

    tls_secret = kwargs.get("tls_secret", f"{service_name}-tls-secret")
    tls_hosts = []
    for host in hosts:
        if isinstance(host, dict):
            tls_hosts.append(host["host"])
        else:
            tls_hosts.append(host)
    tls_config = [{"hosts": tls_hosts, "secretName": tls_secret}]

    ingress_config = {"annotations": controller_annotations, "hosts": host_configs, "tls": tls_config}
    if ingress_class and ingress_class.strip():
        ingress_config["ingressClassName"] = ingress_class
    return ingress_config

def get_keycloak_ingress_config(ingress_class: str, installation_location: str = "", domain_name: str = "") -> dict:
    hosts = ["keycloak.dpsc", "keycloak.processing.svc.cluster.local"]
    if installation_location == INSTALLATION_LOCATION_REMOTE_STR:
        effective_domain_name = domain_name or TDS_PROJECT_DOMAIN_NAME
        remote_keycloak_host = _get_component_fqdn("keycloak", effective_domain_name)
        hosts.append(remote_keycloak_host)

    return create_service_ingress_config(
        service_name="keycloak-http",
        ingress_class=ingress_class,
        hosts=hosts,
        tls_secret="keycloak-tls-secret",
        backend_protocol="HTTP",
        enable_tls_passthrough=False,
        paths=[{"path": "/auth", "pathType": "Prefix"}],
    )

def get_polycore_ingress_config(ingress_class: str, installation_location: str = "", domain_name: str = "") -> dict:
    if installation_location == INSTALLATION_LOCATION_REMOTE_STR:
        effective_domain_name = domain_name or TDS_PROJECT_DOMAIN_NAME
        remote_polycore_host = _get_component_fqdn("polycore", effective_domain_name)
        remote_polycore_obs_host = _get_component_fqdn("polycore-obs", effective_domain_name)
    else:
        remote_polycore_host = None
        remote_polycore_obs_host = None

    grpc_hosts = [
        {"host": "polycore.dpsc", "paths": POLYCORE_GRPC_PATHS},
        {"host": "polycore.mlops-toolchain", "paths": POLYCORE_GRPC_PATHS},
        {"host": "polycore.mlops-toolchain.svc.cluster.local", "paths": POLYCORE_GRPC_PATHS},
    ]
    rest_hosts = [
        {"host": "polycore-obs.dpsc", "paths": [{"path": "/", "pathType": "Prefix"}]},
        {"host": "polycore-obs.mlops-toolchain", "paths": [{"path": "/", "pathType": "Prefix"}]},
        {"host": "polycore-obs.mlops-toolchain.svc.cluster.local", "paths": [{"path": "/", "pathType": "Prefix"}]},
    ]
    grpc_tls_hosts = ["polycore.dpsc", "polycore.mlops-toolchain", "polycore.mlops-toolchain.svc.cluster.local"]
    rest_tls_hosts = ["polycore-obs.dpsc", "polycore-obs.mlops-toolchain", "polycore-obs.mlops-toolchain.svc.cluster.local"]

    if remote_polycore_host:
        grpc_hosts.append({"host": remote_polycore_host, "paths": POLYCORE_GRPC_PATHS})
        grpc_tls_hosts.append(remote_polycore_host)
    if remote_polycore_obs_host:
        rest_hosts.append({"host": remote_polycore_obs_host, "paths": [{"path": "/", "pathType": "Prefix"}]})
        rest_tls_hosts.append(remote_polycore_obs_host)

    grpc_config = create_service_ingress_config(
        service_name="polycore",
        ingress_class=ingress_class,
        hosts=grpc_hosts,
        tls_secret="polycore-ingress-certs",
        backend_protocol="GRPC",
        enable_tls_passthrough=True,
        proxy_body_size="0",
        proxy_timeouts={"read": 600, "send": 600},
        server_snippet="grpc_read_timeout 600s;\ngrpc_send_timeout 600s;\nclient_body_timeout 600s;\n",
        loadbalancer_mode="shared",
    )
    grpc_config["className"] = ingress_class
    grpc_config["tls"][0]["hosts"] = grpc_tls_hosts

    rest_config = create_service_ingress_config(
        service_name="polycore-obs",
        ingress_class=ingress_class,
        hosts=rest_hosts,
        tls_secret="polycore-obs-ingress-certs",
        backend_protocol="HTTP",
        enable_tls_passthrough=False,
        proxy_body_size="0",
    )
    rest_config["tls"][0]["hosts"] = rest_tls_hosts
    rest_config["className"] = ingress_class

    return {"grpc": grpc_config, "rest": rest_config}

def get_minio_dual_ingress_config(ingress_class: str, installation_location: str = "", domain_name: str = "") -> tuple[dict, dict]:
    api_hosts = ["minio.dpsc", "s3.object-store", "s3.object-store.svc.cluster.local"]
    console_hosts = ["minio-console.dpsc"]
    if installation_location == INSTALLATION_LOCATION_REMOTE_STR:
        effective_domain_name = domain_name or TDS_PROJECT_DOMAIN_NAME
        remote_minio_host = _get_component_fqdn("minio", effective_domain_name)
        remote_console_host = _get_component_fqdn("minio-console", effective_domain_name)
        api_hosts.append(remote_minio_host)
        console_hosts.append(remote_console_host)
    api_config = create_service_ingress_config(
        service_name="minio",
        ingress_class=ingress_class,
        hosts=api_hosts,
        tls_secret="minio-tls-secret",
        backend_protocol="HTTPS",
        enable_tls_passthrough=True,
        proxy_body_size="0",
    )
    console_config = create_service_ingress_config(
        service_name="minio-console",
        ingress_class=ingress_class,
        hosts=console_hosts,
        tls_secret="minio-console-tls-secret",
        backend_protocol="HTTPS",
        enable_tls_passthrough=True,
        proxy_body_size="0",
    )
    return api_config, console_config

def get_zot_ingress_config(ingress_class: str, installation_location: str = "", domain_name: str = "") -> dict:
    hosts = [ZOT_DPSC, "zot.zot.svc.cluster.local"]
    if installation_location == INSTALLATION_LOCATION_REMOTE_STR:
        effective_domain_name = domain_name or TDS_PROJECT_DOMAIN_NAME
        remote_zot_host = _get_component_fqdn("zot", effective_domain_name)
        hosts.append(remote_zot_host)
    return create_service_ingress_config(
        service_name="zot",
        ingress_class=ingress_class,
        hosts=hosts,
        tls_secret="zot-ingress-certs",
        backend_protocol="HTTPS",
        enable_tls_passthrough=True,
        proxy_body_size="0",
    )

def get_argo_ingress_config(ingress_class: str, installation_location: str = "", domain_name: str = "") -> dict:
    hosts = ["argo.dpsc", "argo-processing-server.processing", "argo-processing-server.processing.svc.cluster.local"]
    if installation_location == INSTALLATION_LOCATION_REMOTE_STR:
        effective_domain_name = domain_name or TDS_PROJECT_DOMAIN_NAME
        remote_argo_host = _get_component_fqdn("argo", effective_domain_name)
        hosts.append(remote_argo_host)
    return create_service_ingress_config(
        service_name="argo",
        ingress_class=ingress_class,
        hosts=hosts,
        tls_secret="argo-tls-secret",
        backend_protocol="HTTP",
        enable_tls_passthrough=False,
        paths=[{"path": "/argo", "pathType": "Prefix"}]
    )

def get_grafana_ingress_config(ingress_class: str, installation_location: str = "", domain_name: str = "") -> dict:
    hosts = ["grafana.admin.dpsc", "grafana.monitoring.svc.cluster.local"]
    if installation_location == INSTALLATION_LOCATION_REMOTE_STR:
        effective_domain_name = domain_name or TDS_PROJECT_DOMAIN_NAME
        remote_grafana_host = _get_component_fqdn("grafana.admin", effective_domain_name)
        hosts.append(remote_grafana_host)
    return create_service_ingress_config(
        service_name="grafana",
        ingress_class=ingress_class,
        hosts=hosts,
        tls_secret="grafana-tls-secret",
        backend_protocol="HTTP",
        enable_tls_passthrough=False,
    )

def get_lakefs_ingress_config(ingress_class: str, installation_location: str = "", domain_name: str = "") -> dict:
    lakefs_paths = [{"path": "/", "pathType": "Prefix"}]
    hosts = [{"host": LAKEFS_FQDN, "paths": lakefs_paths}]
    tls_hosts = [LAKEFS_FQDN, "my-lakefs.lakefs.svc.cluster.local"]
    if installation_location == INSTALLATION_LOCATION_REMOTE_STR:
        effective_domain_name = domain_name or TDS_PROJECT_DOMAIN_NAME
        remote_lakefs_host = _get_component_fqdn("lakefs", effective_domain_name)
        hosts.append({"host": remote_lakefs_host, "paths": lakefs_paths})
        tls_hosts.append(remote_lakefs_host)
    config = create_service_ingress_config(
        service_name="lakefs",
        ingress_class=ingress_class,
        hosts=hosts,
        tls_secret="lakefs-tls-secret",
        backend_protocol="HTTP",
        enable_tls_passthrough=False,
        proxy_body_size="64m",
    )
    config["tls"][0]["hosts"] = tls_hosts
    return config

def get_mlflow_ingress_config(ingress_class: str, installation_location: str = "", domain_name: str = "") -> dict:
    hosts = ["mlflow.dpsc", "mlflow.processing.svc.cluster.local"]
    if installation_location == INSTALLATION_LOCATION_REMOTE_STR:
        effective_domain_name = domain_name or TDS_PROJECT_DOMAIN_NAME
        remote_mlflow_host = _get_component_fqdn("mlflow", effective_domain_name)
        hosts.append(remote_mlflow_host)
    return create_service_ingress_config(
        service_name="mlflow",
        ingress_class=ingress_class,
        hosts=hosts,
        tls_secret="mlflow-tls-secret",
        backend_protocol="HTTP",
        enable_tls_passthrough=False,
    )

def get_kserve_ingress_config(ingress_class: str, installation_location: str = "", domain_name: str = "") -> dict:
    hosts = ["kserve.dpsc", "kserve-webhook-server-service.kserve.svc.cluster.local"]
    if installation_location == INSTALLATION_LOCATION_REMOTE_STR:
        effective_domain_name = domain_name or TDS_PROJECT_DOMAIN_NAME
        remote_kserver_host = _get_component_fqdn("kserver", effective_domain_name)
        hosts.append(remote_kserver_host)
    return create_service_ingress_config(
        service_name="kserve-webhook-server-service",
        ingress_class=ingress_class,
        hosts=hosts,
        tls_secret="kserve-tls-secret",
        backend_protocol="HTTP",
        enable_tls_passthrough=False,
    )

def get_apisix_ingress_config(ingress_class: str, installation_location: str = "", domain_name: str = "") -> dict:
    """Get APISIX API Gateway ingress configuration."""
    hosts = ["api.dpsc", "apisix-gateway.api-gateway.svc.cluster.local"]
    if installation_location == INSTALLATION_LOCATION_REMOTE_STR:
        effective_domain_name = domain_name or TDS_PROJECT_DOMAIN_NAME
        remote_api_host = _get_component_fqdn("api", effective_domain_name)
        hosts.append(remote_api_host)
    
    return create_service_ingress_config(
        service_name="apisix-gateway",
        ingress_class=ingress_class,
        hosts=hosts,
        tls_secret="apisix-gateway-tls-secret",
        backend_protocol="HTTP",
        enable_tls_passthrough=False,
        proxy_body_size="0",
        proxy_timeouts={"read": 300, "send": 300},
    )

def get_apisix_admin_ingress_config(ingress_class: str, installation_location: str = "", domain_name: str = "") -> dict:
    """Get APISIX Admin API ingress configuration."""
    hosts = ["api-admin.dpsc"]
    if installation_location == INSTALLATION_LOCATION_REMOTE_STR:
        effective_domain_name = domain_name or TDS_PROJECT_DOMAIN_NAME
        remote_admin_host = _get_component_fqdn("api-admin", effective_domain_name)
        hosts.append(remote_admin_host)
    
    return create_service_ingress_config(
        service_name="apisix-admin",
        ingress_class=ingress_class,
        hosts=hosts,
        tls_secret="apisix-admin-tls-secret",
        backend_protocol="HTTP",
        enable_tls_passthrough=False,
        # Add IP restrictions for admin access
        ip_restrictions=["10.0.0.0/8", "**********/12", "***********/16"],
    )

def build_ingress_resource(name: str, ingress_spec: dict, api_version: str = "networking.k8s.io/v1") -> dict:
    return {
        "apiVersion": api_version,
        "kind": "Ingress",
        "metadata": {
            "name": name,
            "annotations": ingress_spec.get("annotations", {})
        },
        "spec": {
            k: v for k, v in ingress_spec.items() if k != "annotations"
        }
    }

def to_yaml(data: dict) -> str:
    return yaml.dump(data, default_flow_style=False, sort_keys=False, allow_unicode=True)
