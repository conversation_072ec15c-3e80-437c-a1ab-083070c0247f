"""Loggin Wrapper Module"""

# https://docs.python.org/3/howto/logging-cookbook.html

import logging
from logging import LogRecord
import os
from pathlib import Path
import re

from mlopscli.utils.constants import DEFAULT_LOG_FILE
from mlopscli.utils.constants import OBFUSCATED_REPLACEMENT_VALUE
from mlopscli.utils.constants import SENSITIVE_VALUES

loggers = {}
LOG_FORMAT_FULL = "%(asctime)s - %(levelname)-8s - %(name)s - %(funcName)s - %(message)s"
LOG_FORMAT_CONSOLE = "[%(levelname)-8s]:\x1b[2m %(name)s - %(funcName)s - \x1b[22m %(message)s"
LOG_FORMAT_CONSOLE_BARE = "[%(levelname)-8s]: %(message)s"
LOG_FORMAT_CONSOLE_HEADERS = "[%(levelname)-8s]: %(message)s"
LOG_FORMAT_FILE_HEADERS = "%(asctime)s - %(levelname)-8s %(message)s"

LOG_DATE_FORMAT = "%Y/%m/%d %H:%M:%S %Z"

LOG_LEVEL = "LOG_LEVEL"
LOG_FILE = "LOG_FILE"


class LogColors:
    blue = "\x1b[0;34m"
    green = "\x1b[0;32m"
    red = "\x1b[31;20m"
    bold_red = "\x1b[31;1m"
    reset = "\x1b[0m"
    yellow = "\x1b[0;93;20m"
    grey = "\x1b[0;90m"

    bold_magenta = "\x1b[0;38;2;204;0;255;1m"
    purple = "\x1b[0;35m"
    mauve = "\x1b[0;38;2;104;0;128m"

    debug = blue
    info = green
    error = red
    warning = yellow
    critical = bold_red

    important = "\x1b[0;93;44m"
    important_title = "\x1b[0;97;44;1m"
    file_content = grey
    title = bold_magenta
    special = mauve
    comment = purple


class CustomFormatter(logging.Formatter):
    def __init__(self, format_str: str = LOG_FORMAT_FULL) -> None:
        super(CustomFormatter, self).__init__(format_str, LOG_DATE_FORMAT)

        self.FORMATS = {
            logging.DEBUG: LogColors.debug + format_str + LogColors.reset,
            logging.INFO: LogColors.info + format_str + LogColors.reset,
            logging.WARNING: LogColors.warning + format_str + LogColors.reset,
            logging.ERROR: LogColors.error + format_str + LogColors.reset,
            logging.CRITICAL: LogColors.critical + format_str + LogColors.reset,
        }

    def format(self, record: LogRecord) -> str:
        record = self.obfuscate_sensitive_info(record)
        log_fmt = self.FORMATS.get(record.levelno)
        formatter = logging.Formatter(log_fmt, LOG_DATE_FORMAT)
        return formatter.format(record)

    def obfuscate_sensitive_info(self, record: LogRecord) -> LogRecord:
        for sw in SENSITIVE_VALUES:
            record.msg = re.sub(sw, OBFUSCATED_REPLACEMENT_VALUE, record.msg)
        return record


def get_logger(
    name: str,
    log_file: str = DEFAULT_LOG_FILE,
    file_level: int = logging.DEBUG,
    file_format: str = LOG_FORMAT_FULL,
    console_level: int = logging.INFO,
    console_format: str = LOG_FORMAT_CONSOLE,
) -> logging.Logger:
    """Get logger"""
    global loggers

    already_defined_logger = loggers.get(name)
    if already_defined_logger:
        return already_defined_logger

    env_log_level = os.getenv(LOG_LEVEL)
    env_log_file = os.getenv(LOG_FILE)

    if env_log_file:
        log_file = env_log_file

    if env_log_level:
        console_level = logging.getLevelName(env_log_level)

    dirname = os.path.dirname(log_file)
    Path(dirname).mkdir(parents=True, exist_ok=True)
    logger = logging.getLogger(name)
    logger.setLevel(logging.DEBUG)

    file_handler = logging.FileHandler(log_file)
    file_handler.setFormatter(CustomFormatter(file_format))
    file_handler.setLevel(file_level)
    logger.addHandler(file_handler)

    console_handler = logging.StreamHandler()
    console_handler.setFormatter(CustomFormatter(console_format))
    console_handler.setLevel(console_level)
    logger.addHandler(console_handler)

    loggers[name] = logger
    return logger
