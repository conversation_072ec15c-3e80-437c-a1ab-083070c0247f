"""This module provides functionality to build and log a detailed header
containing system and environment information, formatted for clarity.
It includes sections for host, hardware, OS, Python environment, and K3S
installation, with color-coded output for better readability."""

import logging

from mlopscli.utils.constants import FAILED
from mlopscli.utils.constants import UNKNOWN
from mlopscli.utils.host_info import HostInfo
from mlopscli.utils.host_info import get_cli_command
from mlopscli.utils.host_info import get_host_info
from mlopscli.utils.installation_logger import InstallationLogger
from mlopscli.utils.logger import LOG_FORMAT_CONSOLE_HEADERS
from mlopscli.utils.logger import LOG_FORMAT_FILE_HEADERS
from mlopscli.utils.logger import LogColors
from mlopscli.utils.logger import get_logger
from mlopscli.utils.system import is_containerized

logger = get_logger("header", file_format=LOG_FORMAT_FILE_HEADERS, console_format=LOG_FORMAT_CONSOLE_HEADERS)

COLORED_UNKNOWN = LogColors.warning + "unknown"
LINE = LogColors.special + "------------------------------------------------------" + LogColors.reset
ROW = LogColors.special + "- "


class HeaderManager:
    """
    Manages the build of the log's header, to log it all at once, after it's fully constructed.
    This is done to avoid regular logs cutting through the header if it were logged as it's built.
    """

    _lines: list[dict[int, str]] = []
    """list of dictionaries being `{logging.LEVEL: "content"}`. level is a literal from the logging module."""

    def _get_preffix(self, tabs: int) -> str:
        preffix = "|\t" * tabs
        return preffix

    def __init__(self) -> None:
        self._lines = []

    def add_line(self, level: int = logging.DEBUG) -> None:
        self._lines.append({level: LINE})

    def add_section(self, title: str, tabs_before_key: int = 0, error: bool = False) -> None:
        preffix = self._get_preffix(tabs_before_key)

        level = logging.DEBUG
        text_color = LogColors.title
        if error:
            text_color = LogColors.bold_red
            level = logging.ERROR
        self._lines.append({level: f"{ROW}{preffix}{text_color}{title}"})

    def add_title_info(self, title: str, data: str) -> None:
        """example: ``- title: [data]``"""
        self._lines.append({logging.INFO: f"{ROW}{LogColors.title}{title}: [{LogColors.info}{data}{LogColors.title}]"})

    def add_key_value(self, key_name: str, value: str, error: bool = False, tabs_after_key: int = 1, tabs_before_key: int = 1) -> None:
        """
        Adds a `key: value` pair to the header. With customizable indentations.

        Args:
            key_name (str): short description of the key.
            value (str): value to log.
            error (bool, optional): Logs the line at an error level. Defaults to False.
            tabs_after_key (int, optional): To align values when the key is too short compared to others of the same indentation. Defaults to 1.
            tabs_before_key (int, optional): To indent under sub-sections. Defaults to 1.
        """
        suffix = ":" + "\t" * tabs_after_key
        preffix = self._get_preffix(tabs_before_key)

        if error:
            self._lines.append({logging.ERROR: f"{ROW}{preffix}{LogColors.critical}{key_name}{suffix}{LogColors.reset}{LogColors.error}{value}"})
        else:
            self._lines.append({logging.DEBUG: f"{ROW}{preffix}{key_name}{suffix}{LogColors.comment}{value}"})

    def add_value(self, value: str, error: bool = False, tabs_before_key: int = 2, intendation: int = 1) -> None:
        """Just like `add_key_value` but without the key portion."""
        preffix = self._get_preffix(intendation)
        alignment = "\t" * tabs_before_key

        if error:
            self._lines.append({logging.ERROR: f"{ROW}{preffix}{alignment}{LogColors.error}{value}"})
        else:
            self._lines.append({logging.DEBUG: f"{ROW}{preffix}{alignment}{LogColors.comment}{value}"})

    def flush(self) -> None:
        """Print all the saved lines, at once, in the logs."""
        for line in self._lines:
            key, value = next(iter(line.items()))
            if key == logging.ERROR:
                logger.error(value)
            elif key == logging.DEBUG:
                logger.debug(value)
            else:
                logger.info(value)


def format_pourcentages(text: str) -> str:
    if text == UNKNOWN:
        return COLORED_UNKNOWN
    elif text == FAILED:
        return text
    return text + " %"


def format_bytes(text: str) -> str:
    if text == UNKNOWN:
        return COLORED_UNKNOWN
    elif text == FAILED:
        return text

    in_bytes = int(text)
    if in_bytes < 1000:
        return str(in_bytes) + " B"

    in_kilo = int(in_bytes / 1000)
    if in_kilo < 1000:
        return str(in_kilo) + " kB"

    in_mega = int(in_kilo / 1000)
    if in_mega < 1000:
        return str(in_mega) + " MB"

    return str(int(in_mega / 1000)) + " GB"


def user_cli_command_header(manager: HeaderManager, info: HostInfo) -> None:
    """Log the entire CLI call command. Standalone from other headers."""
    version = info.cli_version
    command = get_cli_command()

    if version == UNKNOWN:
        version = COLORED_UNKNOWN

    manager.add_title_info("executing", command)
    manager.add_title_info("version  ", version)


def host_section(manager: HeaderManager, info: HostInfo) -> None:
    host_name = info.hostname
    architecture = info.uname_arch

    host_ip = info.hostname_ip
    platform = info.platform
    containerized = is_containerized()
    ips = info.ips

    containerized_info = LogColors.info + "local"
    if containerized:
        containerized_info = LogColors.warning + "containerized"

    name_error = False
    if host_name == UNKNOWN:
        name_error = True
        host_name = COLORED_UNKNOWN

    host_ip_error = False
    if host_ip == UNKNOWN:
        host_ip_error = True
        host_ip = COLORED_UNKNOWN
    elif host_ip == FAILED:
        host_ip_error = True
        host_ip = "support error"

    section_in_error = host_ip_error or name_error

    manager.add_section("host", error=section_in_error)
    manager.add_key_value("name", host_name, tabs_after_key=2, error=name_error)
    manager.add_key_value("IPv4", host_ip, tabs_after_key=2, error=host_ip_error)

    if ips != []:
        for ip in ips:
            manager.add_value(ip)

    manager.add_key_value("platform", platform)
    manager.add_key_value("get_arch()", architecture)
    manager.add_key_value("environment", containerized_info)


def hardware_section(manager: HeaderManager, info: HostInfo) -> None:
    bits = info.architecture_bits
    linkage = info.architecture_linkage
    architecture = info.processor_architecture
    processor_name = info.processor_name

    total_memory = info.memory_total_capacity_in_bytes
    used_memory = info.memory_used_in_pourcent
    available_memory = info.memory_left_in_bytes

    total_disk = info.disk_total_capacity_in_bytes
    used_disk = info.disk_used_in_pourcent
    available_disk = info.disk_left_in_bytes

    total_disk = format_bytes(total_disk)
    available_disk = format_bytes(available_disk)
    used_disk = format_pourcentages(used_disk)
    total_memory = format_bytes(total_memory)
    available_memory = format_bytes(available_memory)
    used_memory = format_pourcentages(used_memory)

    bits_error = False
    if bits == UNKNOWN:
        bits_error = True
        bits = "Can't know bit amount of host architecture."

    architecture_error = False
    if architecture == UNKNOWN:
        architecture_error = True
        architecture = "Failed to get CPU architecture"

    if linkage == UNKNOWN:
        linkage = COLORED_UNKNOWN

    if processor_name == UNKNOWN:
        processor_name = COLORED_UNKNOWN

    processor_error = architecture_error or bits_error
    section_error = processor_error

    manager.add_section("hardware", error=section_error)
    manager.add_section("processor", error=processor_error, tabs_before_key=1)
    manager.add_key_value("bits", bits, error=bits_error, tabs_after_key=2, tabs_before_key=2)
    manager.add_key_value("linkage", linkage, tabs_before_key=2)
    manager.add_key_value("name", processor_name, tabs_before_key=2, tabs_after_key=2)
    manager.add_key_value("architecture", architecture, tabs_before_key=2, error=architecture_error)

    manager.add_section("memory", tabs_before_key=1)
    manager.add_key_value("total", total_memory, tabs_before_key=2)
    manager.add_key_value("left", available_memory, tabs_before_key=2)
    manager.add_key_value("used", used_memory, tabs_before_key=2)

    manager.add_section("disk", tabs_before_key=1)
    manager.add_key_value("total", total_disk, tabs_before_key=2)
    manager.add_key_value("left", available_disk, tabs_before_key=2)
    manager.add_key_value("used", used_disk, tabs_before_key=2)


def python_section(manager: HeaderManager, info: HostInfo) -> None:
    version = info.python_version
    exec_path = info.python_executable_path
    revision = info.python_revision
    build = info.python_build
    compiler = info.python_compiler
    implementation = info.python_implementation
    branch = info.python_branch

    can_use_psutil = info.can_use_psutil

    if can_use_psutil:
        psutil_text = LogColors.green + "available"
    else:
        psutil_text = LogColors.error + "unavailable"

    if revision == UNKNOWN:
        revision = COLORED_UNKNOWN

    if implementation == UNKNOWN:
        implementation = COLORED_UNKNOWN

    if branch == UNKNOWN:
        branch = COLORED_UNKNOWN

    manager.add_section("python")
    manager.add_key_value("exec path", exec_path)
    manager.add_key_value("implementation", implementation)
    manager.add_key_value("compiler", compiler)
    manager.add_key_value("branch", branch, tabs_after_key=2)
    manager.add_key_value("version", version)
    manager.add_key_value("build", build, tabs_after_key=2)
    manager.add_key_value("revision", revision)
    manager.add_key_value("psutil", psutil_text, tabs_after_key=2)


def os_section(manager: HeaderManager, info: HostInfo) -> None:
    name = info.os_name
    version = info.os_version
    release = info.os_release

    if name == UNKNOWN:
        name = COLORED_UNKNOWN

    if version == UNKNOWN:
        version = COLORED_UNKNOWN

    if release == UNKNOWN:
        release = COLORED_UNKNOWN

    name_error = name == COLORED_UNKNOWN
    version_error = version == COLORED_UNKNOWN
    release_error = release == COLORED_UNKNOWN
    section_error = name_error or version_error or release_error

    manager.add_section("os", error=section_error)
    manager.add_key_value("name", name, error=name_error, tabs_after_key=2)
    manager.add_key_value("version", version, error=version_error)
    manager.add_key_value("release", release, error=release_error)


def k3s_section(manager: HeaderManager, info: HostInfo) -> None:
    hostname = info.k3s_hostname
    version = InstallationLogger.get_log_k3s_version()

    name_error = False
    if hostname == UNKNOWN:
        name_error = True
        hostname = COLORED_UNKNOWN
    elif hostname == FAILED:
        name_error = True

    version_error = version == UNKNOWN
    section_error = name_error or version_error

    manager.add_section("K3S", error=section_error)
    manager.add_key_value("hostname", hostname, error=name_error)
    manager.add_key_value("version", version, error=version_error)


def log_full_host_header() -> None:
    info = get_host_info()
    manager = HeaderManager()

    manager.add_line(logging.INFO)
    user_cli_command_header(manager, info)
    manager.add_line()
    host_section(manager, info)
    hardware_section(manager, info)
    os_section(manager, info)
    python_section(manager, info)
    k3s_section(manager, info)

    manager.add_line(logging.INFO)
    manager.flush()
