from dataclasses import dataclass
from dataclasses import field
import logging
import subprocess
import time
from typing import Any
from typing import Dict
from typing import Type

from mlopscli.utils.port_forward_thread import PORT_FORWARD_START_WAIT_TIME
from mlopscli.utils.port_forward_thread import PORT_FORWARD_TYPE_POD
from mlopscli.utils.port_forward_thread import PORT_FORWARD_TYPE_SERVICE
from mlopscli.utils.port_forward_thread import PortForwardThread


class ServiceResolutionError(Exception):
    """Raised when service resolution fails."""

    pass


class PortForwardError(Exception):
    """Raised when port forwarding operations fail."""

    pass


# Configuration constants for robustness
MAX_RETRY_ATTEMPTS = 3
RETRY_BACKOFF_FACTOR = 2
PORT_FORWARD_TIMEOUT = 30
HEALTH_CHECK_TIMEOUT = 10
KUBECTL_TIMEOUT = 15


@dataclass
class ServiceConfig:
    service_name: str
    local_port: int
    remote_port: int
    namespace: str
    client_cls: Type[Any]
    client_kwargs: Dict[str, Any] = field(default_factory=dict)


class InstallationResolver:
    """
    Resolves service endpoints and port-forwarding based on installation location.
    Use this to instantiate service clients (e.g., Keycloak, LakeFS, etc) in a location-agnostic way.
    Provides registration, start, and stop methods for service lifecycle management.
    """

    def __init__(self, logger: logging.Logger) -> None:
        self._logger = logger
        self._service_registry = {}
        self._port_forward_threads = {}

    def register_service(
        self,
        config: ServiceConfig,
    ) -> None:
        """
        Register a service for later resolution and management.

        Example::

            config = ServiceConfig(
                service_name=...,
                local_port=...,
                remote_port=...,
                namespace=...,
                client_cls=...,
                client_kwargs={...}
            )
            resolver.register_service(config)
        """
        self._service_registry[config.service_name] = config

    def start_service(self, service_name: str) -> Type[Any]:
        """Start the service (including port-forward if remote) and return the client instance."""
        if service_name not in self._service_registry:
            raise ValueError(f"Service '{service_name}' is not registered.")
        config = self._service_registry[service_name]
        return self._resolve_service(config)

    def stop_service(self, service_name: str) -> None:
        """Stop the port-forward thread for the service if running."""
        pf_thread = self._port_forward_threads.get(service_name)
        if pf_thread and pf_thread.is_alive():
            pf_thread.stop()  # Signal the thread to stop
            pf_thread.join(timeout=5)
            self._logger.info(f"[Resolver] Port-forward for {service_name} stopped.")
        self._port_forward_threads.pop(service_name, None)

    def _verify_service_exists(self, service_name: str, namespace: str) -> bool:
        """Verify that a service exists in the specified namespace. Returns True if exists, False otherwise."""
        try:
            result = subprocess.run(["kubectl", "get", "svc", service_name, "-n", namespace], capture_output=True, text=True, timeout=10)  # noqa: S603, S607
            return result.returncode == 0
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            return False

    def _resolve_service(
        self,
        config: ServiceConfig,
    ) -> Type[Any]:
        """
        Returns a client instance for the given service, port-forwarding if remote.
        """
        try:
            self._logger.info(f"[Resolver] Starting port-forward for {config.service_name}...")

            if not self._verify_service_exists(config.service_name, config.namespace):
                self._logger.warning(
                    f"[Resolver] Service {config.service_name} not found in namespace {config.namespace}. Falling back to pod forwarding."
                )
                pf_thread = PortForwardThread(
                    type=PORT_FORWARD_TYPE_POD,
                    name=f"{config.service_name}-0",
                    namespace=config.namespace,
                    local_port=config.local_port,
                    remote_port=config.remote_port,
                )
            else:
                pf_thread = PortForwardThread(
                    type=PORT_FORWARD_TYPE_SERVICE,
                    name=config.service_name,
                    namespace=config.namespace,
                    local_port=config.local_port,
                    remote_port=config.remote_port,
                )

            pf_thread.start()
            self._port_forward_threads[config.service_name] = pf_thread
            self._logger.info(f"[Resolver] Awaiting port forwarding for {config.service_name}...")
            time.sleep(PORT_FORWARD_START_WAIT_TIME)
            if not pf_thread.is_alive():
                self._port_forward_threads.pop(config.service_name, None)
                self._logger.warning(f"[Resolver] Port forward for {config.service_name} failed or already running.")
            else:
                self._logger.info(f"[Resolver] Port forwarding successfully established for {config.service_name}")
            # Create and return client
            try:
                self._logger.info(f"[Resolver] Creating client for {config.service_name} at localhost:{config.local_port}")
                return config.client_cls(**config.client_kwargs)
            except Exception as e:
                self.stop_service(config.service_name)  # Ensure cleanup on error
                raise ServiceResolutionError(f"Failed to create client for {config.service_name}: {e}") from e
        except Exception as e:
            self.stop_service(config.service_name)  # Ensure cleanup on error
            self._logger.error(f"[Resolver] Error resolving service {config.service_name}: {str(e)}")
            raise ServiceResolutionError(f"Failed to resolve service {config.service_name}") from e
