"""Cli options."""

from typing import Callable
from typing import List

import click

DRYRUN = "dryrun"
DRY_RUN_OPTION = [
    click.option(
        f"--{DRYRUN}",
        "-d",
        envvar=f"{DRYRUN.upper()}",
        help="Run in dryrun mode",
        type=bool,
        default=False,
        required=False,
        is_flag=True,
        show_default=True,
    ),
]

ARTIFACTORY_USERNAME = "artifactory_username"

ARTIFACTORY_USERNAME_OPTION = [
    click.option(
        f"--{ARTIFACTORY_USERNAME}",
        "-au",
        envvar=f"{ARTIFACTORY_USERNAME.upper()}",
        help="Artifactory username",
        type=str,
        required=True,
    )
]

ARTIFACTORY_TOKEN = "artifactory_token"  # noqa: S105
ARTIFACTORY_TOKEN_OPTION = [
    click.option(
        f"--{ARTIFACTORY_TOKEN}",
        "-at",
        envvar=f"{ARTIFACTORY_TOKEN.upper()}",
        help="Artifactory token",
        type=str,
        required=True,
    )
]


def add_options(options: List[Callable]) -> Callable:
    """Add common options."""

    def _add_options(func: Callable) -> Callable:
        for option in reversed(options):
            func = option(func)
        return func

    return _add_options
