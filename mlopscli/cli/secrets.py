from datetime import datetime
from datetime import timezone
import getpass
import os
import stat

import click

from mlopscli.secrets.keyring_manager import KeyringManager

AUDIT_LOG_PATH = os.path.expanduser("~/.cache/mlopscli/kast_installation/secret_audit.log")
SENSITIVE_COMPONENTS = {"keycloak", "postgres", "minio", "grafana", "argo", "clickhouse", "zot", "artifactory"}


def audit_log(component: str, user: str, action: str) -> None:
    os.makedirs(os.path.dirname(AUDIT_LOG_PATH), exist_ok=True)
    if not os.path.exists(AUDIT_LOG_PATH):
        with open(AUDIT_LOG_PATH, "a") as f:
            os.chmod(AUDIT_LOG_PATH, stat.S_IRUSR | stat.S_IWUSR)
    username = getpass.getuser()
    timestamp = datetime.now(timezone.utc).isoformat() + "Z"
    with open(AUDIT_LOG_PATH, "a") as f:
        f.write(f"{timestamp} | {action} | {component}/{user} | by {username}\n")


@click.group()
def secrets() -> None:
    """Manage and retrieve component secrets."""
    pass


@secrets.command()
@click.option("--component", required=True, help="Component name (e.g., minio, postgres, grafana)")
@click.option("--user", required=True, help="Username for the component")
def show(component: str, user: str) -> None:
    """Show the stored secret for a component/user."""
    keyring_manager = KeyringManager()
    secret = keyring_manager.get_secret(component, user)
    if component.lower() in SENSITIVE_COMPONENTS:
        audit_log(component, user, action="RETRIEVE")
    if secret:
        click.echo(f"[{component}/{user}] secret: {secret}")
    else:
        click.echo(f"No secret found for {component}/{user}.")
