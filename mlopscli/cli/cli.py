"""cli"""

import importlib.metadata
import sys
from types import TracebackType

import click

from mlopscli.cli import cli_manage_utilities
from mlopscli.cli.cli_manage_utilities import package_logs
from mlopscli.cli.secrets import secrets
from mlopscli.dependency_injection.containers import Application
from mlopscli.k3s import cli_manage_k3s
from mlopscli.k3s.cli_manage_k3s import install_k3s
from mlopscli.k3s.cli_manage_k3s import uninstall_k3s
from mlopscli.kast import cli_manage_kast
from mlopscli.kast.cli_manage_kast import install_kast
from mlopscli.kast.cli_manage_kast import uninstall_kast
from mlopscli.utils.exceptions import UnsupportedArchitectureException
from mlopscli.utils.logger import get_logger

logger = get_logger("cli")


@click.group("cli")
@click.version_option(importlib.metadata.version("cortaix-factory-mlops-mlopscli"))
def cli() -> None:
    """mlopscli cli entrypoint."""


cli.add_command(secrets)

cli_commands = [
    (cli_manage_k3s, [install_k3s, uninstall_k3s]),
    (cli_manage_kast, [install_kast, uninstall_kast]),
    (cli_manage_utilities, [package_logs]),
]

for cli_group, commands in cli_commands:
    group = cli_group.click_group
    for command in commands:
        group.add_command(command)
    cli.add_command(group)


# CodeReview: Is this realy used ?
def wire_application() -> Application:
    """Wire the dependency injection container."""
    container = Application()
    container.wire(modules=[sys.modules[__name__]] + [module for module, _ in cli_commands])
    return container


def start() -> None:
    """Script entrypoint."""
    wire_application()
    cli()


def unhandled_exception_catcher(type: type[BaseException], value: BaseException, full_traceback: TracebackType | None) -> None:
    if type == UnsupportedArchitectureException:
        logger.critical("Your architecture is not supported!", exc_info=(type, value, None))
    else:
        logger.critical("UNHANDLED EXCEPTION", exc_info=(type, value, full_traceback))


sys.excepthook = unhandled_exception_catcher
