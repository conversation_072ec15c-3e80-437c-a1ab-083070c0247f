import os

import click

from mlopscli.secrets.keyring_manager import KeyringManager


@click.group()
def sudo() -> None:
    """Manage sudo password in system keyring."""
    pass


@sudo.command()
def set_password() -> None:
    """Store sudo password securely in keyring."""
    user = os.getlogin()
    km = KeyringManager()
    km.prompt_and_set_secret("sudo", user)
    click.echo("Sudo password stored securely in keyring.")


@sudo.command()
def remove_password() -> None:
    """Remove sudo password from keyring."""
    user = os.getlogin()
    km = KeyringManager()
    km.remove_secret("sudo", user)
    click.echo("Sudo password removed from keyring.")


@sudo.command()
def show_password() -> None:
    """Show (retrieve) the sudo password from keyring (for debug only)."""
    user = os.getlogin()
    km = KeyringManager()
    password = km.get_secret("sudo", user)
    if password:
        click.echo(f"Sudo password for {user}: {password}")
    else:
        click.echo("No sudo password found in keyring.")
