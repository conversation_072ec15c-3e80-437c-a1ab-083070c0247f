import os

import yaml

from mlopscli.kast.manage_installation_base import ManageInstallationBaseClass
from mlopscli.utils.constants import KAST_WORKING_DIR
from mlopscli.utils.kubernetes import apply_k8s_component
from mlopscli.utils.kubernetes import create_certificate
from mlopscli.utils.kubernetes import is_component_installed
from mlopscli.utils.kubernetes import is_helm_chart_installed
from mlopscli.utils.kubernetes import is_helm_repo_installed
from mlopscli.utils.kubernetes import uninstall_helm_chart
from mlopscli.utils.kubernetes import uninstall_helm_repo
from mlopscli.utils.kubernetes import uninstall_k8s_component
from mlopscli.utils.kubernetes import wait_for_pod_with_prefix_to_run
from mlopscli.utils.system import run_command
from mlopscli.utils.system import run_command_with_output

OPENTELEMETRY_OPERATOR_NAMESPACE = "opentelemetry-operator-system"
OTEL_HELM_REPO_NAME = "open-telemetry"
OTEL_CHART_VERSION = "0.126.0"
OTEL_HELM_CHART_NAME = "otel-collector"
OTEL_SERVICE_NAME = "otel-collector-opentelemetry-collector"
OTEL_TLS_SECRET_NAME = "opentelemetry-tls"  # noqa: S105
OTEL_DEPLOYMENT_YAML_VALUES = {
    "service": {"enabled": True},
    "mode": "daemonset",
    "presets": {"logsCollection": {"enabled": True}, "kubernetesAttributes": {"enabled": True}},
    "image": {"repository": "otel/opentelemetry-collector-contrib", "tag": "0.127.0"},
    "resources": {"limits": {"memory": "512Mi", "cpu": "500m"}, "requests": {"memory": "256Mi", "cpu": "250m"}},
    "config": {
        "receivers": {
            "otlp": {
                "protocols": {
                    "grpc": {
                        "endpoint": "0.0.0.0:4317",
                        "tls": {
                            "ca_file": "/etc/ssl/certs/ca.crt",
                            "cert_file": "/etc/ssl/certs/tls.crt",
                            "key_file": "/etc/ssl/certs/tls.key",
                        },
                    },
                    "http": {
                        "endpoint": "0.0.0.0:4318",
                        "tls": {
                            "ca_file": "/etc/ssl/certs/ca.crt",
                            "cert_file": "/etc/ssl/certs/tls.crt",
                            "key_file": "/etc/ssl/certs/tls.key",
                        },
                    },
                },
            },
            "filelog": {
                "include": ["/var/log/pods/**/*.log"],
                "exclude": [
                    "/var/log/pods/olap-store_otel-collector-opentelemetry-collector*_*/opentelemetry-collector/*.log",
                    "/var/log/pods/kube-system*_*/opentelemetry-collector/*.log",
                ],
                "operators": [
                    {"id": "container-parser", "type": "container", "max_log_size": 102400},
                    {
                        "id": "json-log-parse",
                        "type": "json_parser",
                        "if": 'body matches "^{.*}$"',
                        "from": "container-parser",
                        "trace": {"span_id": {"parse_from": "attributes.span_id"}, "trace_id": {"parse_from": "attributes.trace_id"}},
                        "severity": {"parse_from": "attributes.level"},
                    },
                ],
            },
        },
        "exporters": {
            "clickhouse": {
                "logs_table_name": "mlobs_otel_logs",
                "traces_table_name": "mlobs_otel_traces",
                "timeout": "5s",
                "retry_on_failure": {"enabled": True, "initial_interval": "5s", "max_interval": "30s", "max_elapsed_time": "300s"},
            }
        },
        "processors": {
            "batch": {"timeout": "5s", "send_batch_size": 100000},
            "k8sattributes": {
                "extract": {
                    "metadata": [
                        "k8s.namespace.name",
                        "k8s.deployment.name",
                        "k8s.statefulset.name",
                        "k8s.daemonset.name",
                        "k8s.node.name",
                        "k8s.pod.name",
                        "k8s.pod.uid",
                        "k8s.pod.start_time",
                        "k8s.cronjob.name",
                        "k8s.job.name",
                        "service.name",
                        "service.namespace",
                        "service.version",
                        "container.image.name",
                        "container.image.tag",
                    ]
                }
            },
        },
        "service": {
            "pipelines": {
                "logs": {"receivers": ["filelog"], "processors": ["batch", "k8sattributes"], "exporters": ["clickhouse"]},
                "traces": {"receivers": ["otlp"], "processors": ["batch"], "exporters": ["clickhouse"]},
                "metrics": {"receivers": ["otlp"], "processors": ["batch"], "exporters": ["clickhouse"]},
            }
        },
    },
    "extraVolumeMounts": [{"mountPath": "/etc/ssl/certs/", "name": "tls-certs", "readOnly": True}],
    "extraVolumes": [
        {
            "name": "tls-certs",
            "secret": {
                "secretName": OTEL_TLS_SECRET_NAME,
            },
        }
    ],
}


class OTel(ManageInstallationBaseClass):
    def __init__(self, namespace: str) -> None:
        super().__init__(logger_name=__name__, namespace=namespace)

    def _add_repo(self) -> None:
        repo_url = "https://open-telemetry.github.io/opentelemetry-helm-charts"
        if is_helm_repo_installed(OTEL_HELM_REPO_NAME):
            self._logger.info(f"{OTEL_HELM_REPO_NAME} helm repo already installed...")
        else:
            self._logger.info(f"Adding helm repo ({OTEL_HELM_REPO_NAME}): {repo_url}")
            try:
                run_command_with_output(f"helm repo add {OTEL_HELM_REPO_NAME} {repo_url}")
                self._logger.info(f"Successfully added helm repo ({OTEL_HELM_REPO_NAME})")
            except Exception as e:
                self._logger.error(f"Failed to add helm repo ({OTEL_HELM_REPO_NAME}): {e}")
                raise

    def install(
        self,
        clickhouse_namespace: str,
        clickhouse_service_name: str,
        clickhouse_database: str,
        clickhouse_username: str,
        clickhouse_password: str,
        kserve_inference_namespace: str,
    ) -> None:
        self._logger.info("Starting installing otel...")

        self._add_repo()

        if not is_component_installed(type="namespace", name=self._namespace):
            self._logger.info(f"Creating {self._namespace} namespace...")
            run_command(f"kubectl create namespace {self._namespace}")
            self._logger.info(f"Successfully created {self._namespace} namespace!")
        else:
            self._logger.info(f"{self._namespace} namespace already exists...")

        create_certificate(
            name=OTEL_TLS_SECRET_NAME,
            namespace=self._namespace,
            dns_names=["localhost", f"{OTEL_SERVICE_NAME}.{self._namespace}", f"{OTEL_SERVICE_NAME}.{self._namespace}.svc.cluster.local"],
            yaml_file_path=KAST_WORKING_DIR,
        )

        if is_helm_chart_installed(OTEL_HELM_CHART_NAME):
            self._logger.info(f"Helm chart ({OTEL_HELM_CHART_NAME}) is already deployed...")
        else:
            self._logger.info("Creating temporary configuration values file...")

            otel_values = OTEL_DEPLOYMENT_YAML_VALUES
            otel_values["config"]["exporters"]["clickhouse"]["endpoint"] = (
                f"tcp://{clickhouse_service_name}.{clickhouse_namespace}:9000?dial_timeout=10s"
            )
            otel_values["config"]["exporters"]["clickhouse"]["database"] = clickhouse_database
            otel_values["config"]["exporters"]["clickhouse"]["username"] = clickhouse_username
            otel_values["config"]["exporters"]["clickhouse"]["password"] = clickhouse_password
            yaml_file_path = os.path.join(KAST_WORKING_DIR, "otel-collector-values.yaml")

            with open(yaml_file_path, "w") as file:
                self._logger.debug("writing otel yaml values...")
                yaml.dump(otel_values, file, default_flow_style=False)
                file.close()

            self._logger.info(f"Deploying otel helm chart: ({OTEL_HELM_CHART_NAME})")
            command = f"helm install -f {yaml_file_path} {OTEL_HELM_CHART_NAME} open-telemetry/opentelemetry-collector \
                      --version {OTEL_CHART_VERSION} -n {self._namespace}"
            self._logger.debug(f"Running command ({command})")
            run_command(command)

            resource_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "yaml", "otel", "otel-collector-crd.yaml")
            self._logger.info("Deploying otel collector CRD ...")
            apply_k8s_component(resource_file)

            wait_for_pod_with_prefix_to_run(namespace=OPENTELEMETRY_OPERATOR_NAMESPACE, pod_name_prefix="opentelemetry-operator-controller-manager")

            resource_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "yaml", "otel", "otel-collector-sidecar.yaml")
            self._logger.info("Deploying otel collector sidecar ...")
            otel_collector_sidecar_file = os.path.join(KAST_WORKING_DIR, "otel-collector-sidecar.yaml")
            with open(resource_file, "r") as file:
                file_content = file.read()
                file_content = file_content.replace("OTEL_SERVICE_NAME", OTEL_SERVICE_NAME)
                file_content = file_content.replace("OTEL_NAMESPACE", self._namespace)

                with open(otel_collector_sidecar_file, "w") as file:
                    self._logger.debug(f"writing otel-collector-sidecar file ({otel_collector_sidecar_file})")
                    file.write(file_content)
                    file.close()
            apply_k8s_component(otel_collector_sidecar_file, kserve_inference_namespace)

            self._logger.info("Successfully deployed otel-collector!")

        self._logger.info("Successfully installed otel")

    def uninstall(self, delete_collector_namespace: bool, delete_operator_namespace: bool) -> None:
        self._logger.info("Uninstalling otel-collector...")
        uninstall_helm_chart(OTEL_HELM_CHART_NAME, self._namespace)
        uninstall_helm_repo(OTEL_HELM_REPO_NAME)

        if delete_collector_namespace:
            uninstall_k8s_component(type="namespace", name=self._namespace)
            self._logger.info("otel-collector namespace cleanup completed")
        if delete_operator_namespace:
            uninstall_k8s_component(type="namespace", name=OPENTELEMETRY_OPERATOR_NAMESPACE)
            self._logger.info("otel-operator namespace cleanup completed")
        self._logger.info("otel-collector uninstall completed")
