import asyncio
import os
from pathlib import Path
import shutil
import subprocess
import time
from typing import Op<PERSON>
from typing import <PERSON>ple

import yaml

from mlopscli.kast.keycloak import Keycloak
from mlopscli.kast.manage_clickhouse_installation import CLICKHOUSE_HELM_CHART_NAME
from mlopscli.kast.manage_clickhouse_installation import <PERSON><PERSON><PERSON>ouse
from mlopscli.kast.manage_gitlab_runner_installation import GIT<PERSON>B_RUNNER_NAMESPACE
from mlopscli.kast.manage_gitlab_runner_installation import Gitlab<PERSON>unner
from mlopscli.kast.manage_installation_base import CA_CERTIFICATES_CRT
from mlopscli.kast.manage_installation_base import KASTCTL_INSTALL_COMMAND
from mlopscli.kast.manage_installation_base import KASTCTL_UNINSTALL_COMMAND
from mlopscli.kast.manage_installation_base import ManageInstallationBaseClass
from mlopscli.kast.manage_lakefs_installation import LAKEFS_FQDN
from mlopscli.kast.manage_lakefs_installation import <PERSON><PERSON>
from mlopscli.kast.manage_otel_installation import OTel
from mlopscli.security.credentials_manager import SecretManager
from mlopscli.utils.constants import ARCH_AMD64
from mlopscli.utils.constants import ARCH_ARM64
from mlopscli.utils.constants import ARTIFACTORY_REGISTRY_URL
from mlopscli.utils.constants import ARTIFACTORY_SECRET_NAME
from mlopscli.utils.constants import CA_CERT_DIR
from mlopscli.utils.constants import INSTALLATION_LOCATION_LOCAL_STR
from mlopscli.utils.constants import INSTALLATION_LOCATION_REMOTE_STR
from mlopscli.utils.constants import INSTALLATION_LOCATIONS
from mlopscli.utils.constants import KAST_WORKING_DIR
from mlopscli.utils.constants import TDS_PROJECT_DOMAIN_NAME
from mlopscli.utils.constants import TMP_WORKING_DIR
from mlopscli.utils.exceptions import SecretCreationException
from mlopscli.utils.exceptions import UnsupportedArchitectureException
from mlopscli.utils.exceptions import UnsupportedInstallationLocationException
from mlopscli.utils.exceptions import UnsupportedWorkloadTypeException
from mlopscli.utils.installation_ingress_resolver import create_service_ingress_config, get_keycloak_ingress_config, get_kserve_ingress_config, get_mlflow_ingress_config, to_yaml
from mlopscli.utils.installation_ingress_resolver import get_argo_ingress_config
from mlopscli.utils.installation_ingress_resolver import get_grafana_ingress_config
from mlopscli.utils.installation_ingress_resolver import get_minio_dual_ingress_config
from mlopscli.utils.installation_ingress_resolver import get_polycore_ingress_config
from mlopscli.utils.installation_ingress_resolver import get_zot_ingress_config
from mlopscli.utils.installation_resolver import InstallationResolver
from mlopscli.utils.installation_resolver import ServiceConfig
from mlopscli.utils.kubernetes import apply_k8s_component
from mlopscli.utils.kubernetes import check_image_pull_secret
from mlopscli.utils.kubernetes import configure_storage_class
from mlopscli.utils.kubernetes import create_certificate
from mlopscli.utils.kubernetes import create_docker_registry_secret
from mlopscli.utils.kubernetes import create_secret
from mlopscli.utils.kubernetes import force_download_image_on_host
from mlopscli.utils.kubernetes import is_component_installed
from mlopscli.utils.kubernetes import is_helm_chart_installed
from mlopscli.utils.kubernetes import is_helm_repo_installed
from mlopscli.utils.kubernetes import restart_deployment
from mlopscli.utils.kubernetes import scale_deployment_down_and_up
from mlopscli.utils.kubernetes import scale_statefulset_down_and_up
from mlopscli.utils.kubernetes import uninstall_helm_chart
from mlopscli.utils.kubernetes import uninstall_helm_repo
from mlopscli.utils.kubernetes import uninstall_k8s_component
from mlopscli.utils.kubernetes import wait_for_pod_to_run
from mlopscli.utils.kubernetes import wait_for_pod_with_prefix_to_run
from mlopscli.utils.logger import LogColors
from mlopscli.utils.manage_base_class import ManageBaseClass
from mlopscli.utils.minio import create_s3_bucket
from mlopscli.utils.port_forward_thread import PORT_FORWARD_START_WAIT_TIME
from mlopscli.utils.port_forward_thread import PORT_FORWARD_TYPE_POD
from mlopscli.utils.port_forward_thread import PortForwardThread
from mlopscli.utils.postgres import create_postgres_database, create_postgres_database_without_port_forwarding
from mlopscli.utils.system import arch_supported
from mlopscli.utils.system import docker_login
from mlopscli.utils.system import get_arch
from mlopscli.utils.system import is_command_available
from mlopscli.utils.system import log_etc_host_file
from mlopscli.utils.system import run_command
from mlopscli.utils.system import run_command_with_output
from mlopscli.utils.system import run_sudo_command_with_output
from mlopscli.utils.installation_ingress_resolver import configure_ingress_class

CONTEXT_NAME = "k3s"
S3_NAMESPACE = "object-store"
CLICKHOUSE_NAMESPACE = "olap-store"
OTEL_NAMESPACE = CLICKHOUSE_NAMESPACE
CLICKHOUSE_DATABASE = "mlops_otel"
ARGO_WORKING_NAMESPACE = "default"
ARGO_S3_SECRET_NAME = "argo-s3"  # noqa: S105
ARGO_INTERMEDIATE_CA_SECRET_NAME = "intermediate-ca"  # noqa: S105
ARGO_LAKEFS_SECRET_NAME = "lakefs-secret"  # noqa: S105
S3_SERVICE_NAME = "s3"
S3_ACCESS_KEY = "minio"
DEFAULT_K3S_USER = "ubuntu"
ARGO_VERSION = "v3.4.11"
ARGO_COMPONENT = "processing"
CERT_MANAGER_COMPONENT = "cert-manager"
CERT_MANAGER_HELM_REPO = "jetstack"
MONITORING_NAMESPACE = "monitoring"
INGRESS_NAMESPACE = "perimeter"
KAST_DIR = "kast-6.5.0"
KAST_DATA_DIR = "kast_data-6.5.0"
ZOT_DPSC = "zot.dpsc"
CONFIG_JSON = "config.json"
KPACK_YAML = "kpack.yaml"
CERTS_MOUNT_PATH = "/etc/ssl/certs"
FULL_CA_CERT_PATH = f"{CERTS_MOUNT_PATH}/{CA_CERTIFICATES_CRT}"
ZOT_COMPONENT = "zot"
ZOT_HELM_CHART_NAME = "my-zot"
ZOT_HELM_CHART_URL = "https://zotregistry.dev/helm-charts/"
ZOT_CHART_VERSION = "0.1.66"
POLYCORE_COMPONENT = "polycore"
POLYCORE_HELM_CHART_NAME = "my-polycore"
POLYCORE_HELM_CHART_URL = "https://artifactory.thalesdigital.io/artifactory/api/helm/helm/"
POLYCORE_CHART_VERSION = "1.1.1-develop"
DAGGER_COMPONENT = "dagger"
DAGGER_HELM_CHART_NAME = "my-dagger"
DAGGER_CHART_VERSION = "1.1.1-develop"
MLOPS_TOOLCHAIN_GENERIC_REPO_URL = "https://artifactory.thalesdigital.io/artifactory/generic-internal/mlops-toolchain"
TRUST_MANAGER_HELM_CHART_NAME = "trust-manager"
TRUST_MANAGER_CHART_VERSION = "v0.17.1"
STORAGECLASS_USER_ID = 1001
DPSC_DOMAIN_NAME = "dpsc"
MLOPS_TOOLCHAIN_NAMESPACE = "mlops-toolchain"

DAGGER_DEPLOYMENT_YAML_VALUES = {
    "engine": {
        "kind": "DaemonSet",
        "image": {
            "ref": "registry.dagger.io/engine",
            "tag": "v0.16.3",  # Use specific version, not 'latest'
            "pullPolicy": "IfNotPresent",
        },
        "resources": {"limits": {"cpu": "2", "memory": "4Gi"}, "requests": {"cpu": "500m", "memory": "1Gi"}},
        "terminationGracePeriodSeconds": 60,
        "tolerations": [{"key": "node-role.kubernetes.io/control-plane", "operator": "Exists", "effect": "NoSchedule"}],
        # Enhanced persistence configuration
        "persistence": {
            "enabled": True,
            "buildArtifacts": {
                "enabled": True,
                "accessModes": ["ReadWriteOnce"],
                "size": "20Gi",  # Specify size - will be updated dynamically
                "storageClassName": "default",  # Will be updated with configure_storage_class
            },
        },
        "imagePullSecrets": [{"name": f"{ARTIFACTORY_SECRET_NAME}"}],
        # Enhanced probe settings for better reliability
        "readinessProbeSettings": {"initialDelaySeconds": 10, "periodSeconds": 10, "timeoutSeconds": 5, "failureThreshold": 3},
        "livenessProbeSettings": {"initialDelaySeconds": 30, "periodSeconds": 30, "timeoutSeconds": 10, "failureThreshold": 3},
    },
    # Use official Dagger certificate handling instead of extraVolumes
    "certificates": {
        "customCA": {
            "enabled": True,
            "sourceType": "configMap",  # or "secret"
            "configMapName": "mlops-ca-bundle",  # Your existing ConfigMap
            "updateCAStore": True,  # Official Dagger CA trust store integration
        }
    },
    # Service configuration
    "service": {"type": "ClusterIP", "port": 12345, "targetPort": 12345, "protocol": "TCP"},
    # Monitoring configuration
    "monitoring": {
        "enabled": False  # Enable for production environments
    },
}


HOST_FILE_TEMPLATE = {
    "all": {
        "children": {},
        "vars": {"ansible_connection": "local"},
    }
}

POLYCORE_DEPLOYMENT_YAML_VALUES = {
    "config": {
        "enable_reflection": True,
        "certificates": {"ca_path": FULL_CA_CERT_PATH, "certificate_paths": {}, "insecure_skip_verify": False},
        "registry": {"external_url": ZOT_DPSC, "internal_url": f"{ZOT_HELM_CHART_NAME}.{ZOT_COMPONENT}.svc.cluster.local:5000", "insecure": False},
        "dagger_engine": {
            "url": "tcp://my-dagger-polysystem-dagger-engine-engine.dagger.svc.cluster.local:12345",
            "insecure": False,
            "timeout": "30s",
        },
        "executor": {
            "type": "argo",
            "argo_server": f"http://argo-processing-server.{ARGO_COMPONENT}.svc.cluster.local:2746",
            "argo_namespace": ARGO_WORKING_NAMESPACE,
            "service_account": "workflow-executor",
            "certificates": {"ca_path": FULL_CA_CERT_PATH, "certificate_paths": {}, "insecure_skip_verify": False},
        },
        "telemetry": {"enabled": True},
        "model_registry": {
            "type": "mlflow",
            "server_host": "http://mlflow.processing.svc.cluster.local:5000",
            "timeout": "10s",
            "certificates": {"ca_path": FULL_CA_CERT_PATH, "certificate_paths": {}, "insecure_skip_verify": False},
        },
        "model_packager": {
            "type": "kitops",
            "builder_options": {
                "type": "dagger",
                "base_image": "ghcr.io/kitops-ml/kitops",
                "base_image_version": "v1.4.0",
                "registry": "my-zot.zot.svc.cluster.local:5000",
                "insecure": False,
                "timeout": 2 * 60 * **********,  # 2 minutes
            },
        },
        "model_scanner": {
            "type": "modelscan",
            "builder_options": {
                "type": "dagger",
                "base_image": "artifactory.thalesdigital.io/docker-internal/mlops-toolchain/modelscan",
                "base_image_version": "0.8.5-2",
                "timeout": 2 * 60 * **********,  # 2 minutes
                "registry": {
                    "url": "",
                    "secret_name": "artifactory_token",
                    "username": "",
                    "password": "",
                },
            },
        },
        "observability": {
            "enabled": True,
            "db": {
                "type": "clickhouse",
            },
        },
    },
    "ingress": "PLACEHOLDER_FOR_DYNAMIC_GENERATION",
    "extraVolumeMounts": [
        {"mountPath": CERTS_MOUNT_PATH, "name": "ca-certificate-only", "readOnly": True},
    ],
    "extraVolumes": [
        {
            "name": "ca-certificate-only",
            "configMap": {
                "name": "mlops-ca-bundle",
                "defaultMode": int("0644", 8),
                "optional": False,
                "items": [{"key": CA_CERTIFICATES_CRT, "path": CA_CERTIFICATES_CRT}],
            },
        }
    ],
}


def get_zot_deployment_values(ingress_class: str) -> dict:
    """Generate ZOT deployment values with ingress controller specific annotations."""
    # Create ingress configuration using the unified function
    ingress_config = create_service_ingress_config(
        service_name="zot",
        ingress_class=ingress_class,
        hosts=[ZOT_DPSC],
        tls_secret="zot-ingress-certs",  # noqa: S106
        backend_protocol="HTTPS",
        enable_tls_passthrough=True,
        proxy_body_size="0",
    )

    return {
        "service": {"type": "ClusterIP"},
        "ingress": ingress_config,
        "mountConfig": True,
        "configFiles": {
            CONFIG_JSON: """
        {
            "storage": {
                "rootDirectory": "/var/lib/registry",
                "dedupe": false,
                "storageDriver": {
                    "name": "s3",
                    "bucket": "zot",
                    "region": "us-east-1",
                    "regionendpoint": "https://s3.object-store:9000",
                    "secure": true,
                    "accesskey": "minio",
                    "secretkey": "<S3_SECRET_REPLACE>",
                    "forcepathstyle": true
                }
            },
            "http": {
                "address": "0.0.0.0",
                "port": 5000,
                "tls": {
                    "cert": "/etc/certs/tls.crt",
                    "key": "/etc/certs/tls.key"
                }
            },
            "log": {
                "level": "debug",
                "output": "/dev/stdout"
            },
            "extensions": {
                "search": {
                    "enable": true
                },
                "ui": {
                    "enable": true
                }
            }
        }
        """
        },
        "extraVolumeMounts": [
            {"mountPath": CERTS_MOUNT_PATH, "name": "ca-certificate-only", "readOnly": True},
            {"name": "certs-volume", "mountPath": "/etc/certs"},
        ],
        "extraVolumes": [
            {
                "name": "ca-certificate-only",
                "configMap": {
                    "name": "mlops-ca-bundle",
                    "defaultMode": int("0644", 8),
                    "optional": False,
                    "items": [{"key": CA_CERTIFICATES_CRT, "path": CA_CERTIFICATES_CRT}],
                },
            },
            {"name": "certs-volume", "secret": {"secretName": "zot-internal-tls"}},
        ],
        "startupProbe": {
            "initialDelaySeconds": 5,
            "periodSeconds": 30,
            "failureThreshold": 3,
        },
        "httpGet": {"scheme": "HTTPS"},
        "persistence": True,
        "pvc": {"create": True},
    }


POLYDB_POSTGRESQL_SERVICE_NAME = "kast-default-postgresql"
POLYDB_POSTGRESQL_POD_NAME = "kast-default-postgresql-0"
POSTGRESQL_NAMESPACE = "sql-store"
POSTGRESQL_USERNAME = "postgres"
POLYDB_POSTGRESQL_HOST = "localhost"
POSTGRESQL_PORT = 5432
POLYDB_DB_NAME = "polydb"
POLYDB_VERSION = "develop-acdedc2ac3b94b78ee4565fbe4e1d982c1775335"

WORKLOAD_TYPE_DEPLOYMENT_RESTART = "WORKLOAD_DEPLOYMENT_RESTART"
WORKLOAD_TYPE_DEPLOYMENT_SCALE = "WORKLOAD_DEPLOYMENT_SCALE"
WORKLOAD_TYPE_STATEFULSET_SCALE = "WORKLOAD_STATEFULSET_SCALE"
WORKLOAD_TYPES = [WORKLOAD_TYPE_DEPLOYMENT_RESTART, WORKLOAD_TYPE_DEPLOYMENT_SCALE, WORKLOAD_TYPE_STATEFULSET_SCALE]
KSERVE_COMPONENT = "kserve"
KSERVE_INFERENCE = "kserve-inference"

KEYCLOAK_NAMESPACE = "authentication"
KEYCLOAK_SERVICE_NAME = "keycloak"


class ManageKastInstallation(ManageBaseClass, ManageInstallationBaseClass):
    def __init__(
        self,
    ) -> None:
        super().__init__(__name__)
        self._otel = OTel(CLICKHOUSE_NAMESPACE)
        self._clickhouse = ClickHouse(CLICKHOUSE_NAMESPACE)
        self._lakefs = LakeFS(headless=True)
        self._gitlab_runner = GitlabRunner(GITLAB_RUNNER_NAMESPACE)
        self._postgres_secret = SecretManager("postgres", user="postgres")
        self._keycloack_secret = SecretManager("keycloak", user="admin")
        self._minio_secret = SecretManager("minio", user="minio")
        self._all_keyring_secrets_list = [self._postgres_secret, self._keycloack_secret, self._minio_secret]
        self._service_installation_resolver = InstallationResolver(self._logger)

    def delete_keyring_secrets(self) -> None:
        self._logger.info("Deleting all secrets registered in keyring...")
        for secret_mgr in self._all_keyring_secrets_list:
            secret_mgr.delete_secret()
        self._logger.info("All secrets registered in keyring have been deleted")

    def install_kast(
        self,
        artifactory_username: str,
        artifactory_token: str,
        installation_location: str,
        gitlab_runner_token: Optional[str] = None,
    ) -> None:
        self._logger.info("Installing KAST...")

        # Validate artifactory credentials
        if not artifactory_username or not artifactory_token:
            self._logger.error("Artifactory credentials are required but not provided")
            self._logger.error("Please provide valid --artifactory-username and --artifactory-token")
            raise ValueError("Missing artifactory credentials")

        log_etc_host_file()

        target_arch = self._get_target_installation_arch(installation_location)

        if not self._check_requirements(installation_location=installation_location):
            self._logger.error("Mandatory requirements are not fullied")
            raise SystemExit(1)
        else:
            self._logger.info("All preliminary mandatory requirements were validated.")

        self._logger.info("Logging into Docker...")
        docker_login(username=artifactory_username, password=artifactory_token, registry=ARTIFACTORY_REGISTRY_URL)
        self._logger.info("Docker login completed successfully")
        try:
            ingress_class_name = configure_ingress_class()
        except Exception as e:
            self._logger.warning(f"Failed to auto-detect ingress class: {e}. Falling back to 'nginx'")
            ingress_class_name = "nginx"

        hostname = None
        host_ip = None

        if installation_location == INSTALLATION_LOCATION_LOCAL_STR:
            (hostname, host_ip) = self.get_hostname_and_ip(arch=target_arch)
            self._update_etc_hosts_info(host_ip)

            if target_arch == ARCH_ARM64:
                self._install_ssh_key(arch=target_arch, hostname=hostname)

        # Check if the helm repo kast is already installed, otherwise add it.
        kast_repo_name = "kast"
        kast_repo_url = "https://artifactory.thalesdigital.io/artifactory/private-helm-kast"
        if is_helm_repo_installed(kast_repo_name):
            self._logger.info(f"{kast_repo_name} helm repo already installed...")
        else:
            self._logger.info(f"Adding helm repo ({kast_repo_name}): {kast_repo_url}")
            try:
                run_command_with_output(
                    f"helm repo add {kast_repo_name} {kast_repo_url} --username {artifactory_username} --password {artifactory_token}"
                )
                self._logger.info(f"Successfully added helm repo ({kast_repo_name})")
            except Exception as e:
                self._logger.error(f"Failed to add helm repo ({kast_repo_name}): {e}")
                self._logger.error("Please check your artifactory credentials and network connectivity")
                raise

        # # Check if the helm repo polycore is already installed, otherwise add it.
        # if is_helm_repo_installed(POLYCORE_COMPONENT):
        #     self._logger.info(f"{POLYCORE_COMPONENT} helm repo already installed...")
        # else:
        #     self._logger.info(f"Adding helm repo ({POLYCORE_COMPONENT}): {POLYCORE_HELM_CHART_URL}")
        #     try:
        #         run_command_with_output(
        #             f"helm repo add {POLYCORE_COMPONENT} {POLYCORE_HELM_CHART_URL} --username {artifactory_username} --password {artifactory_token}"
        #         )
        #         self._logger.info(f"Successfully added helm repo ({POLYCORE_COMPONENT})")
        #     except Exception as e:
        #         self._logger.error(f"Failed to add helm repo ({POLYCORE_COMPONENT}): {e}")
        #         self._logger.error("Please check your artifactory credentials and network connectivity")
        #         raise

        self._logger.info("Updating helm repo...")
        try:
            run_command_with_output("helm repo update")
            self._logger.info("helm repo updated")
        except Exception as e:
            self._logger.error(f"Failed to update helm repositories: {e}")
            self._logger.error("This may cause issues with chart installations")
            raise

        self._gitlab_runner.install(runner_token=gitlab_runner_token)

        if installation_location == INSTALLATION_LOCATION_LOCAL_STR:
            self._install_ingress(arch=target_arch, artifactory_username=artifactory_username, artifactory_token=artifactory_token)

        self._install_certmgr()
        self._install_keycloak(
            installation_location=installation_location,
            ingress_class_name=ingress_class_name,
        )

        s3_password = self._install_minio(
            artifactory_username=artifactory_username,
            artifactory_token=artifactory_token,
            installation_location=installation_location,
            ingress_class_name=ingress_class_name,
        )

        postgresql_password = asyncio.run(
            self._install_postgresql(
                artifactory_username=artifactory_username,
                artifactory_token=artifactory_token,
                installation_location=installation_location,
            )
        )

        self._install_argo(
            s3_password=s3_password,
            ingress_class_name=ingress_class_name,
            installation_location=installation_location,
        )

        # self._install_mlflow(
        #     s3_password=s3_password,
        #     postgres_password=postgresql_password,
        #     artifactory_username=artifactory_username,
        #     artifactory_token=artifactory_token,
        #     installation_location=installation_location,
        #     ingress_class_name=ingress_class_name,
        # )

        (clickhouse_username, clickhouse_password) = self._clickhouse.install(
            s3_password=s3_password,
            artifactory_username=artifactory_username,
            artifactory_token=artifactory_token,
            artifactory_url=ARTIFACTORY_REGISTRY_URL,
            s3_namespace=S3_NAMESPACE,
            s3_service_name=S3_SERVICE_NAME,
            s3_access_key=S3_ACCESS_KEY,
            installation_location=installation_location,
            ingress_class_name=ingress_class_name,
        )

        self._install_prometheus_grafana(
            artifactory_username=artifactory_username,
            artifactory_token=artifactory_token,
            installation_location=installation_location,
            ingress_class_name=ingress_class_name,
        )

        self._install_kserve(
            ingress_class_name=ingress_class_name,
            installation_location=installation_location,
        )

        self._otel.install(
            clickhouse_namespace=CLICKHOUSE_NAMESPACE,
            clickhouse_service_name="clickhouse",
            clickhouse_database=CLICKHOUSE_DATABASE,
            clickhouse_username=clickhouse_username,
            clickhouse_password=clickhouse_password,
            kserve_inference_namespace=KSERVE_INFERENCE,
        )

        self._lakefs.install(
            postgresql_namespace=POSTGRESQL_NAMESPACE,
            postgresql_password=postgresql_password,
            s3_namespace=S3_NAMESPACE,
            s3_service_name=S3_SERVICE_NAME,
            s3_access_key=S3_ACCESS_KEY,
            s3_secret_key=s3_password,
            argo_lakefs_secret_name=ARGO_LAKEFS_SECRET_NAME,
            argo_working_namespace=ARGO_WORKING_NAMESPACE,
            installation_location=installation_location,
            ingress_class_name=ingress_class_name,
        )

        self._install_zot(
            arch=target_arch,
            installation_location=installation_location,
            s3_secret_key=s3_password,
            ingress_class_name=ingress_class_name,
        )

        self._handle_polydb_setup(
            postgresql_password=postgresql_password, artifactory_username=artifactory_username, artifactory_token=artifactory_token
        )
        self._install_polycore(
            artifactory_username=artifactory_username,
            artifactory_token=artifactory_token,
            postgresql_password=postgresql_password,
            clickhouse_username=clickhouse_username,
            clickhouse_password=clickhouse_password,
            installation_location=installation_location,
            ingress_class_name=ingress_class_name,
        )
        self._install_dagger(
            artifactory_username=artifactory_username, artifactory_token=artifactory_token, installation_location=installation_location
        )

        log_etc_host_file()

        self._logger.info("Installing KAST Completed")

    def _get_target_installation_arch(self, installation_location: str) -> str:
        if installation_location == INSTALLATION_LOCATION_LOCAL_STR:
            arch = get_arch()
        elif installation_location == INSTALLATION_LOCATION_REMOTE_STR:
            arch = ARCH_AMD64
        else:
            raise UnsupportedInstallationLocationException(installation_location)
        return arch

    def _check_requirements(self, installation_location: str) -> bool:
        are_requirements_ok = True

        if not is_command_available("cfssl"):
            self._logger.error("cfssl is not a recognized command. Ensure cfssl is installed and functional in your environment.")
            are_requirements_ok = False

        if not is_command_available("docker"):
            self._logger.error("docker is not a recognized command. Ensure docker is installed and functional in your environment.")
            are_requirements_ok = False

        if not is_command_available("kastctl"):
            self._logger.error("kastctl is not a recognized command. Ensure it's installed and available.")
            are_requirements_ok = False

        architecture = get_arch()
        if not arch_supported(architecture):
            self._logger.error(f"Unsupported architecture: {architecture}")
            are_requirements_ok = False

        if installation_location not in INSTALLATION_LOCATIONS:
            self._logger.error(f"installation location ({installation_location}) is not supported. Allowed values are ({INSTALLATION_LOCATIONS})")
            are_requirements_ok = False

        return are_requirements_ok

    def _install_ssh_key(self, arch: str, hostname: str) -> None:
        ssh_public_key_file = "~/.ssh/id_rsa.pub"
        ssh_public_key_file = os.path.expanduser(ssh_public_key_file)

        if not os.path.exists(ssh_public_key_file):
            self._logger.info("Creating ssh public key file")
            run_command('ssh-keygen -t rsa -b 4096 -C "mlopscli" -P "" -f ~/.ssh/id_rsa')
        else:
            self._logger.debug("ssh_public_key_file already exists.")

        if arch == ARCH_ARM64:
            run_sudo_command_with_output(
                command=f"multipass transfer {ssh_public_key_file} {CONTEXT_NAME}:/home/<USER>/authorized_keys-tmp",
                password=self.get_sudo_password(),
            )
            run_sudo_command_with_output(
                command=f"multipass exec {CONTEXT_NAME} -- bash -c 'cat /home/<USER>/authorized_keys-tmp >> /home/<USER>/.ssh/authorized_keys'",
                password=self.get_sudo_password(),
            )
            run_command(f"ssh-keyscan {hostname} >> ~/.ssh/known_hosts")
        elif arch == ARCH_AMD64:
            run_command(f"cat {ssh_public_key_file} >> ~/.ssh/authorized_keys")
        else:
            raise UnsupportedArchitectureException(arch)

    def _update_etc_hosts_info(self, host_ip: str | None) -> None:
        updates = {}
        updates["argo.dpsc"] = host_ip
        updates["clickhouse.dpsc"] = host_ip
        updates["grafana.admin.dpsc"] = host_ip
        updates["keycloak.dpsc"] = host_ip
        updates[LAKEFS_FQDN] = host_ip
        updates["minio-console.dpsc"] = host_ip
        updates["mlflow-processing.dpsc"] = host_ip
        updates["polycore.dpsc"] = host_ip
        updates["polycore-obs.dpsc"] = host_ip
        updates[ZOT_DPSC] = host_ip

        self._write_update_etc_hosts(updates)

    def _install_certmgr(self) -> None:
        self._logger.info("Installing CertMgr...")

        kpack_template_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "yaml", "kpack", "certmanager", KPACK_YAML)
        (kpack_dir, kpack_file) = self._get_kpack_dir_and_file("certmanager")

        if not os.path.exists(kpack_file):
            with open(kpack_template_file, "r") as file:
                kpack_template_content = yaml.safe_load(file)
                if not os.path.exists(kpack_dir):
                    Path(kpack_dir).mkdir(parents=True, exist_ok=True)

                with open(kpack_file, "w") as file:
                    self._logger.debug(f"writing kpack file ({kpack_file})")
                    yaml.dump(kpack_template_content, file, default_flow_style=False)
                    file.close()

        if is_helm_chart_installed("certmanager"):
            self._logger.info("Helm chart (certmanager) is already deployed...")
        else:
            os.chdir(kpack_dir)
            command = KASTCTL_INSTALL_COMMAND
            run_command(command=command, check=True)

        # Create external ca secret
        secret_name = "external-ca-secret"  # noqa: S105
        if is_component_installed(type="secret", name=secret_name, namespace=CERT_MANAGER_COMPONENT):
            self._logger.debug(f"Secret ({secret_name}) already exist")
        else:
            command = f"kubectl create secret tls {secret_name} \
                --namespace={CERT_MANAGER_COMPONENT} \
                --cert={CA_CERT_DIR}/intermediate-ca/output/intermediate-ca-bundle.pem \
                --key={CA_CERT_DIR}/intermediate-ca/output/intermediate-ca-key.pem"
            run_command(command=command)

        # Create cluster issuer
        if not is_component_installed(type="ClusterIssuer", name="external-ca-issuer"):
            resource_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "yaml", "cert-manager", "external-ca-issuer.yaml")
            self._logger.info("Applying external-ca-issuer ...")
            apply_k8s_component(resource_file)

        if is_helm_repo_installed(CERT_MANAGER_HELM_REPO):
            self._logger.info(f"Repo ({CERT_MANAGER_HELM_REPO}) already exist")
        else:
            self._logger.info(f"Adding helm repo ({CERT_MANAGER_HELM_REPO})")
            try:
                run_command_with_output(f"helm repo add {CERT_MANAGER_HELM_REPO} https://charts.jetstack.io")
                self._logger.info(f"Successfully added helm repo ({CERT_MANAGER_HELM_REPO})")
            except Exception as e:
                self._logger.error(f"Failed to add helm repo ({CERT_MANAGER_HELM_REPO}): {e}")
                raise

        # Deploy trust-manager
        if is_helm_chart_installed(TRUST_MANAGER_HELM_CHART_NAME):
            self._logger.info(f"Helm chart ({TRUST_MANAGER_HELM_CHART_NAME}) is already deployed...")
        else:
            command = f"helm upgrade {TRUST_MANAGER_HELM_CHART_NAME} {CERT_MANAGER_HELM_REPO}/{TRUST_MANAGER_HELM_CHART_NAME} \
                --install --version {TRUST_MANAGER_CHART_VERSION} --namespace {CERT_MANAGER_COMPONENT} --wait"
            self._logger.debug(f"Running command ({command})")
            run_command(command)

        # Create cluster bundle
        if not is_component_installed(type="bundle", name="mlops-ca-bundle"):
            resource_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "yaml", "cert-manager", "bundle-mlops-ca.yml")
            self._logger.info("Applying mlops-ca-bundle ...")
            apply_k8s_component(resource_file)

        # Create cluster bundle
        if not is_component_installed(type="bundle", name="ca-bundle"):
            resource_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "yaml", "cert-manager", "bundle-ca.yml")
            self._logger.info("Applying ca-bundle ...")
            apply_k8s_component(resource_file)

        self._logger.info("CertMgr installation completed")

    async def _install_postgresql(self, artifactory_username: str, artifactory_token: str, installation_location: str) -> str:
        """Install PostgreSQL using kpack instead of ansible."""
        self._logger.info("Installing PostgreSQL...")

        postgres_password = self._postgres_secret.get_or_create_secret(length=24, special_chars=False)
        self._logger.info("[SECURITY] PostgreSQL admin password has been securely stored in your system keyring.")
        self._logger.info(f"To retrieve it, use: {self._postgres_secret.print_retrieve_password_command()}")

        # Create namespace if it doesn't exist
        if not is_component_installed(type="namespace", name=POSTGRESQL_NAMESPACE):
            self._logger.info(f"Creating {POSTGRESQL_NAMESPACE} namespace...")
            run_command(f"kubectl create namespace {POSTGRESQL_NAMESPACE}")
        else:
            self._logger.info(f"{POSTGRESQL_NAMESPACE} namespace already exists...")

        # Create registry credentials for image pulls
        if not create_docker_registry_secret(
            name=ARTIFACTORY_SECRET_NAME,
            server=ARTIFACTORY_REGISTRY_URL,
            artifactory_username=artifactory_username,
            artifactory_token=artifactory_token,
            namespace=POSTGRESQL_NAMESPACE,
        ):
            raise SecretCreationException(secret_name=ARTIFACTORY_SECRET_NAME, namespace=POSTGRESQL_NAMESPACE)

        # Setup kpack configuration
        kpack_template_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "yaml", "kpack", "postgresql", KPACK_YAML)
        (kpack_dir, kpack_file) = self._get_kpack_dir_and_file("postgresql")

        # Check if already configured
        if not os.path.exists(kpack_file):
            # Create kpack configuration from template
            with open(kpack_template_file, "r") as file:
                kpack_template_content = yaml.safe_load(file)
                if not os.path.exists(kpack_dir):
                    Path(kpack_dir).mkdir(parents=True, exist_ok=True)

                # Customize with generated password
                kpack_template_content["postgresql"]["adminPassword"] = postgres_password
                kpack_template_content["postgresql"]["exporterPassword"] = postgres_password
                if installation_location == INSTALLATION_LOCATION_REMOTE_STR:
                    # TODO will require specific remote installation type vcluster vs tdp
                    kpack_template_content["postgresql"]["groupId"] = STORAGECLASS_USER_ID
                    kpack_template_content["postgresql"]["userId"] = STORAGECLASS_USER_ID

                kpack_template_content["global"]["storageClassName"] = configure_storage_class(installation_location, "postgresql")

                with open(kpack_file, "w") as file:
                    self._logger.debug(f"writing kpack file ({kpack_file})")
                    yaml.dump(kpack_template_content, file, default_flow_style=False)

        if is_helm_chart_installed("postgresql") or is_helm_chart_installed("postgresql-operator"):
            self._logger.info("PostgreSQL is already deployed...")
        else:
            # waiting_for_serviceaccount_1 = asyncio.create_task(
            #     self._check_serviceaccount_existence(
            #         serviceaccount_name="postgresql-operator-sql-store",
            #         namespace=POSTGRESQL_NAMESPACE,
            #         artifactory_username=artifactory_username,
            #         artifactory_token=artifactory_token,
            #         workload_name="postgresql-operator",
            #         workload_type=WORKLOAD_TYPE_DEPLOYMENT_SCALE,
            #     )
            # )
            # waiting_for_serviceaccount_2 = asyncio.create_task(
            #     self._check_serviceaccount_existence(
            #         serviceaccount_name="kast-default-postgresql-post-install-sa",
            #         namespace=POSTGRESQL_NAMESPACE,
            #         artifactory_username=artifactory_username,
            #         artifactory_token=artifactory_token,
            #         workload_type=WORKLOAD_TYPE_DEPLOYMENT_SCALE,
            #     )
            # )
            # waiting_for_serviceaccount_3 = asyncio.create_task(
            #     self._check_serviceaccount_existence(
            #         serviceaccount_name="postgresql-pod-sql-store",
            #         namespace=POSTGRESQL_NAMESPACE,
            #         artifactory_username=artifactory_username,
            #         artifactory_token=artifactory_token,
            #         workload_name="kast-default-postgresql",
            #         workload_type=WORKLOAD_TYPE_STATEFULSET_SCALE,
            #     )
            # )
            installation_task = asyncio.create_task(
                self._deploy_kast_postgresql(kpack_dir=kpack_dir, artifactory_username=artifactory_username, artifactory_token=artifactory_token)
            )

            await asyncio.gather(installation_task)

        wait_for_pod_to_run(namespace=POSTGRESQL_NAMESPACE, pod_name="kast-default-postgresql-0")

        self._logger.info("Installing PostgreSQL Completed")
        return postgres_password

    async def _deploy_kast_postgresql(self, kpack_dir: str, artifactory_username: str, artifactory_token: str) -> None:
        # Install PostgreSQL using kastctl
        IMAGES = [
            "artifactory.thalesdigital.io/kast-project/kast/spilo-17:4.0.4-ubuntu-22.04-202502061011",
        ]
        # IMPROVEMENT cache hostname and image to save time
        force_download_image_on_host(
            images=IMAGES,
            registry_secret_name=ARTIFACTORY_SECRET_NAME,
            namespace=POSTGRESQL_NAMESPACE,
            artifactory_username=artifactory_username,
            artifactory_token=artifactory_token,
            server=ARTIFACTORY_REGISTRY_URL,
        )

        self._logger.info("Deploying PostgreSQL kpack...")
        os.chdir(kpack_dir)
        command = KASTCTL_INSTALL_COMMAND
        await asyncio.to_thread(run_command, command, True)

        command = f'kubectl patch service kast-default-postgresql -n {POSTGRESQL_NAMESPACE} --type="json" -p=\'[{{ \
                    "op": "replace", \
                    "path": "/spec/selector", \
                    "value": {{"cluster-name": "kast-default-postgresql"}}\
                }}]\''
        await asyncio.to_thread(run_command, command, True)

    def _install_keycloak(self, installation_location: str, ingress_class_name: str) -> None:
        self._logger.info("Installing Keycloak...")

        admin_password = self._keycloack_secret.get_or_create_secret(length=24, special_chars=False)
        self._logger.info("[SECURITY] Keycloak admin password has been securely stored in your system keyring.")
        self._logger.info(f"To retrieve it, use: {self._keycloack_secret.print_retrieve_password_command()}")

        kpack_template_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "yaml", "kpack", "keycloak", KPACK_YAML)
        (kpack_dir, kpack_file) = self._get_kpack_dir_and_file("keycloak")

        with open(kpack_template_file, "r") as file:
            kpack_template_content = yaml.safe_load(file)
            if not os.path.exists(kpack_dir):
                Path(kpack_dir).mkdir(parents=True, exist_ok=True)

            if installation_location == INSTALLATION_LOCATION_REMOTE_STR:
                keycloak_fqdn = f"{KEYCLOAK_SERVICE_NAME}.{TDS_PROJECT_DOMAIN_NAME}"
            else:
                keycloak_fqdn = f"{KEYCLOAK_SERVICE_NAME}.{DPSC_DOMAIN_NAME}"

            for component in kpack_template_content.get("components", []):
                component["namespace"] = KEYCLOAK_NAMESPACE

            kpack_template_content["global"]["domain"] = DPSC_DOMAIN_NAME
            kpack_template_content["global"]["password"] = admin_password
            kpack_template_content["global"]["sso"]["domain"] = keycloak_fqdn
            kpack_template_content["global"]["sso"]["issuer"]["external"] = f"https://{keycloak_fqdn}"
            kpack_template_content["global"]["sso"]["issuer"]["internal"] = f"http://keycloak-http.{KEYCLOAK_NAMESPACE}"
            kpack_template_content["keycloak-realm"]["_values"]["domain"] = DPSC_DOMAIN_NAME
            kpack_template_content["keycloak"]["password"] = admin_password
            kpack_template_content["keycloak"]["domain"] = keycloak_fqdn

            ingress_config = get_keycloak_ingress_config(
                ingress_class=ingress_class_name,
                installation_location= installation_location,
                domain_name=TDS_PROJECT_DOMAIN_NAME if installation_location == INSTALLATION_LOCATION_REMOTE_STR else DPSC_DOMAIN_NAME,
            )
        
            kpack_template_content["keycloak"]["_values"]["keycloakx"]["ingress"]=ingress_config

            with open(kpack_file, "w") as file:
                self._logger.debug(f"writing kpack file ({kpack_file})")
                yaml.dump(kpack_template_content, file, default_flow_style=False)
                file.close()

        if is_helm_chart_installed("keycloak"):
            self._logger.info("Helm chart (keycloak) is already deployed...")
        else:
            os.chdir(kpack_dir)
            run_command(command=KASTCTL_INSTALL_COMMAND, check=True)

        self._logger.info("Preparing to fill keycloak users...")

        # Register the Keycloak service
        keycloak_port = 8080
        self._service_installation_resolver.register_service(
            ServiceConfig(
                service_name=KEYCLOAK_SERVICE_NAME,
                local_port=keycloak_port,
                remote_port=keycloak_port,
                namespace=KEYCLOAK_NAMESPACE,
                client_cls=Keycloak,
                client_kwargs={
                    "keycloak_host": "localhost",
                    "keycloak_port": keycloak_port,
                    "keycloak_path": "auth",
                    "admin_user": "admin",
                    "admin_password": admin_password,
                    "secure_ssl": False,
                    "use_https": False,
                }
            )
        )

        keycloak: Keycloak = self._service_installation_resolver.start_service(KEYCLOAK_SERVICE_NAME)  # type: ignore[assignment]

        # fill users
        users = {"user-a": "password-a", "user-b": "password-b"}

        access_token = keycloak.get_access_token()
        for username, password in users.items():
            keycloak.create_user(access_token, username, password)

        # Stop the service resolver
        self._service_installation_resolver.stop_service(KEYCLOAK_SERVICE_NAME)

        self._logger.info("Keycloak installation completed")

    def _install_ingress(self, arch: str, artifactory_username: str, artifactory_token: str) -> None:
        self._logger.info("Installing Ingress...")
        if not is_component_installed(type="namespace", name=INGRESS_NAMESPACE):
            run_command(f"kubectl create namespace {INGRESS_NAMESPACE}")
        else:
            self._logger.info(f"{INGRESS_NAMESPACE} namespace already exists...")

        if not create_docker_registry_secret(
            name=ARTIFACTORY_SECRET_NAME,
            server=ARTIFACTORY_REGISTRY_URL,
            artifactory_username=artifactory_username,
            artifactory_token=artifactory_token,
            namespace=INGRESS_NAMESPACE,
        ):
            raise SecretCreationException(secret_name=ARTIFACTORY_SECRET_NAME, namespace=INGRESS_NAMESPACE)

        kpack_template_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "yaml", "kpack", "ingress", KPACK_YAML)
        (kpack_dir, kpack_file) = self._get_kpack_dir_and_file("ingress")

        with open(kpack_template_file, "r") as file:
            kpack_template_content = yaml.safe_load(file)
            if not os.path.exists(kpack_dir):
                Path(kpack_dir).mkdir(parents=True, exist_ok=True)

            if arch == ARCH_ARM64:
                kpack_template_content["ingress"]["arch"] = "arm64"
            elif arch == ARCH_AMD64:
                kpack_template_content["ingress"]["arch"] = "amd64"
            else:
                raise UnsupportedArchitectureException(arch)

            with open(kpack_file, "w") as file:
                self._logger.debug(f"writing kpack file ({kpack_file})")
                yaml.dump(kpack_template_content, file, default_flow_style=False)
                file.close()

        if is_helm_chart_installed("ingress"):
            self._logger.info("Helm chart (ingress) is already deployed...")
        else:
            os.chdir(kpack_dir)
            command = KASTCTL_INSTALL_COMMAND
            run_command(command, True)

        resource_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "yaml", "ingress", "ingress-loadbalancer.yaml")
        apply_k8s_component(resource_file, INGRESS_NAMESPACE)
        self._logger.info("Installing Ingress Completed")

    async def _check_serviceaccount_existence(
        self,
        serviceaccount_name: str,
        namespace: str,
        artifactory_username: str,
        artifactory_token: str,
        workload_type: str,
        workload_name: str | None = None,
    ) -> None:
        self._logger.debug("Checking for ServiceAccount creation...")
        while True:
            self._logger.debug(f"Attempt to get the ServiceAccount '{serviceaccount_name}' on namespace '{namespace}'")
            if is_component_installed(type="serviceaccount", name=serviceaccount_name, namespace=namespace):
                self._logger.debug(f"ServiceAccount '{serviceaccount_name}' created.")
                await self.modify_serviceaccount(
                    serviceaccount_name,
                    namespace,
                    artifactory_username=artifactory_username,
                    artifactory_token=artifactory_token,
                    workload_name=workload_name,
                    workload_type=workload_type,
                )
                break  # Exit loop once found
            else:
                await asyncio.sleep(1)

    async def modify_serviceaccount(
        self,
        serviceaccount_name: str,
        namespace: str,
        artifactory_username: str,
        artifactory_token: str,
        workload_type: str,
        workload_name: str | None = None,
    ) -> None:
        self._logger.debug(f"Modifying ServiceAccount '{serviceaccount_name}'...")
        if check_image_pull_secret(service_account_name=serviceaccount_name, namespace=namespace, secret_name=ARTIFACTORY_SECRET_NAME):
            self._logger.debug(f"ServiceAccount '{serviceaccount_name}' already has imagePullSecrets set")
        else:
            command = f'kubectl patch serviceaccount {serviceaccount_name} -n {namespace} --type=\'json\' -p=\'[{{"op": "add", "path": "/imagePullSecrets", "value": [{{"name": "{ARTIFACTORY_SECRET_NAME}"}}]}}]\''  # noqa: E501
            await asyncio.to_thread(run_command, command)
            if not create_docker_registry_secret(
                name=ARTIFACTORY_SECRET_NAME,
                server=ARTIFACTORY_REGISTRY_URL,
                artifactory_username=artifactory_username,
                artifactory_token=artifactory_token,
                namespace=namespace,
            ):
                raise SecretCreationException(secret_name=ARTIFACTORY_SECRET_NAME, namespace=namespace)
            self._logger.debug(f"ServiceAccount '{serviceaccount_name}' modified.")
            if workload_name is not None:
                if workload_type in WORKLOAD_TYPES:
                    self._logger.debug(f"workload_name ({workload_name})")
                    if workload_type == WORKLOAD_TYPE_DEPLOYMENT_RESTART:
                        restart_deployment(deployment_name=workload_name, namespace=namespace)
                    elif workload_type == WORKLOAD_TYPE_DEPLOYMENT_SCALE:
                        scale_deployment_down_and_up(deployment_name=workload_name, namespace=namespace, must_exist=False)
                    elif workload_type == WORKLOAD_TYPE_STATEFULSET_SCALE:
                        scale_statefulset_down_and_up(statefulset_name=workload_name, namespace=namespace, must_exist=False)
                    else:
                        self._logger.warning(f"Ignoring workload action ({workload_type}) on name ({workload_name})")
                else:
                    raise UnsupportedWorkloadTypeException(workload_name)

    def _install_prometheus_grafana(
        self, artifactory_username: str, artifactory_token: str, installation_location: str, ingress_class_name: str
    ) -> None:
        self._logger.info("Installing Prometheus and Grafana...")
        if not is_component_installed(type="namespace", name=MONITORING_NAMESPACE):
            run_command(f"kubectl create namespace {MONITORING_NAMESPACE}")
        else:
            self._logger.info(f"{MONITORING_NAMESPACE} namespace already exists...")

        if not create_docker_registry_secret(
            name=ARTIFACTORY_SECRET_NAME,
            server=ARTIFACTORY_REGISTRY_URL,
            artifactory_username=artifactory_username,
            artifactory_token=artifactory_token,
            namespace=MONITORING_NAMESPACE,
        ):
            raise SecretCreationException(secret_name=ARTIFACTORY_SECRET_NAME, namespace=MONITORING_NAMESPACE)

        dashboard_configmap = "otel-clickhouse-dashboard"
        dashboard_file = os.path.join(
            os.path.dirname(os.path.abspath(__file__)), "..", "yaml", "prometheus-grafana", "mlops_otel_clickhouse_dashboard.json"
        )

        if not is_component_installed(type="configmap", name=dashboard_configmap, namespace=MONITORING_NAMESPACE):
            run_command(
                f"kubectl create configmap {dashboard_configmap} -n {MONITORING_NAMESPACE} --from-file=mlops_otel_clickhouse_dashboard.json={dashboard_file}"  # noqa: E501
            )
        else:
            self._logger.info(f"{dashboard_configmap} configmap already exists...")

        kpack_template_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "yaml", "kpack", "prometheus-grafana", KPACK_YAML)
        (kpack_dir, kpack_file) = self._get_kpack_dir_and_file("prometheus-grafana")

        with open(kpack_template_file, "r") as file:
            kpack_template_content = yaml.safe_load(file)
            if not os.path.exists(kpack_dir):
                Path(kpack_dir).mkdir(parents=True, exist_ok=True)

            # Get ingress configuration using unified abstraction
            grafana_ingress_config = get_grafana_ingress_config(
                ingress_class=ingress_class_name, installation_location=installation_location, domain_name=TDS_PROJECT_DOMAIN_NAME
            )

            with open(kpack_file, "w") as file:
                self._logger.debug(f"writing kpack file ({kpack_file})")
                kpack_template_content["global"]["ingress"] = {"adminClassName": ingress_class_name}
                kpack_template_content["global"]["storageClassName"] = configure_storage_class(
                    installation_location=installation_location, component="prometheus-grafana"
                )

                kpack_template_content["grafana"]["_values"]["grafana"]["ingress"] = grafana_ingress_config

                yaml.dump(kpack_template_content, file, default_flow_style=False)
                file.close()

        if is_helm_chart_installed("grafana"):
            self._logger.info("Helm chart (grafana) is already deployed...")
        else:
            os.chdir(kpack_dir)
            command = KASTCTL_INSTALL_COMMAND
            run_command(command=command, check=True)

        self._logger.info("Installing Prometheus and Grafana Completed")

    def _install_minio(self, artifactory_username: str, artifactory_token: str, installation_location: str, ingress_class_name: str) -> str:
        self._logger.info("Installing Minio...")
        if not is_component_installed(type="namespace", name=S3_NAMESPACE):
            run_command(f"kubectl create namespace {S3_NAMESPACE}")
        if not create_docker_registry_secret(
            name=ARTIFACTORY_SECRET_NAME,
            server=ARTIFACTORY_REGISTRY_URL,
            artifactory_username=artifactory_username,
            artifactory_token=artifactory_token,
            namespace=S3_NAMESPACE,
        ):
            raise SecretCreationException(secret_name=ARTIFACTORY_SECRET_NAME, namespace=S3_NAMESPACE)
        serviceaccount_name = "default"
        if check_image_pull_secret(service_account_name=serviceaccount_name, namespace=S3_NAMESPACE, secret_name=ARTIFACTORY_SECRET_NAME):
            self._logger.debug(f"ServiceAccount '{serviceaccount_name}' already has imagePullSecrets set")
        else:
            command = f'kubectl patch serviceaccount {serviceaccount_name} -n {S3_NAMESPACE} --type=\'json\' -p=\'[{{"op": "add", "path": "/imagePullSecrets", "value": [{{"name": "{ARTIFACTORY_SECRET_NAME}"}}]}}]\''  # noqa: E501
            self._logger.debug(f"Patching serviceAccount '{serviceaccount_name}' to add imagePullSecrets")
            run_command(command)

        minio_internal_tls_secret = "s3-internal-tls"  # noqa: S105
        create_certificate(
            name=minio_internal_tls_secret,
            namespace=S3_NAMESPACE,
            dns_names=["localhost", f"{S3_SERVICE_NAME}.{S3_NAMESPACE}"],
            yaml_file_path=KAST_WORKING_DIR,
        )

        s3_password = self._minio_secret.get_or_create_secret(length=24, special_chars=False)
        self._logger.info("[SECURITY] Keycloak admin password has been securely stored in your system keyring.")
        self._logger.info(f"To retrieve it, use: {self._minio_secret.print_retrieve_password_command()}")

        kpack_template_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "yaml", "kpack", "minio", KPACK_YAML)
        (kpack_dir, kpack_file) = self._get_kpack_dir_and_file("minio")

        with open(kpack_template_file, "r") as file:
            kpack_template_content = yaml.safe_load(file)
            if not os.path.exists(kpack_dir):
                Path(kpack_dir).mkdir(parents=True, exist_ok=True)
            kpack_template_content["minio"]["_values"]["minio"]["users"][0]["accessKey"] = "minio"
            kpack_template_content["minio"]["_values"]["minio"]["users"][0]["secretKey"] = s3_password
            kpack_template_content["minio"]["_values"]["minio"]["tls"]["certSecret"] = minio_internal_tls_secret
            kpack_template_content["minio"]["_values"]["minio"]["persistence"] = {
                "enabled": True,
                "storageClass": configure_storage_class(installation_location, "minio"),
                "size": os.getenv("MINIO_VOLUME_SIZE", "10Gi"),
            }
            # Get dual ingress configurations using unified abstraction
            api_ingress_config, console_ingress_config = get_minio_dual_ingress_config(
                ingress_class=ingress_class_name, installation_location=installation_location, domain_name=TDS_PROJECT_DOMAIN_NAME
            )

            # Apply ingress configurations to kpack template
            kpack_template_content["minio"]["_values"]["minio"]["ingress"] = api_ingress_config
            kpack_template_content["minio"]["_values"]["minio"]["consoleIngress"] = console_ingress_config

            if installation_location == INSTALLATION_LOCATION_REMOTE_STR:
                # TODO will require specific remote installation type vcluster vs tdp
                kpack_template_content["minio"]["userId"] = STORAGECLASS_USER_ID
                kpack_template_content["minio"]["groupId"] = STORAGECLASS_USER_ID

            with open(kpack_file, "w") as file:
                self._logger.debug(f"writing kpack file ({kpack_file})")
                yaml.dump(kpack_template_content, file, default_flow_style=False)
                file.close()

        if is_helm_chart_installed("minio"):
            self._logger.info("Helm chart (minio) is already deployed...")
        else:
            os.chdir(kpack_dir)
            command = KASTCTL_INSTALL_COMMAND
            run_command(command=command, check=True)

        self._logger.info("Installing Minio Completed")
        return s3_password

    def _install_argo(self, s3_password: str, installation_location: str, ingress_class_name: str) -> None:
        self._logger.info("Installing Argo Workflow...")

        create_s3_bucket(
            service_name=S3_SERVICE_NAME,
            namespace=S3_NAMESPACE,
            s3_access_key=S3_ACCESS_KEY,
            s3_secret_key=s3_password,
            s3_host="localhost",
            s3_port=9000,
            bucket_name="argo-archives",
        )

        if not is_component_installed(type="namespace", name=ARGO_COMPONENT):
            run_command(f"kubectl create namespace {ARGO_COMPONENT}")

        if is_component_installed(type="secret", name=ARGO_INTERMEDIATE_CA_SECRET_NAME, namespace=ARGO_WORKING_NAMESPACE):
            self._logger.debug(f"Secret ({ARGO_INTERMEDIATE_CA_SECRET_NAME}) already exist")
        else:
            command = f"kubectl create secret generic {ARGO_INTERMEDIATE_CA_SECRET_NAME} \
            --namespace={ARGO_WORKING_NAMESPACE} \
            --from-file=intermediate-ca-cert.pem={CA_CERT_DIR}/intermediate-ca/output/intermediate-ca-cert.pem"
            run_command(command=command)

        kpack_template_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "yaml", "kpack", "argo", KPACK_YAML)
        (kpack_dir, kpack_file) = self._get_kpack_dir_and_file("argo")

        with open(kpack_template_file, "r") as file:
            kpack_template_content = yaml.safe_load(file)
            if not os.path.exists(kpack_dir):
                Path(kpack_dir).mkdir(parents=True, exist_ok=True)

            # Get ingress configuration using unified abstraction
            argo_ingress_config = get_argo_ingress_config(
                ingress_class=ingress_class_name, installation_location=installation_location, domain_name=TDS_PROJECT_DOMAIN_NAME
            )
            kpack_template_content["argoworkflows"]["_values"]["argo-workflows"]["server"]["ingress"] = argo_ingress_config

            with open(kpack_file, "w") as file:
                self._logger.debug(f"writing kpack file ({kpack_file})")
                yaml.dump(kpack_template_content, file, default_flow_style=False)
                file.close()

        if is_helm_chart_installed("argoworkflows"):
            self._logger.info("Helm chart (argo-workflows) is already deployed...")
        else:
            self._logger.info("Deploying argo-workflows kpack...")
            os.chdir(kpack_dir)
            command = KASTCTL_INSTALL_COMMAND
            run_command(command=command, check=True)

        resource_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "yaml", "argo", "workflow-executor-binding.yaml")
        apply_k8s_component(resource_file, ARGO_WORKING_NAMESPACE)

        create_secret(
            secret_name=ARGO_S3_SECRET_NAME,
            secret_data={"accesskey": S3_ACCESS_KEY, "secretkey": s3_password},
            namespace=ARGO_WORKING_NAMESPACE,
            force_create=True,
        )

        self._logger.info("Installing Argo Workflow Completed")

    def _install_argo_cli(self) -> None:
        if not os.path.exists(TMP_WORKING_DIR):
            self._logger.debug(f"Path ({TMP_WORKING_DIR}) did not exist, creating it...")
            os.makedirs(TMP_WORKING_DIR)

        run_command(
            f"wget https://github.com/argoproj/argo-workflows/releases/download/{ARGO_VERSION}/argo-darwin-arm64.gz -O {TMP_WORKING_DIR}/argo-darwin-arm64.gz"  # noqa: E501
        )
        os.chdir(TMP_WORKING_DIR)
        run_command("gunzip argo-darwin-arm64.gz")
        run_command("mv argo-darwin-arm64 argo")
        run_command("chmod 755 argo")
        run_sudo_command_with_output(command="mv argo /usr/local/bin", password=self.get_sudo_password())

    def _install_mlflow(
        self,
        s3_password: str,
        postgres_password: str,
        artifactory_username: str,
        artifactory_token: str,
        installation_location: str,
        ingress_class_name: str,
    ) -> None:
        self._logger.info("Installing ML Flow...")

        self._service_installation_resolver.register_service(
            ServiceConfig(
                service_name=POLYDB_POSTGRESQL_SERVICE_NAME,
                local_port=POSTGRESQL_PORT,
                remote_port=POSTGRESQL_PORT,
                namespace=POSTGRESQL_NAMESPACE,
                client_cls= object,  # noqa: E501
            )
        )
        # Start the PostgreSQL service 
        self._service_installation_resolver.start_service(POLYDB_POSTGRESQL_SERVICE_NAME)
        create_postgres_database_without_port_forwarding(
            postgresql_username="postgres",
            postgresql_password=postgres_password,
            postgresql_host="localhost",
            postgresql_port=5432,
            db_name="mlflow_processing",
        )
        self._service_installation_resolver.stop_service(POLYDB_POSTGRESQL_SERVICE_NAME)

        # Create S3 bucket for MLFlow processing
        create_s3_bucket(
            service_name=S3_SERVICE_NAME,
            namespace=S3_NAMESPACE,
            s3_access_key=S3_ACCESS_KEY,
            s3_secret_key=s3_password,
            s3_host="localhost",
            s3_port=9000,
            bucket_name="mlflow-processing",
        )

        if not is_component_installed(type="namespace", name=ARGO_COMPONENT):
            run_command(f"kubectl create namespace {ARGO_COMPONENT}")
        else:
            self._logger.info(f"{ARGO_COMPONENT} namespace already exists...")

        if not create_docker_registry_secret(
            name=ARTIFACTORY_SECRET_NAME,
            server=ARTIFACTORY_REGISTRY_URL,
            artifactory_username=artifactory_username,
            artifactory_token=artifactory_token,
            namespace=ARGO_COMPONENT,
        ):
            raise SecretCreationException(secret_name=ARTIFACTORY_SECRET_NAME, namespace=ARGO_COMPONENT)

        serviceaccount_name = "default"
        if check_image_pull_secret(service_account_name=serviceaccount_name, namespace=ARGO_COMPONENT, secret_name=ARTIFACTORY_SECRET_NAME):
            self._logger.debug(f"ServiceAccount '{serviceaccount_name}' already has imagePullSecrets set")
        else:
            command = f'kubectl patch serviceaccount {serviceaccount_name} -n {ARGO_COMPONENT} --type=\'json\' -p=\'[{{"op": "add", "path": "/imagePullSecrets", "value": [{{"name": "{ARTIFACTORY_SECRET_NAME}"}}]}}]\''  # noqa: E501
            self._logger.debug(f"Patching serviceAccount '{serviceaccount_name}' to add imagePullSecrets")
            run_command(command)

        kpack_template_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "yaml", "kpack", "mlflow", KPACK_YAML)
        (kpack_dir, kpack_file) = self._get_kpack_dir_and_file("mlflow")

        with open(kpack_template_file, "r") as file:
            kpack_template_content = yaml.safe_load(file)
            if not os.path.exists(kpack_dir):
                Path(kpack_dir).mkdir(parents=True, exist_ok=True)
            kpack_template_content["mlflow"]["s3"]["host"] = f"https://{S3_SERVICE_NAME}.{S3_NAMESPACE}:9000"
            kpack_template_content["mlflow"]["s3"]["secretKey"] = s3_password
            kpack_template_content["mlflow"]["s3"]["accessKey"] = S3_ACCESS_KEY
            kpack_template_content["mlflow"]["postgres"]["password"] = postgres_password
            kpack_template_content["mlflow"]["postgres"]["passwordUrlEncoded"] = postgres_password

            ingress_config = get_mlflow_ingress_config(
                ingress_class=ingress_class_name, installation_location=installation_location, domain_name=TDS_PROJECT_DOMAIN_NAME
            )
            kpack_template_content["mlflow"]["_values"]["ingress"]=ingress_config

            with open(kpack_file, "w") as file:
                self._logger.debug(f"writing kpack file ({kpack_file})")
                yaml.dump(kpack_template_content, file, default_flow_style=False)
                file.close()

        if is_helm_chart_installed("mlflow"):
            self._logger.info("Helm chart (mlflow) is already deployed...")
        else:
            self._logger.info("Deploying MLFlow kpack...")
            os.chdir(kpack_dir)
            # Currently using repo POLYCORE_COMPONENT until KAST provide an updated MLFLOW KPACK version
            command = f"kastctl install --prompt=false --repo-alias={POLYCORE_COMPONENT} --fetch=false --file={kpack_file} --namespace={ARGO_COMPONENT}"  # noqa: E501
            self._logger.debug(f"Running command ({command})")
            run_command(command=command, check=True)
        self._logger.info("Installing ML Flow Completed")

    def _install_zot(self, installation_location: str, arch: str, s3_secret_key: str, ingress_class_name: str) -> None:
        self._logger.info("Starting deploying zot...")
        create_s3_bucket(
            service_name=S3_SERVICE_NAME,
            namespace=S3_NAMESPACE,
            s3_access_key=S3_ACCESS_KEY,
            s3_secret_key=s3_secret_key,
            s3_host="localhost",
            s3_port=9000,
            bucket_name=ZOT_COMPONENT,
        )

        # Create k3s namespace for zot if not already done.
        if not is_component_installed(type="namespace", name=ZOT_COMPONENT):
            self._logger.info(f"Creating {ZOT_COMPONENT} namespace...")
            run_command(f"kubectl create namespace {ZOT_COMPONENT}")
            self._logger.info(f"Successfully created {ZOT_COMPONENT} namespace!")
        else:
            self._logger.info(f"{ZOT_COMPONENT} namespace already exists...")

        # Check if the helm repo for zot is already installed, otherwise add it.
        if is_helm_repo_installed(ZOT_COMPONENT):
            self._logger.info(f"{ZOT_COMPONENT} helm repo already installed...")
        else:
            self._logger.info(f"Adding helm repo ({ZOT_COMPONENT}): {ZOT_HELM_CHART_URL}")
            try:
                run_command_with_output(f"helm repo add {ZOT_COMPONENT} {ZOT_HELM_CHART_URL}")
                self._logger.info(f"Successfully added helm repo ({ZOT_COMPONENT})")
            except Exception as e:
                self._logger.error(f"Failed to add helm repo ({ZOT_COMPONENT}): {e}")
                raise

        zot_internal_tls_secret = "zot-internal-tls"  # noqa: S105
        create_certificate(
            name=zot_internal_tls_secret,
            namespace=ZOT_COMPONENT,
            dns_names=["localhost", f"{ZOT_HELM_CHART_NAME}.{ZOT_COMPONENT}", f"{ZOT_HELM_CHART_NAME}.{ZOT_COMPONENT}.svc.cluster.local"],
            yaml_file_path=KAST_WORKING_DIR,
        )

        # Deploy Zot
        if is_helm_chart_installed(ZOT_HELM_CHART_NAME):
            self._logger.info(f"Helm chart ({ZOT_HELM_CHART_NAME}) is already deployed...")
        else:
            self._logger.info(f"Creating temporary configuration values file for ({ZOT_HELM_CHART_NAME})...")
            zot_values = get_zot_deployment_values(ingress_class_name)

            zot_configmap = zot_values["configFiles"][CONFIG_JSON]
            zot_configmap = zot_configmap.replace("<S3_SECRET_REPLACE>", s3_secret_key)
            zot_values["configFiles"][CONFIG_JSON] = zot_configmap

            # Get ingress configuration using unified abstraction
            zot_ingress_config = get_zot_ingress_config(
                ingress_class=ingress_class_name, installation_location=installation_location, domain_name=TDS_PROJECT_DOMAIN_NAME
            )
            zot_values["ingress"] = zot_ingress_config

            if installation_location == INSTALLATION_LOCATION_LOCAL_STR and arch == ARCH_ARM64:
                zot_values.update({"image": {"repository": "ghcr.io/project-zot/zot-linux-arm64"}})

            if installation_location == INSTALLATION_LOCATION_REMOTE_STR:
                zot_values["pvc"]["storageClassName"] = configure_storage_class(installation_location=installation_location, component=ZOT_COMPONENT)

            yaml_file_path = os.path.join(KAST_WORKING_DIR, "zot-values.yaml")
            with open(yaml_file_path, "w") as file:
                self._logger.debug("writing zot yaml values...")
                yaml.dump(zot_values, file, default_flow_style=False)
                file.close()

            self._logger.info(f"Deploying zot helm chart: ({ZOT_HELM_CHART_NAME})")
            command = f"helm install -f {yaml_file_path} {ZOT_HELM_CHART_NAME} zot/zot --version {ZOT_CHART_VERSION} -n {ZOT_COMPONENT}"
            self._logger.debug(f"Running command ({command})")
            run_command(command)

            self._logger.info("Successfully deployed Zot!")

    def _handle_polydb_setup(self, postgresql_password: str, artifactory_username: str, artifactory_token: str) -> None:
        """
        Step of the installation procedure to execute somewhere after postgresql is installed.
        Gets the Golang executable that creates the database's tables
        Executes it with given postgresql credentials.
        """
        self._logger.info("Setting up PolyDB Postgresql.")
        executable_name = ""

        local_arch = get_arch()
        if local_arch == ARCH_ARM64:
            executable_name = "dbtool_darwin_arm64"
        elif local_arch == ARCH_AMD64:
            executable_name = "dbtool_linux_amd64"
        else:
            self._logger.error("Cannot build path to binary executable for an unsupported machine.")
            raise UnsupportedArchitectureException(local_arch)

        full_executable_path = os.path.join(KAST_WORKING_DIR, executable_name)
        self._logger.debug(f"Expected full path to binaries: {LogColors.file_content}{full_executable_path}")
        if os.path.exists(full_executable_path):
            self._logger.debug(f"Executable already exists at {LogColors.file_content}{full_executable_path}")
            self._logger.info("Skipping download of PolyDB executable, it already exists.")
            self._logger.info("If you want to re-download it, please delete the file and re-run the installation.")
        else:
            self._logger.debug(f"Executable does not exist at {LogColors.file_content}{full_executable_path}")
            self._logger.info("Downloading PolyDB executable...")
            if not os.path.exists(KAST_WORKING_DIR):
                self._logger.debug(f"Path ({KAST_WORKING_DIR}) did not exist, creating it...")
                os.makedirs(KAST_WORKING_DIR)  # noqa: S108
            self._logger.debug(f"Downloading PolyDB executable to {LogColors.file_content}{full_executable_path}")
            run_command(
                f"wget --user={artifactory_username} --password={artifactory_token} {MLOPS_TOOLCHAIN_GENERIC_REPO_URL}/dbtool/{POLYDB_VERSION}/{executable_name} -O {full_executable_path}"  # noqa: E501
            )
            run_command(f"chmod 755 {full_executable_path}")
            self._logger.info("Successfully downloaded PolyDB executable.")
        self._logger.debug("Running PolyDB executable pre-run tasks...")

        self._service_installation_resolver.register_service(
            ServiceConfig(
                service_name=POLYDB_POSTGRESQL_SERVICE_NAME,
                local_port=POSTGRESQL_PORT,
                remote_port=POSTGRESQL_PORT,
                namespace=POSTGRESQL_NAMESPACE,
                client_cls= object,  # noqa: E501
            )
        )
        self._logger.info("[POLYDB][CREATE] Port forwarding started.")
        self._service_installation_resolver.start_service(POLYDB_POSTGRESQL_SERVICE_NAME)
        self._polydb_executable_pre_run(postgresql_password)
        self._service_installation_resolver.stop_service(POLYDB_POSTGRESQL_SERVICE_NAME)
        self._logger.info("[POLYDB][CREATE] Port forwarding stopped.") 
        self._logger.info("[POLYDB][MIGRATE] Port forwarding started.")
        self._service_installation_resolver.start_service(POLYDB_POSTGRESQL_SERVICE_NAME)
        self._run_polydb_executable(full_executable_path, postgresql_password)
        self._service_installation_resolver.stop_service(POLYDB_POSTGRESQL_SERVICE_NAME)
        self._logger.info("[POLYDB][MIGRATE] Port forwarding stopped.")
        
        self._logger.debug("PolyDB setup successfully.")

    def _polydb_executable_pre_run(self, postgresql_password) -> None:
        self._logger.info("Performing PolyDB executable pre runs")
        create_postgres_database_without_port_forwarding(
            postgresql_username=POSTGRESQL_USERNAME,
            postgresql_password=postgresql_password,
            postgresql_host=POLYDB_POSTGRESQL_HOST,
            postgresql_port=POSTGRESQL_PORT,
            db_name=POLYDB_DB_NAME,
        )
        self._logger.info("pre run checks successful.")

    def _run_polydb_executable(self, full_executable_path: str, postgresql_password: str) -> None:
        if not os.path.exists(full_executable_path):
            self._logger.error(f"Invalid path: {LogColors.file_content}{full_executable_path}")
            raise SystemExit(1)
        else:
            self._logger.debug("Executable found.")
        self._logger.info(f"Running {full_executable_path}")

        port = f"-port {POSTGRESQL_PORT}"
        user = f"-user {POSTGRESQL_USERNAME}"
        password = f"-password {postgresql_password}"
        dbname = f"-dbname {POLYDB_DB_NAME}"

        command = f"{full_executable_path} {port} {user} {password} {dbname}"
        completed_process = subprocess.run(args=[command], shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        return_code = completed_process.returncode

        process_prints = completed_process.stdout.decode("utf-8")
        if len(process_prints) == 0:
            self._logger.warning("No outputs logged from executable.")
        else:
            self._logger.info("Logging execution outputs...")
            self._logger.debug(f"\n{LogColors.file_content}{process_prints}")

        if return_code != 0:
            self._logger.error(f"Executable terminated with non 0 return code: ({LogColors.file_content}{return_code}{LogColors.error})")
            errors = completed_process.stderr.decode("utf-8")

            if len(errors) == 0:
                self._logger.critical("No errors logged from the executable!")
            else:
                self._logger.critical(f"[POLYDB GO ERRORS]:{LogColors.critical}\n{completed_process.stderr.decode('utf-8')}")
            raise SystemExit(1)

        self._logger.info(f"Executable finished with return code: {LogColors.file_content}{return_code}")
        self._logger.info("Successfully ran executable!")


    def _install_dagger(self, artifactory_username: str, artifactory_token: str, installation_location: str) -> None:
        if not is_component_installed(type="namespace", name=DAGGER_COMPONENT):
            self._logger.info(f"Creating {DAGGER_COMPONENT} namespace...")
            run_command(f"kubectl create namespace {DAGGER_COMPONENT}")
            self._logger.info(f"Successfully created {DAGGER_COMPONENT} namespace!")
        else:
            self._logger.info(f"{DAGGER_COMPONENT} namespace already exists...")

        if not create_docker_registry_secret(
            name=ARTIFACTORY_SECRET_NAME,
            server=ARTIFACTORY_REGISTRY_URL,
            artifactory_username=artifactory_username,
            artifactory_token=artifactory_token,
            namespace=DAGGER_COMPONENT,
        ):
            raise SecretCreationException(secret_name=ARTIFACTORY_SECRET_NAME, namespace=DAGGER_COMPONENT)

        if is_helm_chart_installed(DAGGER_HELM_CHART_NAME):
            self._logger.info(f"Helm chart ({DAGGER_HELM_CHART_NAME}) is already deployed...")
        else:
            self._logger.info(f"Creating temporary configuration values file for ({DAGGER_HELM_CHART_NAME})...")

            dagger_values = DAGGER_DEPLOYMENT_YAML_VALUES
            dagger_values["engine"]["persistence"]["buildArtifacts"]["storageClassName"] = configure_storage_class(
                installation_location, DAGGER_COMPONENT
            )
            if installation_location == INSTALLATION_LOCATION_REMOTE_STR:
                dagger_values["engine"]["containerd_sock_mount"] = {"path": "/run/k0s/containerd.sock", "type": "DirectoryOrCreate"}

            yaml_file_path = os.path.join(KAST_WORKING_DIR, "dagger-values.yaml")
            with open(yaml_file_path, "w") as file:
                self._logger.debug("writing dagger yaml values...")
                yaml.dump(dagger_values, file, default_flow_style=False)
                file.close()

            self._logger.info(f"Deploying dagger helm chart: ({DAGGER_HELM_CHART_NAME})")
            # command = f"helm install -f {yaml_file_path} {DAGGER_HELM_CHART_NAME} polycore/polysystem-dagger-engine \
            #           --version {DAGGER_CHART_VERSION} -n {DAGGER_COMPONENT}"
            command = f"helm install -f {yaml_file_path} {DAGGER_HELM_CHART_NAME} /home/<USER>/Documents/workspace/projects/cortaix/sources/devops/mlopscli/worktree/vclster-remote/upstream/mlopscli/projects/charts/polysystem-dagger-engine-1.1.1-develop.tgz \
            --version {DAGGER_CHART_VERSION} -n {DAGGER_COMPONENT}"
            self._logger.debug(f"Running command ({command})")
            run_command(command)

            self._logger.info("Successfully deployed dagger!")

    def _install_kserve(self, ingress_class_name: str, installation_location: str) -> None:
        if not is_component_installed(type="namespace", name=KSERVE_COMPONENT):
            self._logger.info(f"Creating {KSERVE_COMPONENT} namespace...")
            run_command(f"kubectl create namespace {KSERVE_COMPONENT}")
            self._logger.info(f"Successfully created {KSERVE_COMPONENT} namespace!")
        else:
            self._logger.info(f"{KSERVE_COMPONENT} namespace already exists...")

        if not is_component_installed(type="namespace", name=KSERVE_INFERENCE):
            self._logger.info(f"Creating {KSERVE_INFERENCE} namespace...")
            run_command(f"kubectl create namespace {KSERVE_INFERENCE}")
            self._logger.info(f"Successfully created {KSERVE_INFERENCE} namespace!")
        else:
            self._logger.info(f"{KSERVE_INFERENCE} namespace already exists...")

        kserve_webhook_server_tls = "kserve-webhook-server-tls"  # noqa: S105
        create_certificate(
            name=kserve_webhook_server_tls,
            namespace=KSERVE_COMPONENT,
            dns_names=["localhost", "kserve-webhook-server-service.kserve.svc"],
            yaml_file_path=KAST_WORKING_DIR,
        )

        kserve_manifest_folder = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "yaml", "kserve")

        configmap_name = "inferenceservice-config"

        self._logger.debug("Deploying kserve...")
        command = f"kubectl apply --server-side -f {kserve_manifest_folder}/kserve.yaml"
        run_command(command=command, check=False)

        wait_for_pod_with_prefix_to_run(namespace=KSERVE_COMPONENT, pod_name_prefix="kserve-controller-manager")

        self._logger.debug("Deploying kserve CRDS...")
        command = f"kubectl apply --server-side -f {kserve_manifest_folder}/kserve-cluster-resources.yaml"
        run_command(command=command)

        self._logger.debug("Patching kserve deploymnet mode...")
        command = (
            f"kubectl patch configmap {configmap_name} -n {KSERVE_COMPONENT} "
            "--type=strategic "
            '-p \'{"data": {"deploy": "{\\"defaultDeploymentMode\\": \\"RawDeployment\\"}"}}\''
        )
        run_command(command=command)

        if installation_location == INSTALLATION_LOCATION_REMOTE_STR:
            kserver_effective_host = self._get_component_fqdn(compoment=KSERVE_COMPONENT, domain_name=TDS_PROJECT_DOMAIN_NAME)
        else:
            kserver_effective_host = f"{KSERVE_COMPONENT}.{DPSC_DOMAIN_NAME}"

        ingress_config = get_kserve_ingress_config(
            ingress_class=ingress_class_name, installation_location=installation_location, domain_name=TDS_PROJECT_DOMAIN_NAME
        )

        self._logger.debug("Patching kserve ingress classname...")
        command = (
            f"kubectl patch configmap {configmap_name} -n {KSERVE_COMPONENT} "
            "--type=json "
            '-p=\'[{"op": "replace", "path": "/data/ingress", '
            '"value": "{\\"enableGatewayApi\\": false,\\n    '
            '\\"kserveIngressGateway\\": \\"kserve/kserve-ingress-gateway\\",\\n    '
            '\\"ingressGateway\\": \\"knative-serving/knative-ingress-gateway\\",\\n    '
            '\\"localGateway\\": \\"knative-serving/knative-local-gateway\\",\\n    '
            '\\"localGatewayService\\": \\"knative-local-gateway.istio-system.svc.cluster.local\\",\\n    '
            f'\\"ingressDomain\\": \\"{kserver_effective_host}\\",\\n    '
            f'\\"ingressClassName\\": \\"{ingress_config["ingressClassName"]}\\",\\n    '
            '\\"urlScheme\\": \\"http\\",\\n    '
            '\\"disableIstioVirtualHost\\": false,\\n    '
            '\\"disableIngressCreation\\": false\\n}" }]\''
        )
        run_command(command=command)

        self._logger.debug("Patching kserve storageInitializer classname...")
        command = (
            f"kubectl patch configmap {configmap_name} -n {KSERVE_COMPONENT} "
            "--type=json "
            '-p=\'[{"op": "replace", "path": "/data/storageInitializer", '
            '"value": "{\\"image\\": \\"kserve/storage-initializer:v0.15.0\\",\\n    '
            '\\"memoryRequest\\": \\"100Mi\\",\\n    '
            '\\"memoryLimit\\": \\"1Gi\\",\\n    '
            '\\"cpuRequest\\": \\"100m\\",\\n    '
            '\\"cpuLimit\\": \\"1\\",\\n    '
            '\\"caBundleConfigMapName\\": \\"ca-bundle\\",\\n    '
            '\\"caBundleVolumeMountPath\\": \\"/etc/ssl/certs\\",\\n    '
            '\\"enableDirectPvcVolumeMount\\": true,\\n    '
            '\\"enableModelcar\\": false,\\n    '
            '\\"cpuModelcar\\": \\"10m\\",\\n    '
            '\\"memoryModelcar\\": \\"15Mi\\"\\n}" }]\''
        )
        run_command(command=command)
        self._add_kserve_kit_storage()
        self._logger.info("Successfully deployed kserve!")

    def _add_kserve_kit_storage(self) -> None:
        # absolute path is required to work in local for development and with packaged wheel
        resource_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "yaml", "kserve", "kserve-kitprovider.yaml")
        self._logger.info("Applying kserve-kitprovider ...")
        apply_k8s_component(resource_file, "kserve-kitprovider")

    def _install_polycore(
        self,
        artifactory_username: str,
        artifactory_token: str,
        postgresql_password: str,
        clickhouse_username: str,
        clickhouse_password: str,
        installation_location: str,
        ingress_class_name: str,
    ) -> None:
        # Create k3s namespace for polycore if not already done.
        if not is_component_installed(type="namespace", name=MLOPS_TOOLCHAIN_NAMESPACE):
            self._logger.info(f"Creating {MLOPS_TOOLCHAIN_NAMESPACE} namespace...")
            run_command(f"kubectl create namespace {MLOPS_TOOLCHAIN_NAMESPACE}")
            self._logger.info(f"Successfully created {MLOPS_TOOLCHAIN_NAMESPACE} namespace!")
        else:
            self._logger.info(f"{MLOPS_TOOLCHAIN_NAMESPACE} namespace already exists...")

        if not create_docker_registry_secret(
            name=ARTIFACTORY_SECRET_NAME,
            server=ARTIFACTORY_REGISTRY_URL,
            artifactory_username=artifactory_username,
            artifactory_token=artifactory_token,
            namespace=MLOPS_TOOLCHAIN_NAMESPACE,
        ):
            raise SecretCreationException(secret_name=ARTIFACTORY_SECRET_NAME, namespace=MLOPS_TOOLCHAIN_NAMESPACE)

        create_secret(
            "artifactory-secret",
            {"url": "artifactory.thalesdigital.io", "username": artifactory_username, "token": artifactory_token},
            MLOPS_TOOLCHAIN_NAMESPACE,
            True,
        )

        self._logger.info("Saving PolyCore postgresql information for PolyDB...")
        create_secret(
            "postgres-secret",
            {
                "username": POSTGRESQL_USERNAME,
                "password": postgresql_password,
                "db-name": POLYDB_DB_NAME,
                "host": f"kast-default-postgresql.{POSTGRESQL_NAMESPACE}.svc.cluster.local",
                "port": str(POSTGRESQL_PORT),
            },
            MLOPS_TOOLCHAIN_NAMESPACE,
            True,
        )

        # Deploy polycore
        if is_helm_chart_installed(POLYCORE_HELM_CHART_NAME):
            self._logger.info(f"Helm chart ({POLYCORE_HELM_CHART_NAME}) is already deployed...")
        else:
            self._logger.info(f"Creating temporary configuration values file for ({POLYCORE_HELM_CHART_NAME})...")
            polycore_values = POLYCORE_DEPLOYMENT_YAML_VALUES.copy()
            # Generate dynamic ingress configuration with all remote/local handling
            ingress_config = get_polycore_ingress_config(
                ingress_class_name,
                installation_location,
                domain_name=TDS_PROJECT_DOMAIN_NAME if installation_location == INSTALLATION_LOCATION_REMOTE_STR else DPSC_DOMAIN_NAME,
            )
            
            polycore_values["ingress"]=ingress_config

            # Configure observability database connection
            polycore_values["config"]["observability"]["db"]["addr"] = f"{CLICKHOUSE_HELM_CHART_NAME}.{CLICKHOUSE_NAMESPACE}.svc.cluster.local:9000"
            polycore_values["config"]["observability"]["db"]["database"] = CLICKHOUSE_DATABASE
            polycore_values["config"]["observability"]["db"]["username"] = clickhouse_username
            polycore_values["config"]["observability"]["db"]["password"] = clickhouse_password

            yaml_file_path = os.path.join(KAST_WORKING_DIR, "polycore-values.yaml")
            with open(yaml_file_path, "w") as file:
                self._logger.debug("writing polycore yaml values...")
                yaml.dump(polycore_values, file, default_flow_style=False)
                file.close()

            self._logger.info(f"Deploying polycore helm chart: ({POLYCORE_HELM_CHART_NAME})")
            # command = f"helm install -f {yaml_file_path} {POLYCORE_HELM_CHART_NAME} polycore/polycore --version {POLYCORE_CHART_VERSION} -n {MLOPS_TOOLCHAIN_NAMESPACE}"  # noqa: E501

            command = f"helm install -f {yaml_file_path} {POLYCORE_HELM_CHART_NAME}  /home/<USER>/Documents/workspace/projects/cortaix/sources/devops/mlopscli/worktree/vclster-remote/upstream/mlopscli/projects/charts/polycore-1.1.1-develop.tgz --version {POLYCORE_CHART_VERSION} -n {MLOPS_TOOLCHAIN_NAMESPACE}"  # noqa: E501
            
            self._logger.debug(f"Running command ({command})")
            run_command(command)

            self._logger.info("Successfully deployed Polycore!")

    def _get_kpack_dir_and_file(self, component: str) -> Tuple[str, str]:
        kpack_file = os.path.join(KAST_WORKING_DIR, "kpack", component, KPACK_YAML)
        kpack_dir = os.path.dirname(kpack_file)

        return (kpack_dir, kpack_file)

    def uninstall_kast(self) -> None:
        self._logger.info("Uninstalling KAST...")

        self._uninstall_kserve()
        self._uninstall_polycore()
        self._uninstall_dagger()
        self._uninstall_zot()
        self._lakefs.uninstall(delete_namespace=True)
        self._otel.uninstall(delete_collector_namespace=False, delete_operator_namespace=True)
        self._clickhouse.uninstall(delete_namespace=True)
        self._uninstall_mlflow()
        self._uninstall_postgresql()
        self._uninstall_minio()
        self._uninstall_argo()
        self._uninstall_prometheus_grafana()
        self._uninstall_keycloak()
        self._uninstall_certmgr()
        self._uninstall_ingress()
        self._gitlab_runner.uninstall(delete_namespace=True)
        uninstall_helm_repo("kast")

        # Even if secrets should have been deleted during component uninstall phase, we call it again just in case
        self.delete_keyring_secrets()

        if os.path.exists(KAST_WORKING_DIR):
            self._logger.debug(f"removing folder {KAST_WORKING_DIR}")
            shutil.rmtree(KAST_WORKING_DIR)

        self._logger.info("Uninstalling KAST Completed")

    def _uninstall_kserve(self) -> None:
        self._logger.info("Uninstalling kserve...")
        uninstall_k8s_component(type="namespace", name=KSERVE_INFERENCE)
        uninstall_k8s_component(type="namespace", name=KSERVE_COMPONENT)
        self._logger.info("Uninstalling kserve Completed")

    def _uninstall_polycore(self) -> None:
        self._logger.info("Uninstalling Polycore...")
        uninstall_helm_chart(POLYCORE_HELM_CHART_NAME, MLOPS_TOOLCHAIN_NAMESPACE)
        uninstall_helm_repo(POLYCORE_COMPONENT)
        # WARNING: deleting global mlops namespace will have an impact on other applications deployed on this namespace
        uninstall_k8s_component(type="namespace", name=MLOPS_TOOLCHAIN_NAMESPACE)
        self._logger.info("Uninstalling Polycore Completed")

    def _uninstall_zot(self) -> None:
        self._logger.info("Uninstalling Zot...")
        uninstall_helm_chart(ZOT_HELM_CHART_NAME, ZOT_COMPONENT)
        uninstall_helm_repo(ZOT_COMPONENT)
        uninstall_k8s_component(type="namespace", name=ZOT_COMPONENT)

        # This secret deletion is required until argo is running workflow in default namespace and default namespace cannot be deleted
        uninstall_k8s_component("secret", ARGO_LAKEFS_SECRET_NAME, ARGO_WORKING_NAMESPACE)

        self._logger.info("Uninstalling Zot Completed")

    def _uninstall_dagger(self) -> None:
        self._logger.info("Uninstalling dagger...")
        uninstall_helm_chart(DAGGER_HELM_CHART_NAME, DAGGER_COMPONENT)
        uninstall_k8s_component(type="namespace", name=DAGGER_COMPONENT)
        self._logger.info("Uninstalling dagger   Completed")

    def _uninstall_mlflow(self) -> None:
        self._logger.info("Uninstalling ML Flow...")
        (kpack_dir, kpack_file) = self._get_kpack_dir_and_file("mlflow")
        if os.path.exists(kpack_file):
            os.chdir(kpack_dir)
            run_command(KASTCTL_UNINSTALL_COMMAND)
        self._logger.info("Uninstalling ML Flow Completed")

    def _uninstall_postgresql(self) -> None:
        self._logger.info("Uninstalling PostgreSQL...")
        (kpack_dir, kpack_file) = self._get_kpack_dir_and_file("postgresql")
        if os.path.exists(kpack_file):
            os.chdir(kpack_dir)
            run_command(KASTCTL_UNINSTALL_COMMAND)

        uninstall_k8s_component(type="pvc", name="pgdata-kast-default-postgresql-0", namespace=POSTGRESQL_NAMESPACE)
        uninstall_k8s_component(type="namespace", name=POSTGRESQL_NAMESPACE)
        self._postgres_secret.delete_secret()

        self._logger.info("Uninstalling PostgreSQL Completed")

    def _uninstall_minio(self) -> None:
        self._logger.info("Uninstalling Minio...")
        (kpack_dir, kpack_file) = self._get_kpack_dir_and_file("minio")
        if os.path.exists(kpack_file):
            os.chdir(kpack_dir)
            run_command(KASTCTL_UNINSTALL_COMMAND)
        uninstall_k8s_component(type="namespace", name=S3_NAMESPACE)
        self._minio_secret.delete_secret()
        self._logger.info("Uninstalling Minio Completed")

    def _uninstall_argo(self) -> None:
        self._logger.info("Uninstalling Argo Workflow...")
        (kpack_dir, kpack_file) = self._get_kpack_dir_and_file("argo")
        if os.path.exists(kpack_file):
            os.chdir(kpack_dir)
            command = KASTCTL_UNINSTALL_COMMAND
            run_command(command=command)

        # It is ok to delete processing namespace since mlflow has been already uninstalled at this point
        uninstall_k8s_component(type="namespace", name=ARGO_COMPONENT)

        # These secrets deletion is required until argo is running workflow in default namespace and default namespace cannot be deleted
        uninstall_k8s_component("secret", ARTIFACTORY_SECRET_NAME, ARGO_WORKING_NAMESPACE)
        uninstall_k8s_component("secret", ARGO_INTERMEDIATE_CA_SECRET_NAME, ARGO_WORKING_NAMESPACE)
        uninstall_k8s_component("secret", ARGO_S3_SECRET_NAME, ARGO_WORKING_NAMESPACE)

        self._logger.info("Uninstalling Argo Workflow Completed")

    def _uninstall_ingress(self) -> None:
        self._logger.info("Uninstalling Ingress...")
        (kpack_dir, kpack_file) = self._get_kpack_dir_and_file("ingress")
        if os.path.exists(kpack_file):
            os.chdir(kpack_dir)
            command = KASTCTL_UNINSTALL_COMMAND
            run_command(command=command)

        uninstall_k8s_component("service", "ingress-nginx-controller", INGRESS_NAMESPACE)
        uninstall_k8s_component(type="namespace", name=INGRESS_NAMESPACE)
        self._logger.info("Uninstalling Ingress Completed")

    def _uninstall_prometheus_grafana(self) -> None:
        self._logger.info("Uninstalling Prometheus and Grafana...")
        (kpack_dir, kpack_file) = self._get_kpack_dir_and_file("prometheus-grafana")
        if os.path.exists(kpack_file):
            os.chdir(kpack_dir)
            command = KASTCTL_UNINSTALL_COMMAND
            run_command(command=command)

        uninstall_k8s_component(type="namespace", name=MONITORING_NAMESPACE)
        self._logger.info("Uninstalling Prometheus and Grafana Completed")

    def _uninstall_keycloak(self) -> None:
        self._logger.info("Uninstalling keycloak...")
        (kpack_dir, kpack_file) = self._get_kpack_dir_and_file("keycloak")
        if os.path.exists(kpack_file):
            os.chdir(kpack_dir)
            command = KASTCTL_UNINSTALL_COMMAND
            run_command(command=command)

        uninstall_k8s_component(type="namespace", name="authentication")
        self._keycloack_secret.delete_secret()
        self._logger.info("Uninstalling keycloak Completed")

    def _uninstall_certmgr(self) -> None:
        self._logger.info("Uninstalling certificate manager...")
        uninstall_helm_chart(TRUST_MANAGER_HELM_CHART_NAME, CERT_MANAGER_COMPONENT)
        (kpack_dir, kpack_file) = self._get_kpack_dir_and_file("certmanager")
        if os.path.exists(kpack_file):
            os.chdir(kpack_dir)
            command = KASTCTL_UNINSTALL_COMMAND
            run_command(command=command)

        uninstall_k8s_component(type="namespace", name=CERT_MANAGER_COMPONENT)
        uninstall_helm_repo(CERT_MANAGER_HELM_REPO)
        self._logger.info("Uninstalling certificate manager Completed")
