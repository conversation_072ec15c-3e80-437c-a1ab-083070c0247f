from mlopscli.utils.logger import get_logger

CA_CERTIFICATES_CRT = "ca-certificates.crt"
KASTCTL_INSTALL_COMMAND = "kastctl install"
KASTCTL_UNINSTALL_COMMAND = "kastctl uninstall"


class ManageInstallationBaseClass:
    def __init__(self, logger_name: str, namespace: str) -> None:
        self._namespace = namespace
        self._logger = get_logger(logger_name)

    def _get_component_fqdn(self, compoment: str, domain_name: str) -> str:
        return f"{compoment}.{domain_name}"
