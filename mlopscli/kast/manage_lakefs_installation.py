import asyncio
import os
from typing import Tuple

import lakefs_client
from lakefs_client.api import repositories_api
from lakefs_client.configuration import Configuration
from lakefs_client.exceptions import NotFoundException
from lakefs_client.models import RepositoryCreation
from playwright.async_api import async_playwright
import yaml

from mlopscli.kast.manage_installation_base import CA_CERTIFICATES_CRT
from mlopscli.kast.manage_installation_base import ManageInstallationBaseClass
from mlopscli.utils.constants import CLUSTER_ISSUER
from mlopscli.utils.constants import INSTALLATION_LOCATION_REMOTE_STR
from mlopscli.utils.constants import KAST_WORKING_DIR
from mlopscli.utils.constants import NGINX_PROXY_BODY_SIZE
from mlopscli.utils.constants import TDS_PROJECT_DOMAIN_NAME
from mlopscli.utils.encoder import generate_random_base64_string
from mlopscli.utils.installation_ingress_resolver import get_lakefs_ingress_config
from mlopscli.utils.installation_resolver import InstallationResolver
from mlopscli.utils.installation_resolver import ServiceConfig
from mlopscli.utils.kubernetes import create_secret, is_component_installed
from mlopscli.utils.kubernetes import is_helm_chart_installed
from mlopscli.utils.kubernetes import is_helm_repo_installed
from mlopscli.utils.kubernetes import uninstall_helm_chart
from mlopscli.utils.kubernetes import uninstall_helm_repo
from mlopscli.utils.kubernetes import uninstall_k8s_component
from mlopscli.utils.kubernetes import wait_for_pod_with_prefix_to_run
from mlopscli.utils.minio import create_s3_bucket
from mlopscli.utils.postgres import create_postgres_database
from mlopscli.utils.system import run_command
from mlopscli.utils.system import run_command_with_output

LAKEFS_FQDN = "lakefs.dpsc"
LAKEFS_COMPONENT = "lakefs"
LAKEFS_HELM_CHART_NAME = "my-lakefs"
LAKEFS_CHART_VERSION = "1.4.3"
LAKEFS_POSTGRES_SECRET_NAME = "postgres-secret"  # noqa: S105
LAKEFS_POSTGRES_DATABASE_NAME = "lakefs"
POSTGRESQL_SERVICE_NAME = "kast-default-postgresql"
POSTGRESQL_NAMESPACE = "sql-store"
POSTGRESQL_USERNAME = "postgres"
POSTGRESQL_PORT = 5432
LAKEFS_CREDS_FILE = os.path.join(KAST_WORKING_DIR, "lakefs-creds.yaml")
LAKEFS_DEPLOYMENT_YAML_VALUES = {
    "ingress": {
        "enabled": True,
        "annotations": {NGINX_PROXY_BODY_SIZE: "64m", CLUSTER_ISSUER: "external-ca-issuer"},
        "hosts": [{"host": LAKEFS_FQDN, "paths": ["/"]}],
        "tls": [
            {
                "hosts": [LAKEFS_FQDN, f"{LAKEFS_HELM_CHART_NAME}.{LAKEFS_COMPONENT}.svc.cluster.local"],
                "secretName": "lakefs-tls-secret",
            }
        ],
    },
    "existingSecret": LAKEFS_POSTGRES_SECRET_NAME,
    "secretKeys": {"authEncryptSecretKey": "auth_encrypt_secret_key", "databaseConnectionString": "databaseConnectionString"},
    "extraVolumeMounts": [{"mountPath": "/etc/ssl/certs/", "name": "ca-certificate-only", "readOnly": True}],
    "extraVolumes": [
        {
            "name": "ca-certificate-only",
            "configMap": {
                "name": "mlops-ca-bundle",
                "defaultMode": 0o644,
                "optional": False,
                "items": [{"key": CA_CERTIFICATES_CRT, "path": CA_CERTIFICATES_CRT}],
            },
        }
    ],
    "lakefsConfig": """database:
  type: "postgres"
blockstore:
  type: s3
  s3:
    force_path_style: true
    endpoint: <minio-endpoint>
    discover_bucket_region: false
    credentials:
      access_key_id: <minio-access_key_id>
      secret_access_key: <minio-secret_access_key>
""",
}


class LakeFS(ManageInstallationBaseClass):
    def __init__(
        self,
        headless: bool,
    ) -> None:
        super().__init__(logger_name=__name__, namespace=LAKEFS_COMPONENT)
        self._user_email = "<EMAIL>"
        self._headless = headless

    def install(
        self,
        postgresql_namespace: str,
        postgresql_password: str,
        s3_namespace: str,
        s3_service_name: str,
        s3_access_key: str,
        s3_secret_key: str,
        argo_lakefs_secret_name: str,
        argo_working_namespace: str,
        installation_location: str,
        ingress_class_name: str,
    ) -> None:
        self._logger.info("Starting installing lakefs...")

        # Set the FQDN for LakeFS
        if installation_location == INSTALLATION_LOCATION_REMOTE_STR:
            self._logger.info("Setting LakeFS FQDN to remote location but port-forwarding will be used")
            self.lakefs_fqdn = f"http://localhost:{8000}"
        else:
            self.lakefs_fqdn = f"https://{LAKEFS_FQDN}"

        # Create an installation resolver
        lakefs_service_resolver = InstallationResolver(self._logger)
        lakefs_service_resolver.register_service(
            ServiceConfig(
                service_name=LAKEFS_HELM_CHART_NAME,
                local_port=8000,
                remote_port=80,
                namespace=self._namespace,
                client_cls=lakefs_client.ApiClient,
            )
        )

        self._logger.info("Creating postgres secret...")
        # Create the postgres secret needed by LakeFS
        create_secret(
            "postgres-secret",
            {
                "username": POSTGRESQL_USERNAME,
                "password": postgresql_password,
                "db-name": LAKEFS_POSTGRES_DATABASE_NAME,
                "host":
                f"{POSTGRESQL_SERVICE_NAME}.{POSTGRESQL_NAMESPACE}.svc.cluster.local",
                "port": str(POSTGRESQL_PORT),
            },
            self._namespace,
            True,
        )

        self._logger.info("Creating postgres and s3 resources...")
        # Create Postgres and S3 resources
        create_postgres_database(
            pod_name=f"{POSTGRESQL_SERVICE_NAME}-0",
            namespace=postgresql_namespace,
            postgresql_username=POSTGRESQL_USERNAME,
            postgresql_password=postgresql_password,
            postgresql_host="localhost",
            postgresql_port=5432,
            db_name=LAKEFS_COMPONENT,
        )
        self._logger.info("Creating S3 bucket...")
        # Create S3 bucket
        create_s3_bucket(
            service_name=s3_service_name,
            namespace=s3_namespace,
            s3_access_key=s3_access_key,
            s3_secret_key=s3_secret_key,
            s3_host="localhost",
            s3_port=9000,
            bucket_name=LAKEFS_COMPONENT,
        )

        if not is_component_installed(type="namespace", name=self._namespace):
            run_command(f"kubectl create namespace {self._namespace}")

        if not is_component_installed(type="secret", name=LAKEFS_POSTGRES_SECRET_NAME, namespace=self._namespace):
            random_str = generate_random_base64_string(32)
            command = f"""kubectl create secret generic {LAKEFS_POSTGRES_SECRET_NAME} \
                --from-literal=databaseConnectionString=postgresql://postgres:{postgresql_password}@kast-default-postgresql.{postgresql_namespace}.svc.cluster.local:5432/{LAKEFS_COMPONENT} \
                --from-literal=auth_encrypt_secret_key="{random_str}" -n {self._namespace}"""  # noqa: E501  #nosec
            run_command(command)

        if is_helm_repo_installed(LAKEFS_COMPONENT):
            self._logger.info(f"Repo ({LAKEFS_COMPONENT}) already exist")
        else:
            self._logger.info(f"Adding helm repo ({LAKEFS_COMPONENT})")
            try:
                run_command_with_output("helm repo add lakefs https://charts.lakefs.io")
                self._logger.info(f"Successfully added helm repo ({LAKEFS_COMPONENT})")
            except Exception as e:
                self._logger.error(f"Failed to add helm repo ({LAKEFS_COMPONENT}): {e}")
                raise

        if is_helm_chart_installed(LAKEFS_HELM_CHART_NAME):
            self._logger.info(f"Helm chart ({LAKEFS_COMPONENT}) is already deployed")
        else:
            self._logger.info(f"Creating temporary configuration values file for ({LAKEFS_HELM_CHART_NAME}) ...")
            lakefs_values = LAKEFS_DEPLOYMENT_YAML_VALUES
            lakefs_configmap = lakefs_values["lakefsConfig"]
            lakefs_configmap = lakefs_configmap.replace("<minio-endpoint>", f"https://{s3_service_name}.{s3_namespace}:9000")
            lakefs_configmap = lakefs_configmap.replace("<minio-access_key_id>", s3_access_key)
            lakefs_configmap = lakefs_configmap.replace("<minio-secret_access_key>", s3_secret_key)
            lakefs_values["lakefsConfig"] = lakefs_configmap

            # Get ingress configuration using unified abstraction
            lakefs_ingress_config = get_lakefs_ingress_config(
                ingress_class=ingress_class_name, installation_location=installation_location, domain_name=TDS_PROJECT_DOMAIN_NAME
            )
            lakefs_values["ingress"] = lakefs_ingress_config

            yaml_file_path = os.path.join(KAST_WORKING_DIR, "lakefs-values.yaml")
            with open(yaml_file_path, "w") as file:
                self._logger.debug("writing lakefs yaml values...")
                yaml.dump(lakefs_values, file, default_flow_style=False)
                file.close()

            self._logger.info(f"Deploying lakefs helm chart: ({LAKEFS_HELM_CHART_NAME})")
            command = f"helm install -f {yaml_file_path} {LAKEFS_HELM_CHART_NAME} lakefs/lakefs --version {LAKEFS_CHART_VERSION} \
                  -n {self._namespace}"
            self._logger.debug(f"Running command ({command})")
            run_command(command)

        self._logger.info(f"waiting for pod {LAKEFS_HELM_CHART_NAME} to start...")
        wait_for_pod_with_prefix_to_run(namespace=self._namespace, pod_name_prefix=LAKEFS_HELM_CHART_NAME)
        self._install_browsers()
        self._logger.info("Browser installed")

        # Start the service forwarding resolver
        lakefs_service_resolver.start_service(LAKEFS_HELM_CHART_NAME)

        (lakefs_access_key_id, lakefs_secret_access_key) = asyncio.run(self._login_and_extract_credentials(self.lakefs_fqdn))
        self._create_lakefs_secret(
            secret_name=argo_lakefs_secret_name,
            service=LAKEFS_HELM_CHART_NAME,
            target_namespace=argo_working_namespace,
            lakefs_access_key_id=lakefs_access_key_id,
            lakefs_secret_access_key=lakefs_secret_access_key,
        )
        self._create_default_repository(lakefs_access_key_id=lakefs_access_key_id, lakefs_secret_access_key=lakefs_secret_access_key)

        # Stop the service forwarding resolver
        lakefs_service_resolver.stop_service(LAKEFS_HELM_CHART_NAME)

        self._logger.info("Successfully installed lakefs")

    async def _login_and_extract_credentials(self, url: str) -> Tuple[str, str]:
        self._logger.info("Starting lakefs credentials extraction ...")
        lakefs_access_key_id = None
        lakefs_secret_access_key = None

        if os.path.exists(LAKEFS_CREDS_FILE):
            self._logger.info(f"Lakefs credentials file ({LAKEFS_CREDS_FILE}) already exist. Skipping extraction")
            with open(LAKEFS_CREDS_FILE, "r") as file:
                creds = yaml.safe_load(file)
                lakefs_access_key_id = creds["access_key_id"]
                lakefs_secret_access_key = creds["secret_access_key"]
            return (lakefs_access_key_id, lakefs_secret_access_key)

        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=self._headless, args=["--ignore-certificate-errors", "--no-sandbox"])
            self._logger.info("Browser launched")

            page = await browser.new_page()
            self._logger.info("New page created")

            try:
                await page.goto(url, wait_until="networkidle", timeout=10000)
                await asyncio.sleep(10)
            except Exception as e:
                self._logger.error("Timed out waiting for:", url, e)
                await browser.close()
                raise RuntimeError(f"Failed to load LakeFS URL: {url}") from e

            class_exists = await page.evaluate("""() => {
                return document.querySelector('.setup-widget.card') !== null;
            }""")

            if not class_exists:
                self._logger.info(
                    "The class setup-widget card doesn't exist in the source code. Credentials have been already extracted from webpage"
                )
            else:
                self._logger.info("The class setup-widget card exists in the source code.")
                await page.fill("#user-email", self._user_email)  # Adjust the selector as needed
                await page.click('button[type="submit"]')  # Adjust the selector as needed

                try:
                    await page.wait_for_selector("div.row.mt-4 code", timeout=10000)  # Wait for the Access Key ID to appear
                except Exception as e:
                    self._logger.error("Timed out waiting for the Access Key ID to appear:", e)
                    await browser.close()
                    raise e

                # Extract the Access Key ID
                lakefs_access_key_id = await page.inner_text("div.row.mt-4 code")
                lakefs_secret_access_key = await page.inner_text("div.row.mt-2 code")

                self._logger.debug("Access Key ID: %s", lakefs_access_key_id)
                self._logger.debug("Secret Access Key: %s", lakefs_secret_access_key)

                # Here you might want to save the credentials to the YAML file
                lakefs_data = {"access_key_id": lakefs_access_key_id, "secret_access_key": lakefs_secret_access_key}
                with open(LAKEFS_CREDS_FILE, "w") as file:
                    yaml.dump(lakefs_data, file, default_flow_style=False)
                    self._logger.info("Credentials saved to %s", LAKEFS_CREDS_FILE)

            await browser.close()

        # Ensure credentials were extracted
        if not lakefs_access_key_id or not lakefs_secret_access_key:
            raise RuntimeError("Failed to extract LakeFS credentials")

        self._logger.info("Lakefs credentials extraction completed")
        return (lakefs_access_key_id, lakefs_secret_access_key)

    def _create_lakefs_secret(
        self, secret_name: str, service: str, target_namespace: str, lakefs_access_key_id: str, lakefs_secret_access_key: str
    ) -> None:
        if is_component_installed(type="secret", name=secret_name, namespace=target_namespace):
            self._logger.debug(f"Secret ({secret_name}) already exist")
        else:
            self._logger.info(f"Creating lakefs secret {target_namespace}/{secret_name}")
            end_point_url = f"http://{service}.{self._namespace}.svc.cluster.local"
            command = f"kubectl create secret generic {secret_name} \
            --namespace={target_namespace} \
            --from-literal=accessKey={lakefs_access_key_id} \
            --from-literal=secretAccessKey={lakefs_secret_access_key} \
            --from-literal=endpointUrl={end_point_url}"
            run_command(command=command)

    def _install_browsers(self) -> None:
        self._logger.info("Installing browers...")
        if self._headless:
            run_command("playwright install chromium-headless-shell")
        else:
            run_command("playwright install chromium")
        self._logger.info("Browers installation completed")

    def _create_default_repository(self, lakefs_access_key_id: str, lakefs_secret_access_key: str) -> None:
        # Configuration for LakeFS
        lakefs_config = Configuration(host=f"{self.lakefs_fqdn}/api/v1", username=lakefs_access_key_id, password=lakefs_secret_access_key)
        lakefs_config.verify_ssl = False

        # Initialize API client
        with lakefs_client.ApiClient(lakefs_config) as api_client:
            repo_api = repositories_api.RepositoriesApi(api_client)

            name = "kedro-template"

            try:
                repo = repo_api.get_repository(repository=name)
                self._logger.info(f"Repository {name} already exists.")
            except NotFoundException:
                self._logger.info(f"Repository {name} not found. Creating...")
                repo = RepositoryCreation(name=name, storage_namespace=f"s3://lakefs/{name}", default_branch="main")

                created_repo = repo_api.create_repository(repository_creation=repo)
                self._logger.info(f"Repository created: {created_repo.id}")

    def uninstall(self, delete_namespace: bool) -> None:
        self._logger.info("Uninstalling Lake FS...")
        # postgres DB and minio data will be deleted by the postgresql and minio uninstall
        uninstall_k8s_component(type="secret", name=LAKEFS_POSTGRES_SECRET_NAME, namespace=self._namespace)
        uninstall_helm_chart(LAKEFS_HELM_CHART_NAME, self._namespace)
        if delete_namespace:
            uninstall_k8s_component(type="namespace", name=self._namespace)
        uninstall_helm_repo(LAKEFS_COMPONENT)

        if os.path.isfile(LAKEFS_CREDS_FILE):
            os.remove(LAKEFS_CREDS_FILE)
            self._logger.debug(f"{LAKEFS_CREDS_FILE} has been deleted.")
        else:
            self._logger.debug(f"{LAKEFS_CREDS_FILE} does not exist.")

        self._logger.info("Uninstalling Lake FS Completed")


# Usage
if __name__ == "__main__":
    import asyncio

    lakefs = LakeFS(headless=True)
    lakefs._install_browsers()

    async def test_credentials() -> None:
        creds = await lakefs._login_and_extract_credentials(f"https://{LAKEFS_FQDN}")
        lakefs_access_key_id, lakefs_secret_access_key = creds
        lakefs._create_lakefs_secret(
            secret_name="toto",  # noqa: S106
            service="my-lakefs",
            target_namespace="default",
            lakefs_access_key_id=lakefs_access_key_id,
            lakefs_secret_access_key=lakefs_secret_access_key,
        )
        lakefs._create_default_repository(lakefs_access_key_id=lakefs_access_key_id, lakefs_secret_access_key=lakefs_secret_access_key)

    asyncio.run(test_credentials())
