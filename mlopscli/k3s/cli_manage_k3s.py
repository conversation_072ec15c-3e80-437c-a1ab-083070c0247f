"""cli manage_aks."""

import click
from click.core import Context
import click_config_file
from dependency_injector import providers
from dependency_injector.wiring import Provide
from dependency_injector.wiring import Provider
from dependency_injector.wiring import inject

from mlopscli.dependency_injection.containers import Application
from mlopscli.dependency_injection.containers import configure_application_container
from mlopscli.k3s.manage_k3s_installation import ManageK3SInstallation
from mlopscli.utils.log_header import log_full_host_header
from mlopscli.utils.logger import get_logger

logger = get_logger(__name__)


@click.group(name="k3s")
def click_group() -> None:
    """Manage K3S"""
    pass


@click.command(name="install", short_help="install k3s")
@click_config_file.configuration_option(implicit=False)
@click.pass_context
@inject
def install_k3s(
    ctx: Context,
    config: providers.Configuration = Provider[Application.config],  # type: ignore
    **kwargs,  # pylint: disable=unused-argument  # noqa: ANN003
) -> int:
    """cli entrypoint."""
    config.from_dict(ctx.params)
    configure_application_container(config, "manage_k3s")
    return _install_k3s()


# CodeReview: Why the inject here
@inject
def _install_k3s(
    executor: ManageK3SInstallation = Provide[Application.services.manage_k3s_installation],  # type: ignore
) -> int:
    log_full_host_header()
    executor.install_k3s()
    return 0


@click.command(name="uninstall", short_help="uninstall k3s")
@click_config_file.configuration_option(implicit=False)
@click.pass_context
@inject
def uninstall_k3s(
    ctx: Context,
    config: providers.Configuration = Provider[Application.config],  # type: ignore
    **kwargs,  # pylint: disable=unused-argument  # noqa: ANN003
) -> int:
    """cli entrypoint."""
    config.from_dict(ctx.params)
    configure_application_container(config, "manage_k3s")
    return _uninstall_k3s()


def _uninstall_k3s(
    executor: ManageK3SInstallation = Provide[Application.services.manage_k3s_installation],  # type: ignore
) -> int:
    logger.info("started")
    log_full_host_header()
    executor.uninstall_k3s()
    logger.info("ended")
    return 0
