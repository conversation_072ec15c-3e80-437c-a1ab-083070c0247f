---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: workflow-executor
rules:
  - apiGroups:
      - argoproj.io
    resources:
      - workflowtaskresults
      - workflows
    verbs:
      - "*"
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: workflow-executor
imagePullSecrets:
  - name: reg-cred
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: workflow-executor
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: workflow-executor
subjects:
  - kind: ServiceAccount
    name: workflow-executor
