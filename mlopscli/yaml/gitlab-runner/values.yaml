# Gitlab Runner token used to connect to the Gitlab instance
runnerToken: ""
gitlabUrl: https://gitlab.thalesdigital.io/

# Max number of concurrent pods to run
concurrent: 4

# Permissions to give to the service account
rbac:
  create: true
  rules:
    - resources: ["events"]
      verbs: ["list", "watch"]
    - resources: ["namespaces"]
      verbs: ["create", "delete"]
    - resources: ["pods"]
      verbs: ["create","delete","get"]
    - apiGroups: [""]
      resources: ["pods/attach","pods/exec"]
      verbs: ["get","create","patch","delete"]
    - apiGroups: [""]
      resources: ["pods/log"]
      verbs: ["get","list"]
    - resources: ["secrets"]
      verbs: ["create","delete","get","update"]
    - resources: ["serviceaccounts"]
      verbs: ["get"]
    - resources: ["services"]
      verbs: ["create","get"]

## Specifies whether a ServiceAccount should be created
serviceAccount:
  create: true
  name: "gitlab-runner-dast-sa"

  config: |
    [[runners]]
      [runners.kubernetes]
        namespace = "{{.Release.Namespace}}"
        image = "alpine"
