{"id": null, "uid": null, "title": "MLOps toolchain OpenTelemetry ClickHouse Traces", "tags": ["otel", "clickhouse", "tracing", "MLOps toolchain", "poly-dashboard"], "timezone": "browser", "schemaVersion": 37, "version": 1, "refresh": "30s", "panels": [{"type": "timeseries", "title": "Requests per Second", "datasource": "grafana-clickhouse-datasource", "targets": [{"queryType": "sql", "rawSql": "SELECT\n  toStartOfInterval(Timestamp, INTERVAL 1 minute) AS time,\n  ServiceName,\n  count() AS requests\nFROM mlops_otel.mlobs_otel_traces\nGROUP BY time, ServiceName\nORDER BY time", "refId": "A"}], "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"x": 0, "y": 0, "w": 12, "h": 8}}, {"type": "barchart", "title": "Top Services by Requests", "datasource": "grafana-clickhouse-datasource", "targets": [{"queryType": "sql", "rawSql": "SELECT\n  ServiceName,\n  count() AS total_requests\nFROM mlops_otel.mlobs_otel_traces\nGROUP BY ServiceName\nORDER BY total_requests DESC\nLIMIT 10", "refId": "B"}], "gridPos": {"x": 12, "y": 0, "w": 12, "h": 8}}, {"type": "table", "title": "Error Rate by Service", "datasource": "grafana-clickhouse-datasource", "targets": [{"queryType": "sql", "rawSql": "SELECT\n  ServiceName,\n  sum(CASE WHEN StatusCode != 'Unset' THEN 1 ELSE 0 END) * 100.0 / count() AS error_rate\nFROM mlops_otel.mlobs_otel_traces\nGROUP BY ServiceName", "refId": "C"}], "gridPos": {"x": 0, "y": 8, "w": 12, "h": 6}}, {"type": "table", "title": "Average Span Duration", "datasource": "grafana-clickhouse-datasource", "targets": [{"queryType": "sql", "rawSql": "SELECT\n  ServiceName,\n  avg(Duration) AS avg_duration_ms\nFROM mlops_otel.mlobs_otel_traces\nGROUP BY ServiceName\nORDER BY avg_duration_ms DESC", "refId": "D"}], "gridPos": {"x": 12, "y": 8, "w": 12, "h": 6}}, {"type": "heatmap", "title": "Latency Heatmap", "datasource": "grafana-clickhouse-datasource", "targets": [{"queryType": "sql", "rawSql": "SELECT\n  toStartOfInterval(Timestamp, INTERVAL 1 minute) AS time,\n  Duration\nFROM mlops_otel.mlobs_otel_traces\nWHERE ServiceName = 'polycore'", "refId": "E"}], "gridPos": {"x": 0, "y": 14, "w": 12, "h": 8}}, {"type": "table", "title": "Trace Explorer", "datasource": "grafana-clickhouse-datasource", "targets": [{"queryType": "sql", "rawSql": "SELECT\n  Timestamp,\n  TraceId,\n  SpanId,\n  ParentSpanId,\n  SpanName,\n  Duration,\n  ServiceName,\n  StatusCode\nFROM mlops_otel.mlobs_otel_traces\nORDER BY Timestamp ASC\nLIMIT 100", "refId": "F"}], "gridPos": {"x": 12, "y": 14, "w": 12, "h": 10}}, {"type": "piechart", "title": "SpanKind Distribution", "datasource": "grafana-clickhouse-datasource", "targets": [{"queryType": "sql", "rawSql": "SELECT\n  SpanKind,\n  count() AS count\nFROM mlops_otel.mlobs_otel_traces\nGROUP BY SpanKind\nORDER BY count DESC", "refId": "G"}], "gridPos": {"x": 0, "y": 22, "w": 12, "h": 8}}, {"type": "table", "title": "Slowest Spans", "datasource": "grafana-clickhouse-datasource", "targets": [{"queryType": "sql", "rawSql": "SELECT\n  SpanName,\n  Duration,\n  ServiceName,\n  Timestamp\nFROM mlops_otel.mlobs_otel_traces\nORDER BY Timestamp ASC, Duration DESC\nLIMIT 10", "refId": "H"}], "gridPos": {"x": 12, "y": 24, "w": 12, "h": 8}}, {"type": "timeseries", "title": "Trace Count over Time", "datasource": "grafana-clickhouse-datasource", "targets": [{"queryType": "sql", "rawSql": "SELECT\n  toStartOfInterval(Timestamp, INTERVAL 5 minute) AS time,\n  countDistinct(TraceId) AS trace_count\nFROM mlops_otel.mlobs_otel_traces\nGROUP BY time\nORDER BY time", "refId": "I"}], "gridPos": {"x": 0, "y": 30, "w": 24, "h": 8}}], "templating": {"list": []}, "annotations": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["30s", "1m", "5m", "15m", "1h", "6h", "12h", "24h"]}}