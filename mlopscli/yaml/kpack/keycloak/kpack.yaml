---
components:
  - name: keycloak
    version: 1.7.9+2.3.0
  - name: keycloak-realm
    version: 1.6.4

# Note keycloak helm doesn't pull docker image from artifactory

global:
  sso:
    enabled: true
    issuer:
      path: /auth/realms/kast/protocol/openid-connect/

keycloak-realm:
  _install:
    flags:
      timeout: "15m"
  _values:
    createSecurityGroupsOnMasterRealm: true

keycloak:
  user: admin
  ingress:
    enabled: true
    appRoot: /auth
  cert:
    secretName: "keycloak-tls-secret"
  _values:  # configuration injected at chart level
    keycloakx:
      ingress:
        annotations:
          cert-manager.io/cluster-issuer: external-ca-issuer
        tls:
          - hosts:
            secretName: "keycloak-tls-secret"
      resources:
        requests:
          memory: "1300Mi"
          cpu: "200m"
        limits:
          memory: "1300Mi"
          cpu: "600m"
