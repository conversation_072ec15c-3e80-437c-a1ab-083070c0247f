---
components:
  - name: mlflow
    version: 1.1.0
    namespace: processing

global:
  domain: "dpsc"
mlflow:
  s3:
    enabled: true
    host: <minio-host>
    secretKey: <minio-secret-key>
    accessKey: <minio-access-key>
  postgres:
    enabled: true
    password: <postgres-password>
    passwordUrlEncoded: <postgres-password>
  _values:
    ingress:
      enabled: true
      annotations:
        cert-manager.io/cluster-issuer: external-ca-issuer
      hosts:
        - "mlflow-processing.dpsc"
        - "mlflow.processing"
        - "mlflow.processing.svc.cluster.local"
      tls:
        - hosts:
            - "mlflow-processing.dpsc"
            - "mlflow.processing"
            - "mlflow.processing.svc.cluster.local"
          secretName: "mlflow-tls-secret"
    extraVolumeMounts:
      - mountPath: /etc/ssl/certs
        name: ca-certificate-only
        readOnly: true
    extraVolumes:
      - configMap:
          defaultMode: 420
          items:
            - key: ca-certificates.crt
              path: ca-certificates.crt
          name: mlops-ca-bundle
          optional: false
        name: ca-certificate-only
    extraEnv:
      - name: AWS_CA_BUNDLE
        value: /etc/ssl/certs/ca-certificates.crt
