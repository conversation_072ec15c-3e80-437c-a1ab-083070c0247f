---
components:
  - name: argoworkflows
    version: 4.1.1+0.42.7
    namespace: processing

global:
  domain: "dpsc"

argoworkflows:
  archive:
    enabled: true
  server:
    ingress:
      enabled: true
  s3:
    endpoint: s3.object-store:9000
  _values:
    argo-workflows:
      artifactRepository:
        s3:
          insecure: false
          caSecret:
            name: intermediate-ca
            key: intermediate-ca-cert.pem
      server:
        ingress:
          annotations:
            cert-manager.io/cluster-issuer: external-ca-issuer
          hosts:
            - "argo.dpsc"
            - "argo-processing-server.processing"
            - "argo-processing-server.processing.svc.cluster.local"
          tls:
            - hosts:
                - "argo.dpsc"
                - "argo-processing-server.processing"
                - "argo-processing-server.processing.svc.cluster.local"
              secretName: "argo-tls-secret"
