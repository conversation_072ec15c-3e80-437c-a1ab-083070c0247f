---
components:
  - name: certmanager
    version: 1.4.1+1.12.3
    namespace: cert-manager
# certmanager:
# No need imagePullSecrets since this cert-manager helm doesn't pull docker image from artifactory
#   _values:
#     cert-manager:
#       global:
#         imagePullSecrets:
#           - name: "reg-cred"


# NOTE
# WRN coalesce.go:286: warning: cannot overwrite table with non table for certmanager.cert-manager.startupapicheck.securityContext (map[runAsNonRoot:true seccompProfile:map[type:RuntimeDefault]])
# This error is due to a workaround made by KAST team
# https://artifactory.thalesdigital.io/ui/repos/tree/General/private-helm-kast/certmanager/certmanager-1.4.1+1.12.3.tgz/certmanager/values.yaml
# # Workaround in order to remove the field 'securityContext.seccompProfile' as kustomize is not able to override this resource.
# # As a limitation, the field 'securityContext.runAsNonRoot' is not defined either but remains set at the container level.
# # issue: https://github.com/helm/helm/issues/7891
# # securityContext: *sec_context
# securityContext: ""
