---
components:
  - name: minio
    version: 6.8.0+5.4.0
    namespace: object-store

global:
  domain: "dpsc"
minio:
  api:
    tls: true
  console:
    enabled: true
    tls: true
  internaltls:
    enabled: true
  _values:
    healthCheck:
      enabled: false  # KAST kpack doesn't support extravolume to mount self-signed certs which cause post-job- to timeout on linux
    minio:
      ingress:
        enabled: true
        annotations:
          cert-manager.io/cluster-issuer: external-ca-issuer
        hosts:
          - "minio.dpsc"
          - "s3.object-store"
          - "s3.object-store.svc.cluster.local"
        tls:
          - hosts:
              - "minio.dpsc"
              - "s3.object-store"
              - "s3.object-store.svc.cluster.local"
            secretName: "minio-tls-secret"
      consoleIngress:
        enabled: true
        annotations:
          cert-manager.io/cluster-issuer: external-ca-issuer
        hosts:
          - "minio-console.dpsc"
        tls:
          - hosts:
              - "minio-console.dpsc"
            secretName: "minio-console-tls-secret"
      serviceAccount:
        create: false
      tls:
        enabled: true
        certSecret: s3-internal-tls
        publicCrt: tls.crt
      users:
        - accessKey: <minio-access-key>
          secretKey: <minio-secret-key>
          policy: consoleAdmin
      postJob:
        securityContext:
          enabled: false
