---
components:
  - name: clickhouse
    version: 3.5.0
    namespace: olap-store

clickhouse:
  backup:
    enabled: false
  _values:
    backup:
      logLevel: trace
      resources:
        requests:
          cpu: 100m
          memory: 32Mi
    resources:
      requests:
        cpu: 100m
        memory: 32Mi
    ingress:
      enabled: true
      annotations:
        cert-manager.io/cluster-issuer: external-ca-issuer
      hosts:
        - "clickhouse.dpsc"
        - "clickhouse.olap-store"
        - "clickhouse.olap-store.svc.cluster.local"
      tls:
        - hosts:
            - "clickhouse.dpsc"
            - "clickhouse.olap-store"
            - "clickhouse.olap-store.svc.cluster.local"
          secretName: "clickhouse-tls-secret"
