---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: sleep-auto
  name: sleep-auto
  namespace: default
spec:
  replicas: 1
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app: sleep-auto
  template:
    metadata:
      labels:
        app: sleep-auto
    spec:
      containers:
        - command:
            - /bin/sh
            - -c
            - sleep 1d
          image: quay.io/zenlab/curl:latest
          name: curl
          volumeMounts:
            - mountPath: /etc/ssl/certs/
              name: ca-certificate-only
              readOnly: true
      volumes:
        - name: ca-certificate-only
          configMap:
            name: mlops-ca-bundle
            defaultMode: 0644
            optional: false
            items:
              - key: ca-certificates.crt
                path: ca-certificates.crt
