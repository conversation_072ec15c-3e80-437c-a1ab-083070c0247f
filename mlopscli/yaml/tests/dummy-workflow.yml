---
apiVersion: argoproj.io/v1alpha1
kind: Workflow
metadata:
  generateName: dummy-workflow-
  namespace: default
spec:
  entrypoint: dummy-workflow
  templates:
    - name: dummy-workflow
      steps:
        - - name: step1
            template: dummy-step

          - name: step2
            template: dummy-step

    - name: dummy-step
      serviceAccountName: workflow-executor
      script:
        image: zot.dpsc/python:3.8
        command: [python]
        source: |
          print("This is a dummy step.")

# how to push image in zot
# docker run --rm --platform linux/aarch64 -v /var/run/docker.sock:/var/run/docker.sock -v /etc/hosts:/etc/hosts quay.io/containers/skopeo:latest copy --dest-tls-verify=false --format=oci docker://python:3.8 docker://zot.dpsc/python:3.8
