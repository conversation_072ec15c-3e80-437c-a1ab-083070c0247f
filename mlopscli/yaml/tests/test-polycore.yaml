---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: sleep-auto
  name: sleep-auto
  namespace: default
spec:
  replicas: 1
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app: sleep-auto
  template:
    metadata:
      labels:
        app: sleep-auto
    spec:
      imagePullSecrets:
        - name: reg-cred
      containers:
        - command:
            - /bin/sh
            - -c
            - sleep 1d
          image: artifactory.thalesdigital.io/docker-internal/mlops-toolchain/polycore:develop-b22aa1c834a9d08b05826e7d2a3085f60bc6801d
          name: curl
          volumeMounts:
            - mountPath: /etc/ssl/certs/
              name: ca-certificate-only
              readOnly: true
      volumes:
        - name: ca-certificate-only
          configMap:
            name: mlops-ca-bundle
            defaultMode: 0644
            optional: false
            items:
              - key: ca-certificates.crt
                path: ca-certificates.crt
