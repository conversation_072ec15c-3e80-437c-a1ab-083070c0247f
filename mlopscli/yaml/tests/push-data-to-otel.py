# <PERSON>lick<PERSON> commands
#
# SHOW DATABASES
# SHOW TABLES FROM mlops_otel;
# SELECT * FROM mlops_otel.mlobs_otel_traces;
# SELECT * FROM mlops_otel.mlobs_otel_logs;
#
# pip install opentelemetry-api opentelemetry-sdk opentelemetry-exporter-otlp
#
# run:
# python mlopscli/yaml/tests/push-data-to-otel.py
#
import os

from grpc import ssl_channel_credentials
from opentelemetry import trace
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor

# Set up a tracer provider
tracer_provider = TracerProvider()
trace.set_tracer_provider(tracer_provider)

cacert = "~/.cache/mlopscli/k3s_installation/certs/intermediate-ca/output/intermediate-ca-bundle.pem"
cacert = os.path.expanduser(cacert)


# Create SSL channel credentials
with open(cacert, "rb") as f:
    ca_cert = f.read()
credentials = ssl_channel_credentials(root_certificates=ca_cert)

# Set up OTLP exporter
otlp_exporter = OTLPSpanExporter(endpoint="localhost:4317", credentials=credentials)

# Set up batch span processor
tracer_provider.add_span_processor(BatchSpanProcessor(otlp_exporter))

# Get a tracer
tracer = trace.get_tracer(__name__)

# Create and send a span
with tracer.start_as_current_span("example-span") as span:
    span.set_attribute("example.attribute", "value")
