apiVersion: opentelemetry.io/v1alpha1
kind: OpenTelemetryCollector
metadata:
  name: inference-otel-sidecar
spec:
  mode: sidecar
  config: |
    receivers:
      prometheus:
        config:
          scrape_configs:
            - job_name: otel-collector
              scrape_interval: 5s
              static_configs:
                - targets: [localhost:8080]
    processors:

    exporters:
      otlp:
        endpoint: OTEL_SERVICE_NAME.OTEL_NAMESPACE.svc.cluster.local:4317
        tls:
          insecure_skip_verify: true
    service:
      pipelines:
        metrics:
          receivers: [prometheus]
          processors: []
          exporters: [otlp]
