#!/usr/bin/env bash

# Strict mode
set -euo pipefail
IFS=$'\n\t'

# Determine script's base directory (so it can find certs/)
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly CERTS_CONFIG_DIR="${SCRIPT_DIR}/certs"

readonly LOG_FILE="/tmp/pki-scaffold.log"
readonly GREEN='\033[0;32m'
readonly RED='\033[0;31m'
readonly YELLOW='\033[1;33m'
readonly NC='\033[0m'

# Functions
log() {
    local level=$1
    shift
    local message=$*
    local timestamp
    timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "${LOG_FILE}"
}

info() { log "INFO" "$*"; }
error() { log "ERROR" "${RED}$*${NC}"; }
warn() { log "WARN" "${YELLOW}$*${NC}"; }
success() { log "SUCCESS" "${GREEN}$*${NC}"; }

cleanup() {
    local exit_code=$?
    if [ $exit_code -ne 0 ]; then
        error "Script failed with exit code: $exit_code"
        error "Check ${LOG_FILE} for details"
    fi
    # Secure cleanup of temporary files
    if [[ -n ${TEMP_DIR:-} ]]; then
        rm -rf "${TEMP_DIR}"
    fi
}

check_prerequisites() {
    info "Checking prerequisites..."

    local required_commands=("cfssl" "cfssljson" "openssl")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            error "Required command not found: $cmd"
            exit 1
        fi
    done

    # Verify required files exist
    local required_files=(
        "${CERTS_CONFIG_DIR}/profiles.json"
        "${CERTS_CONFIG_DIR}/root-ca-csr.json"
        "${CERTS_CONFIG_DIR}/intermediate-ca-csr.json"
    )

    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            error "Required file not found: $file"
            exit 1
        fi
    done
}

setup_directories() {
    info "Setting up directory structure..."

    for dir in "${CERT_DIRS[@]}"; do
        mkdir -p "$dir"
        chmod 700 "$dir"
    done
}

generate_root_ca() {
    info "Generating Root CA..."

    cfssl gencert -initca "$CERTS_CONFIG_DIR/root-ca-csr.json" | cfssljson -bare "$OUTPUT_BASE_DIR/certs/root-ca/output/root-ca"
    mv "$OUTPUT_BASE_DIR/certs/root-ca/output/root-ca.pem" "$OUTPUT_BASE_DIR/certs/root-ca/output/root-ca-cert.pem"
    cp "$OUTPUT_BASE_DIR/certs/root-ca/output/root-ca-cert.pem" "$OUTPUT_BASE_DIR/certs/root-ca/output/root-ca-bundle.pem"

    # Validate root CA
    if ! openssl x509 -in "$OUTPUT_BASE_DIR/certs/root-ca/output/root-ca-cert.pem" -noout -text > /dev/null 2>&1; then
        error "Root CA validation failed"
        exit 1
    fi
    success "Root CA generated successfully"
}

generate_intermediate_ca() {
    info "Generating Intermediate CA..."

    cfssl gencert \
        -ca="$OUTPUT_BASE_DIR/certs/root-ca/output/root-ca-cert.pem" \
        -ca-key="$OUTPUT_BASE_DIR/certs/root-ca/output/root-ca-key.pem" \
        -config="$CERTS_CONFIG_DIR/profiles.json" \
        -profile=intermediate \
        "$CERTS_CONFIG_DIR/intermediate-ca-csr.json" | cfssljson -bare "$OUTPUT_BASE_DIR/certs/intermediate-ca/output/intermediate-ca"

    mv "$OUTPUT_BASE_DIR/certs/intermediate-ca/output/intermediate-ca.pem" "$OUTPUT_BASE_DIR/certs/intermediate-ca/output/intermediate-ca-cert.pem"
    cat "$OUTPUT_BASE_DIR/certs/intermediate-ca/output/intermediate-ca-cert.pem" "$OUTPUT_BASE_DIR/certs/root-ca/output/root-ca-cert.pem" > "$OUTPUT_BASE_DIR/certs/intermediate-ca/output/intermediate-ca-bundle.pem"

    # Validate intermediate CA
    if ! openssl verify -CAfile "$OUTPUT_BASE_DIR/certs/root-ca/output/root-ca-cert.pem" "$OUTPUT_BASE_DIR/certs/intermediate-ca/output/intermediate-ca-cert.pem" > /dev/null 2>&1; then
        error "Intermediate CA validation failed"
        exit 1
    fi
    success "Intermediate CA generated successfully"
}

set_permissions() {
    info "Setting secure permissions..."

    # Set restrictive permissions on private keys
    find . -name "*-key.pem" -exec chmod 600 {} \;
    # Set read-only permissions on certificates
    find . -name "*-cert.pem" -exec chmod 644 {} \;
    find . -name "*-bundle.pem" -exec chmod 644 {} \;
}

main() {
    # Initialize
    trap cleanup EXIT
    TEMP_DIR=$(mktemp -d)

    # Parse arguments
    OUTPUT_BASE_DIR="${1:-${SCRIPT_DIR}}"

    info "Output directory is ($OUTPUT_BASE_DIR)"

    # Rewrite CERT_DIRS using the argument
    CERT_DIRS=(
        "${OUTPUT_BASE_DIR}/certs/root-ca/output"
        "${OUTPUT_BASE_DIR}/certs/intermediate-ca/output"
    )

    # Start logging
    info "Starting PKI scaffold process..."

    # Execute steps
    check_prerequisites
    setup_directories
    generate_root_ca
    generate_intermediate_ca
    set_permissions

    success "PKI scaffold completed successfully!"
    info "Certificate bundles available at:"
    for dir in "${CERT_DIRS[@]}"; do
        if [[ -d "$dir" ]]; then
            echo "  - $dir"
        fi
    done
}

# Execute main function
main "$@"
