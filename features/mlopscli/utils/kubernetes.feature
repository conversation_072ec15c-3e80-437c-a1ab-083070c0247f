@current
Feature: Restart Deployment

Scenario: Restarting a valid deployment
  Given a deployment named "my-deployment" in namespace "default"
  When the restart_deployment function is called
  Then the deployment is restarted successfully
  And the logger logged a DEBUG message: Deployment 'my-deployment' in namespace 'default' restarted successfully

Scenario: Attempting to restart a nonexistent deployment
  Given a deployment named "nonexistent-deployment" in namespace "default"
  When the restart_deployment function is called with a nonexistent deployment
  Then the logger logged a ERROR message: Deployment 'nonexistent-deployment' not found in namespace 'default'

Scenario: Attempting to restart a deployment but error occured
  Given a deployment named "my-deployment" in namespace "default"
  When the restart_deployment function is called with a exsting deployment but error occured
  Then the logger logged a ERROR message: Error occurred while trying to restart deployment:

Scenario: Validate presence of an image pull secret in a service account
  Given a service account named "my-service-account" in namespace "default"
  And it has an image pull secret named "my-secret"
  When I check if the image pull secret exists
  Then it should return true

Scenario: Validate absence of an image pull secret in a service account
  Given a service account named "my-service-account" in namespace "default"
  And it has no image pull secrets
  When I check if the image pull secret exists
  Then it should return false

Scenario: Handle non-existent service account
  Given a non existiant service account named "invalid-service-account" in namespace "default"
  When I check if the image pull secret exists for non-existent service account
  Then it should return false
  And the logger logged a ERROR message: Exception when calling CoreV1Api->read_namespaced_service_account


Scenario: Validate image pull secret does not exist in a service account
  Given a service account named "my-service-account" in namespace "default"
  And it has an image pull secret named "different-secret"
  When I check if the image pull secret "my-secret" exists
  Then it should return false


Scenario: Create a new secret
  Given the secret "my_secret" does not exist in namespace "default"
  When I create the secret "my_secret" with data {"password": "my_password"} in namespace "default"
  Then the secret "my_secret" should be created in namespace "default"

Scenario: Force create an existing secret
  Given the secret "my_secret" exists in namespace "default"
  When I create the secret "my_secret" with data {"password": "my_new_password"} in namespace "default" with force create
  Then the secret "my_secret" should be updated in namespace "default"

Scenario: Fail to create a secret if an API exception is raised
  Given the secret "my_secret" does not exist in namespace "default"
  When an error occurs while creating the secret "my_secret" with data {"password": "my_password"} in namespace "default"
  Then the logger logged a ERROR message: Failed to create secret

Scenario: Create a new Docker Registry secret successfully
  Given I have a Docker Registry secret named "my-registry-secret"
  And the server is "my.registry.com"
  And the username is "my-user"
  And the token is "my-token"
  And the namespace is "default"
  When I create the Docker Registry secret
  Then the docker registry secret "my-registry-secret" should be created in namespace "default"
  And the result is True

Scenario: Update an existing Docker Registry secret when force_create is true
  Given I have a Docker Registry secret named "my-registry-secret"
  And the server is "my.registry.com"
  And the username is "my-user"
  And the token is "my-token"
  And the namespace is "default"
  When I create the Docker Registry secret with force_create set to true
  Then the docker registry secret "my-registry-secret" should be updated in namespace "default"
  And the result is True

Scenario: Fail to create a docker registry secret if an API exception is raised
  Given I have a Docker Registry secret named "my-registry-secret"
  And the server is "my.registry.com"
  And the username is "my-user"
  And the token is "my-token"
  And the namespace is "default"
  When an error occurs while creating the docker registry secret
  Then the logger logged a ERROR message: Failed to create secret
  And the result is False

Scenario: Create a Docker Registry secret but always exist
  Given I have a Docker Registry secret named "my-registry-secret"
  And the server is "my.registry.com"
  And the username is "my-user"
  And the token is "my-token"
  And the namespace is "default"
  When I create the Docker Registry secret but already exist
  Then the result is True

Scenario: Successfully delete a pod
  Given the pod "test-pod" exists in namespace "default"
  When I delete the pod "test-pod" from namespace "default"
  Then delete_namespaced_pod has been called

Scenario: Pod not found
  Given the pod "non-existing-pod" does not exist in namespace "default"
  When I delete the pod "non-existing-pod" from namespace "default"
  Then delete_namespaced_pod has been called
  And the logger logged a ERROR message: Pod 'non-existing-pod' not found in namespace

Scenario: Pod generic exception
  Given a bad behaviour happen for the pod "non-existing-pod" does not exist in namespace "default"
  When I delete the pod "non-existing-pod" from namespace "default"
  Then delete_namespaced_pod has been called
  And the logger logged a ERROR message: Error occurred while trying to delete pod 'non-existing-pod'


Scenario: Successful download of images on all nodes
  Given I have the registry secret "<secret_name>" in namespace "<namespace>"
  When I force download the images "<images>" from the registry
  Then all images should be downloaded successfully on the nodes

Scenario: Fail to download of images on all nodes
  Given generic exception occured during retrieving nodes with registry secret "<secret_name>" in namespace "<namespace>"
  When I force download the images "<images>" from the registry
  Then the logger logged a ERROR message: Exception while downloading images

Scenario: Successfully create a pod with specified parameters
  Given I have the pod name "<pod_name>"
  And I have the image "<image>"
  And a registry secret "<registry_secret_name>"
  And I have the namespace "<namespace>"
  And I want to assign the pod to node "<node_name>"
  When I create the pod
  Then the pod should be created successfully

Scenario: Fails to create a pod when an API exception is raised
  Given I have the pod name "<pod_name>"
  And I have the image "<image>"
  And a registry secret "<registry_secret_name>"
  And I have the namespace "<namespace>"
  And I want to assign the pod to node "<node_name>"
  When I create the pod and an error occurs
  Then the logger logged a ERROR message: Exception when creating pods

Scenario: Successfully wait for a pod to reach Running status
  Given I have the namespace "<namespace>"
  And I have the pod name "<pod_name>"
  When I wait for the pod to run
  Then the pod should be running

Scenario: Fail to wait for the pod due to timeout
  Given I have the namespace "<namespace>"
  And I have the pod name "<pod_name>"
  When I wait for the pod to run timeout
  Then a TimeoutError should be raised

Scenario: Fail to wait for the pod due to non-running status (Succeeded/Failed)
  Given I have the namespace "<namespace>"
  And I have the pod name "<pod_name>"
  When I wait for the pod to run failed
  Then an Exception should be raised indicating the pod is not running

Scenario: The helm chart is installed
  Given a helm chart with name "my-app" exists
  Then the answer is "True"

Scenario: The helm chart is not installed
  Given a helm chart with name "non-existent-app" does not exist
  Then the answer is "False"

Scenario: The specified helm repo is installed
  Given a helm repo named "zot" exists
  When I check if the repo "zot" is installed
  Then it's "True" that it's installed

Scenario: The specified helm repo isn't installed, but others are
  Given a helm repo named "lakeFS" exists
  And a helm repo named "kast" exists
  When I check if the repo "bruh" is installed
  Then it's "False" that it's installed

Scenario: There is no helm repo installed
  Given no installed helm repo
  When I check if the repo "I don't exist" is installed
  Then it's "False" that it's installed

Scenario: Uninstalling an installed helm repo
  Given a helm repo named "test" exists
  When I uninstall the "test" repo
  Then it calls the "uninstall" command

Scenario: Uninstalling an inexisting helm repo
  Given no installed helm repo
  When I uninstall the "test" repo
  Then it doesn't call the "uninstall" command
