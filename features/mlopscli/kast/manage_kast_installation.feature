Feature: manage_k3s_installation

Scenario: I check requirements
  When I check requirements
  Then everything is fine

# Scenario: Print etc hosts information
#   When I print etc host info
#   Then I should see the expected output "lakefs.dpsc"
#   And I should see the expected output "zot.dpsc"
#   And everything is fine

Sc<PERSON><PERSON>: I fill env
  When I fill env
  Then env contain "ANSIBLE_ROLES_PATH"
  And everything is fine

# Scenario: I generate hosts file
#   When I generate hosts file with artifactory username "<EMAIL>"
#   Then file content "<EMAIL>"
#   And everything is fine

Scenario: The component is installed
  Given a Kubernetes component of type "deployment" with name "my-app" in namespace "default" exists
  Then the answer is "True"

Scenario: The component is installed
  Given a Kubernetes component of type "deployment" with name "my-app" without namespace scope exists
  Then the answer is "True"

Scenario: The component is not installed
  Given a Kubernetes component of type "deployment" with name "non-existent-app" in namespace "default" does not exist
  Then the answer is "False"

Scenario: Handling PolyDB with an unsupported architecture
  Given an unsupported architecture
  When I try to handle PolyDB's setup
  Then I get an unsupported arch error

Scenario: Running PolyDB pre runs with missing binaries
  Given missing dbtool binaries
  When I run PolyDB run method
  Then the script stops with exit code 1

Scenario: Running PolyDB pre runs successfully
  Given dbtool's binaries are installed
  And a valid postgresql password
  When I run PolyDB's pre runs
  Then no exceptions happen
  And it calls the "create_postgres_database" command
  And it calls the "port_forward_thread.start" command
  And it passed "5" to the "time.sleep" command

Scenario: Running PolyDB executable with a wrong postgresql password
  Given an invalid postgresql password
  And x86_64 as my architecture
  When I run PolyDB run method
  Then the script stops with exit code 1

Scenario: I execute PolyDB post run
  When I run PolyDB post run method
  Then no exceptions happen
  And it calls the "port_forward_thread.stop" command
  And it calls the "port_forward_thread.join" command

Scenario: Local installation location
  Given the installation location is "local"
  And x86_64 as my architecture
  When calling _host_file_per_location with the installation location
  Then the returned hosts file should be "hosts_local.yaml" and architecture should be "x86_64"

Scenario: Remote installation location
  Given the installation location is "remote"
  And x86_64 as my architecture
  When calling _host_file_per_location with the installation location
  Then the returned hosts file should be "hosts_remote.yaml" and architecture should be "x86_64"

Scenario: Unsupported installation location
  Given the installation location is "unknown"
  When calling _host_file_per_location with the installation location
  Then an exception of type UnsupportedInstallationLocationException should be raised

Scenario: Install SSH key on ARM64 architecture
  Given the machine architecture is ARM64
  And the hostname is "valid_hostname"
  When installing the SSH key
  Then the SSH key is generated if it doesn't exist
  And it is transferred to the ARM64 machine
  And the authorized_keys file is updated

Scenario: Install SSH key on AMD64 architecture
  Given the machine architecture is AMD64
  And the hostname is "valid_hostname"
  When installing the SSH key
  Then the SSH key is generated if it doesn't exist
  And it is appended to the authorized_keys file on the host machine

Scenario: Unsupported architecture
  Given the machine architecture is "unsupported_architecture"
  And the hostname is "valid_hostname"
  When installing the SSH key
  Then an UnsupportedArchitectureException is raised
