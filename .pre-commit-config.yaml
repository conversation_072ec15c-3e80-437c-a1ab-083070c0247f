repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0 # Get the latest from: https://github.com/pre-commit/pre-commit-hooks/releases
    hooks:
    -   id: end-of-file-fixer
        name: end-of-file-fixer (all files)
    -   id: trailing-whitespace
        name: trailing-whitespace (all files)
- repo: https://github.com/astral-sh/ruff-pre-commit
  # Ruff version.
  rev: v0.8.6
  hooks:
    # Run the formatter.
    - id: ruff-format
      name: ruff format
    - id: ruff
      name: ruff sort imports
      args: ["check", "--select", "I", "--fix"]
    # Run the linter.
    - id: ruff
      name: ruff run linter
