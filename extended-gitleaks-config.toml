[extend]
# Extends default packaged ruleset, NOTE: do not change the path.
path = "/gitleaks.toml"

[allowlist]
    description = "ignore #nosec lines, ignore old commit with no #nosec, old postgres string, first test with hardcoded values"
    regexTarget = "line"
    regexes = ['''#nosec''', '''postgresql://postgres:{postgresql_password}''', '''postgres://{postgresql_username}:{postgresql_password}''', '''postgres://someones-mother:reallyObvious''', '''AKCp8ihpJQjx7NUoMFmk5PW4QFLugFCzJu56Dfbr9WZTsZbSywy8T3LFXG7Jo7RuKWyo4dE2N''']
